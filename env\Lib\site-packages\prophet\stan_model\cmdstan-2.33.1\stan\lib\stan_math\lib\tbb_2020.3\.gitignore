# Ignore the debug and release directories created with <PERSON><PERSON><PERSON> builds #
#########################################################################
build/*_debug/
build/*_release/

# Compiled source #
###################
*.com
*.class
*.dll
*.lib
*.pdb
*.exe
*.o
*.so
*.so.1
*.so.2
*.dylib
*.a
*.obj
*.pyc

*.orig
*.raw
*.sample
*.slo
*.swp
*.config
*.la
*.lai
*.lo
*.nhdr
*.nii.gz
*.nrrd

# Packages #
############
# it's better to unpack these files and commit the raw source
# git has its own built in compression methods
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.tgz
*.zip

# Logs and databases #
######################
*.log
*.sql
*.sqlite

# OS generated files #
######################
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE generated files #
######################
/.ninja_deps
/.ninja_log
/build.ninja
/rules.ninja
*~
.emacs.desktop
.tags

# Build system generated files #
################################
CMakeCache.txt
CMakeFiles/
cmake/TBBConfig.cmake
cmake/TBBConfigVersion.cmake

# Other #
#########
.clang_complete
.idea
.svn
crash*
*.tmp
/.vs
