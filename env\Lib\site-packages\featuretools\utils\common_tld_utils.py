# put longer TLDs first to avoid catching a small part of a longer TLD and escape periods
COMMON_TLDS = [
    "management",
    "technology",
    "solutions",
    "delivery",
    "services",
    "software",
    "digital",
    "finance",
    "monster",
    "network",
    "support",
    "systems",
    "website",
    "agency",
    "design",
    "events",
    "global",
    "health",
    "online",
    "stream",
    "studio",
    "travel",
    "apple",
    "click",
    "cloud",
    "email",
    "games",
    "group",
    "media",
    "ninja",
    "press",
    "rocks",
    "space",
    "store",
    "today",
    "tools",
    "video",
    "works",
    "world",
    "aero",
    "arpa",
    "asia",
    "bank",
    "best",
    "blog",
    "buzz",
    "care",
    "casa",
    "chat",
    "club",
    "coop",
    "cyou",
    "desi",
    "farm",
    "goog",
    "guru",
    "host",
    "info",
    "jobs",
    "life",
    "link",
    "live",
    "mobi",
    "name",
    "news",
    "page",
    "plus",
    "shop",
    "site",
    "team",
    "tech",
    "work",
    "zone",
    "app",
    "aws",
    "bid",
    "biz",
    "box",
    "cam",
    "cat",
    "com",
    "dev",
    "edu",
    "eus",
    "fun",
    "gov",
    "icu",
    "int",
    "ltd",
    "mil",
    "net",
    "nyc",
    "one",
    "onl",
    "org",
    "ovh",
    "pro",
    "pub",
    "run",
    "sap",
    "top",
    "vip",
    "win",
    "xxx",
    "xyz",
    "ac",
    "ad",
    "ae",
    "ag",
    "ai",
    "al",
    "am",
    "ar",
    "at",
    "au",
    "az",
    "ba",
    "bd",
    "be",
    "bg",
    "br",
    "by",
    "bz",
    "ca",
    "cc",
    "cf",
    "ch",
    "cl",
    "cm",
    "cn",
    "co",
    "cr",
    "cu",
    "cx",
    "cy",
    "cz",
    "de",
    "dk",
    "do",
    "ec",
    "ee",
    "eg",
    "es",
    "eu",
    "fi",
    "fm",
    "fr",
    "ga",
    "ge",
    "gg",
    "gl",
    "gq",
    "gr",
    "gs",
    "gt",
    "hk",
    "hn",
    "hr",
    "hu",
    "id",
    "ie",
    "il",
    "im",
    "in",
    "io",
    "ir",
    "is",
    "it",
    "jo",
    "jp",
    "ke",
    "kh",
    "ki",
    "kr",
    "kw",
    "kz",
    "la",
    "lb",
    "li",
    "lk",
    "lt",
    "lu",
    "lv",
    "ly",
    "ma",
    "md",
    "me",
    "mk",
    "ml",
    "mm",
    "mn",
    "ms",
    "mu",
    "mx",
    "my",
    "nf",
    "ng",
    "nl",
    "no",
    "np",
    "nu",
    "nz",
    "om",
    "pa",
    "pe",
    "ph",
    "pk",
    "pl",
    "pr",
    "ps",
    "pt",
    "pw",
    "py",
    "qa",
    "re",
    "ro",
    "rs",
    "ru",
    "sa",
    "sc",
    "se",
    "sg",
    "sh",
    "si",
    "sk",
    "so",
    "st",
    "su",
    "sv",
    "sx",
    "th",
    "tj",
    "tk",
    "tn",
    "to",
    "tr",
    "tt",
    "tv",
    "tw",
    "ua",
    "ug",
    "uk",
    "us",
    "uy",
    "vc",
    "ve",
    "vn",
    "ws",
    "za",
]
