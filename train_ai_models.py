#!/usr/bin/env python3
"""
AI Models Training Application - تطبيق تدريب نماذج الذكاء الاصطناعي
تدريب منفصل ومتوازي لنماذج الذكاء الاصطناعي
"""

import asyncio
import sys
import os
import json
import threading
import time
from datetime import datetime
from typing import Dict, List, Any, Optional
import logging

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.strategy.ai_analysis import AIAnalysisLayer

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ai_training.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class AITrainingManager:
    """مدير تدريب نماذج الذكاء الاصطناعي"""
    
    def __init__(self):
        """تهيئة مدير التدريب"""
        self.ai_layer = None
        self.training_thread = None
        self.is_training = False
        self.training_progress = {}
        self.training_results = {}
        
        logger.info("🤖 AI Training Manager initialized")
    
    def start_background_training(self):
        """بدء التدريب في الخلفية"""
        if self.is_training:
            logger.warning("⚠️ Training already in progress")
            return False
        
        logger.info("🚀 Starting background AI training...")
        self.training_thread = threading.Thread(target=self._run_training, daemon=True)
        self.training_thread.start()
        return True
    
    def _run_training(self):
        """تشغيل التدريب في خيط منفصل"""
        try:
            self.is_training = True
            self.training_progress = {
                'status': 'INITIALIZING',
                'progress': 0,
                'current_step': 'Initializing AI layer',
                'start_time': datetime.now().isoformat()
            }
            
            # تهيئة طبقة الذكاء الاصطناعي
            logger.info("📚 Initializing AI layer for training...")
            self.ai_layer = AIAnalysisLayer()
            
            self.training_progress.update({
                'status': 'LOADING_DATA',
                'progress': 10,
                'current_step': 'Loading historical data'
            })
            
            # تحميل البيانات التاريخية
            logger.info("📊 Loading historical training data...")
            training_data = self._load_training_data()
            
            if not training_data or len(training_data['candles']) < 100:
                raise Exception("Insufficient training data")
            
            self.training_progress.update({
                'status': 'PREPARING_DATA',
                'progress': 30,
                'current_step': f'Preparing {len(training_data["candles"])} samples'
            })
            
            # تحضير البيانات
            logger.info(f"🔧 Preparing {len(training_data['candles'])} training samples...")
            X, y = self.ai_layer.prepare_training_data(
                training_data['candles'],
                training_data['outcomes']
            )
            
            self.training_progress.update({
                'status': 'TRAINING',
                'progress': 50,
                'current_step': f'Training models with {len(X)} features'
            })
            
            # تدريب النماذج
            logger.info(f"🎯 Training AI models with {len(X)} samples...")
            training_results = self.ai_layer.train_models(X, y)
            
            self.training_progress.update({
                'status': 'SAVING',
                'progress': 90,
                'current_step': 'Saving trained models'
            })
            
            # حفظ النتائج
            self.training_results = training_results
            self._save_training_report()
            
            self.training_progress.update({
                'status': 'COMPLETED',
                'progress': 100,
                'current_step': 'Training completed successfully',
                'end_time': datetime.now().isoformat()
            })
            
            logger.info("✅ AI training completed successfully!")
            
        except Exception as e:
            logger.error(f"❌ Training failed: {e}")
            self.training_progress.update({
                'status': 'FAILED',
                'error': str(e),
                'end_time': datetime.now().isoformat()
            })
        finally:
            self.is_training = False
    
    def _load_training_data(self) -> Dict[str, List]:
        """تحميل البيانات التاريخية للتدريب"""
        try:
            historical_dir = "data/historical"
            all_candles = []
            all_outcomes = []
            
            if not os.path.exists(historical_dir):
                logger.warning("⚠️ Historical data directory not found")
                return {'candles': [], 'outcomes': []}
            
            # قراءة جميع ملفات البيانات التاريخية
            for filename in os.listdir(historical_dir):
                if filename.endswith('.json'):
                    filepath = os.path.join(historical_dir, filename)
                    
                    try:
                        with open(filepath, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                        
                        candles = data.get('candles', [])
                        
                        if len(candles) >= 50:
                            # تقسيم البيانات إلى عينات تدريب
                            lookback_period = 20
                            prediction_horizon = 1
                            
                            for i in range(lookback_period, len(candles) - prediction_horizon):
                                # أخذ آخر lookback_period شمعة
                                sample_candles = candles[i-lookback_period:i]
                                
                                # تحديد النتيجة بناءً على الشمعة التالية
                                current_close = float(candles[i]['close'])
                                future_close = float(candles[i + prediction_horizon]['close'])
                                
                                # تبسيط التصنيف لتجنب عدم التوازن
                                if future_close > current_close:  # أي ارتفاع
                                    outcome = 'CALL'
                                else:  # أي انخفاض أو ثبات
                                    outcome = 'PUT'
                                
                                all_candles.append(sample_candles)
                                all_outcomes.append(outcome)
                    
                    except Exception as e:
                        logger.warning(f"⚠️ Error loading {filename}: {e}")
                        continue
            
            logger.info(f"📊 Loaded {len(all_candles)} training samples from {len(os.listdir(historical_dir))} files")
            
            return {
                'candles': all_candles,
                'outcomes': all_outcomes
            }
            
        except Exception as e:
            logger.error(f"❌ Error loading training data: {e}")
            return {'candles': [], 'outcomes': []}
    
    def _save_training_report(self):
        """حفظ تقرير التدريب"""
        try:
            report = {
                'training_timestamp': datetime.now().isoformat(),
                'training_progress': self.training_progress,
                'training_results': self.training_results,
                'model_performance': self.ai_layer.get_model_performance() if self.ai_layer else {}
            }
            
            # إنشاء مجلد التقارير إذا لم يكن موجوداً
            reports_dir = "data/ai_training_reports"
            os.makedirs(reports_dir, exist_ok=True)
            
            # حفظ التقرير
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_file = os.path.join(reports_dir, f"training_report_{timestamp}.json")
            
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            logger.info(f"📄 Training report saved: {report_file}")
            
        except Exception as e:
            logger.error(f"❌ Error saving training report: {e}")
    
    def get_training_status(self) -> Dict[str, Any]:
        """الحصول على حالة التدريب"""
        return {
            'is_training': self.is_training,
            'progress': self.training_progress,
            'results': self.training_results
        }
    
    def stop_training(self):
        """إيقاف التدريب"""
        if self.is_training and self.training_thread:
            logger.info("🛑 Stopping training...")
            self.is_training = False
            # ملاحظة: لا يمكن إيقاف الخيط بقوة، سيتوقف عند النقطة التالية

def main():
    """الدالة الرئيسية"""
    print("🤖 AI Models Training Application")
    print("="*50)
    
    trainer = AITrainingManager()
    
    while True:
        print("\n" + "="*50)
        print("🤖 AI TRAINING MENU")
        print("="*50)
        print("1. Start Background Training")
        print("2. Check Training Status")
        print("3. View Training Results")
        print("4. Stop Training")
        print("5. Exit")
        print("="*50)
        
        choice = input("Select option (1-5): ").strip()
        
        if choice == '1':
            if trainer.start_background_training():
                print("🚀 Background training started!")
                print("You can check progress with option 2")
            else:
                print("⚠️ Training already in progress")
        
        elif choice == '2':
            status = trainer.get_training_status()
            print("\n📊 Training Status:")
            print(f"Is Training: {status['is_training']}")
            
            if status['progress']:
                progress = status['progress']
                print(f"Status: {progress.get('status', 'Unknown')}")
                print(f"Progress: {progress.get('progress', 0)}%")
                print(f"Current Step: {progress.get('current_step', 'Unknown')}")
                
                if 'start_time' in progress:
                    print(f"Started: {progress['start_time']}")
                if 'end_time' in progress:
                    print(f"Ended: {progress['end_time']}")
                if 'error' in progress:
                    print(f"Error: {progress['error']}")
        
        elif choice == '3':
            status = trainer.get_training_status()
            if status['results']:
                print("\n📈 Training Results:")
                results = status['results']
                
                if 'error' in results:
                    print(f"❌ Training failed: {results['error']}")
                else:
                    print(f"✅ Training completed successfully")
                    print(f"Models trained: {len(results.get('models', {}))}")
                    
                    for model_name, model_results in results.get('models', {}).items():
                        if isinstance(model_results, dict) and 'accuracy' in model_results:
                            print(f"  {model_name}: {model_results['accuracy']:.3f} accuracy")
            else:
                print("📭 No training results available")
        
        elif choice == '4':
            trainer.stop_training()
            print("🛑 Training stop requested")
        
        elif choice == '5':
            print("👋 Goodbye!")
            break
        
        else:
            print("❌ Invalid choice. Please try again.")
        
        if choice != '5':
            input("\nPress Enter to continue...")

if __name__ == "__main__":
    main()
