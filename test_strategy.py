#!/usr/bin/env python3
"""
Strategy Testing Application - تطبيق اختبار الاستراتيجية
اختبار شامل للاستراتيجية الرباعية الطبقات
"""

import asyncio
import sys
import os
import json
from datetime import datetime
from typing import Dict, List, Any, Optional

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.strategy.decision_maker import StrategyDecisionMaker
from src.strategy.strategy_tester import StrategyTester
from src.strategy.strategy_config import StrategyConfig

class StrategyTestApp:
    """تطبيق اختبار الاستراتيجية"""
    
    def __init__(self):
        """تهيئة التطبيق"""
        self.strategy = StrategyDecisionMaker()
        self.tester = StrategyTester()
        self.config = StrategyConfig()
        
        print("🧪 Binary Options Strategy Testing System")
        print("="*60)
        print("Target Win Rate: 80% (8 out of 10 trades)")
        print("Minimum Confidence: 80%")
        print("="*60)
    
    async def run(self):
        """تشغيل التطبيق الرئيسي"""
        try:
            while True:
                choice = self._show_main_menu()
                
                if choice == '1':
                    await self._test_single_pair()
                elif choice == '2':
                    await self._test_multiple_pairs()
                elif choice == '3':
                    await self._run_comprehensive_test()
                elif choice == '4':
                    await self._test_live_analysis()
                elif choice == '5':
                    self._view_strategy_settings()
                elif choice == '6':
                    self._update_strategy_settings()
                elif choice == '7':
                    await self._view_test_results()
                elif choice == '8':
                    print("👋 Goodbye!")
                    break
                else:
                    print("❌ Invalid choice. Please try again.")
                
                input("\nPress Enter to continue...")
                
        except KeyboardInterrupt:
            print("\n🛑 Application stopped by user")
        except Exception as e:
            print(f"❌ Application error: {e}")
    
    def _show_main_menu(self) -> str:
        """عرض القائمة الرئيسية"""
        print("\n" + "="*60)
        print("🧪 STRATEGY TESTING MENU")
        print("="*60)
        print("1. Test Single Pair")
        print("2. Test Multiple Pairs")
        print("3. Run Comprehensive Test (All Available Pairs)")
        print("4. Test Live Analysis (with simulated live candle)")
        print("5. View Strategy Settings")
        print("6. Update Strategy Settings")
        print("7. View Test Results History")
        print("8. Exit")
        print("="*60)
        
        return input("Select option (1-8): ").strip()
    
    async def _test_single_pair(self):
        """اختبار زوج واحد"""
        try:
            print("\n🔍 Single Pair Test")
            print("-" * 30)
            
            # الحصول على الأزواج المتاحة
            available_pairs = self._get_available_pairs()
            
            if not available_pairs:
                print("❌ No pairs available for testing")
                return
            
            # عرض الأزواج المتاحة
            print("Available pairs:")
            for i, pair in enumerate(available_pairs, 1):
                print(f"{i:2d}. {pair}")
            
            # اختيار الزوج
            try:
                choice = int(input(f"\nSelect pair (1-{len(available_pairs)}): ").strip())
                if 1 <= choice <= len(available_pairs):
                    selected_pair = available_pairs[choice - 1]
                else:
                    print("❌ Invalid selection")
                    return
            except ValueError:
                print("❌ Invalid input")
                return
            
            print(f"\n⏳ Testing strategy on {selected_pair}...")
            
            # تشغيل الاختبار
            result = await self.tester.test_strategy_on_pair(selected_pair)
            
            # عرض النتائج
            self._display_single_test_result(result)
            
        except Exception as e:
            print(f"❌ Error in single pair test: {e}")
    
    async def _test_multiple_pairs(self):
        """اختبار عدة أزواج"""
        try:
            print("\n🔍 Multiple Pairs Test")
            print("-" * 30)
            
            available_pairs = self._get_available_pairs()
            
            if not available_pairs:
                print("❌ No pairs available for testing")
                return
            
            print(f"Available pairs: {', '.join(available_pairs)}")
            
            pairs_input = input("\nEnter pairs (comma-separated) or 'all' for all pairs: ").strip()
            
            if pairs_input.lower() == 'all':
                selected_pairs = available_pairs
            else:
                selected_pairs = [p.strip().upper() for p in pairs_input.split(',') if p.strip()]
                
                # التحقق من صحة الأزواج
                invalid_pairs = [p for p in selected_pairs if p not in available_pairs]
                if invalid_pairs:
                    print(f"❌ Invalid pairs: {', '.join(invalid_pairs)}")
                    return
            
            if not selected_pairs:
                print("❌ No valid pairs selected")
                return
            
            print(f"\n⏳ Testing strategy on {len(selected_pairs)} pairs...")
            
            # تشغيل الاختبار
            results = await self.tester.test_strategy_on_multiple_pairs(selected_pairs)
            
            # عرض النتائج
            self._display_multiple_test_results(results)
            
        except Exception as e:
            print(f"❌ Error in multiple pairs test: {e}")
    
    async def _run_comprehensive_test(self):
        """تشغيل اختبار شامل"""
        try:
            print("\n🔬 Comprehensive Strategy Test")
            print("-" * 40)
            
            available_pairs = self._get_available_pairs()
            
            if not available_pairs:
                print("❌ No pairs available for testing")
                return
            
            print(f"Testing strategy on all {len(available_pairs)} available pairs...")
            print("This may take several minutes...")
            
            confirm = input("\nProceed with comprehensive test? (yes/y/no/n): ").strip().lower()

            if confirm not in ['yes', 'y']:
                print("❌ Test cancelled")
                return
            
            print("\n⏳ Running comprehensive test...")
            
            # تشغيل الاختبار الشامل
            results = await self.tester.test_strategy_on_multiple_pairs(available_pairs)
            
            # عرض النتائج التفصيلية
            self._display_comprehensive_results(results)
            
        except Exception as e:
            print(f"❌ Error in comprehensive test: {e}")
    
    async def _test_live_analysis(self):
        """اختبار التحليل مع البيانات الحية"""
        try:
            print("\n📊 Live Analysis Test")
            print("-" * 30)
            
            available_pairs = self._get_available_pairs()
            
            if not available_pairs:
                print("❌ No pairs available for testing")
                return
            
            # اختيار زوج للاختبار
            pair_name = available_pairs[0]  # أول زوج متاح
            print(f"Testing live analysis on {pair_name}")
            
            # تحميل البيانات التاريخية
            candles = self._load_pair_data(pair_name)
            
            if not candles or len(candles) < 50:
                print(f"❌ Insufficient data for {pair_name}")
                return
            
            # محاكاة شمعة حية
            last_candle = candles[-1]
            live_candle = {
                'time': last_candle['time'] + 300,  # 5 دقائق بعد آخر شمعة
                'open': last_candle['close'],
                'high': last_candle['close'] * 1.0005,  # ارتفاع طفيف
                'low': last_candle['close'] * 0.9995,   # انخفاض طفيف
                'close': last_candle['close'] * 1.0002, # إغلاق أعلى قليلاً
                'volume': last_candle.get('volume', 1000),
                'is_live': True  # علامة للشمعة الحية
            }
            
            print("📈 Simulated live candle:")
            print(f"   Open: {live_candle['open']:.5f}")
            print(f"   High: {live_candle['high']:.5f}")
            print(f"   Low: {live_candle['low']:.5f}")
            print(f"   Close: {live_candle['close']:.5f}")
            
            print("\n⏳ Analyzing with live candle...")
            
            # تشغيل التحليل مع الشمعة الحية
            result = self.strategy.run_strategy_analysis(
                pair_name=pair_name,
                candles=candles[:-1],  # بدون آخر شمعة
                live_candle=live_candle
            )
            
            # عرض النتائج
            self._display_live_analysis_result(result)
            
        except Exception as e:
            print(f"❌ Error in live analysis test: {e}")
    
    def _view_strategy_settings(self):
        """عرض إعدادات الاستراتيجية"""
        print("\n⚙️ Current Strategy Settings")
        print("="*50)
        
        settings = self.strategy.get_current_settings()
        
        print(f"Minimum Confidence Threshold: {settings['min_confidence_threshold']}%")
        print(f"Minimum Agreement Layers: {settings['min_agreement_layers']}")
        print(f"Risk Management Enabled: {settings['risk_management_enabled']}")
        print(f"Trading Hours: {settings['trading_hours']['start']}:00 - {settings['trading_hours']['end']}:00")
        print(f"Available Expiry Times: {settings['available_expiry_times']} minutes")
        
        # عرض أداء الاستراتيجية
        performance = self.strategy.get_strategy_performance()
        
        if performance['total_signals'] > 0:
            print(f"\n📊 Strategy Performance:")
            print(f"Total Signals: {performance['total_signals']}")
            print(f"Trade Signals: {performance['trade_signals']}")
            print(f"Trade Ratio: {performance['trade_ratio']:.2f}")
            print(f"Average Confidence: {performance['average_confidence']:.1f}%")
    
    def _update_strategy_settings(self):
        """تحديث إعدادات الاستراتيجية"""
        try:
            print("\n⚙️ Update Strategy Settings")
            print("-" * 40)
            
            current_settings = self.strategy.get_current_settings()
            
            print("Current settings:")
            print(f"1. Min Confidence: {current_settings['min_confidence_threshold']}%")
            print(f"2. Min Agreement Layers: {current_settings['min_agreement_layers']}")
            print(f"3. Risk Management: {current_settings['risk_management_enabled']}")
            
            print("\nWhat would you like to update?")
            print("1. Minimum Confidence Threshold")
            print("2. Minimum Agreement Layers")
            print("3. Risk Management")
            print("4. Cancel")
            
            choice = input("Select option (1-4): ").strip()
            
            if choice == '1':
                try:
                    new_confidence = float(input(f"Enter new confidence threshold (current: {current_settings['min_confidence_threshold']}%): "))
                    if 0 <= new_confidence <= 100:
                        self.strategy.update_strategy_settings({'min_confidence_threshold': new_confidence})
                        print(f"✅ Confidence threshold updated to {new_confidence}%")
                    else:
                        print("❌ Invalid value. Must be between 0 and 100")
                except ValueError:
                    print("❌ Invalid input")
            
            elif choice == '2':
                try:
                    new_layers = int(input(f"Enter minimum agreement layers (current: {current_settings['min_agreement_layers']}): "))
                    if 1 <= new_layers <= 4:
                        self.strategy.update_strategy_settings({'min_agreement_layers': new_layers})
                        print(f"✅ Agreement layers updated to {new_layers}")
                    else:
                        print("❌ Invalid value. Must be between 1 and 4")
                except ValueError:
                    print("❌ Invalid input")
            
            elif choice == '3':
                current_risk = current_settings['risk_management_enabled']
                new_risk = input(f"Enable risk management? (current: {current_risk}) (yes/y/no/n): ").strip().lower()
                if new_risk in ['yes', 'y', 'no', 'n']:
                    enabled = new_risk in ['yes', 'y']
                    self.strategy.update_strategy_settings({'risk_management_enabled': enabled})
                    print(f"✅ Risk management updated to {enabled}")
                else:
                    print("❌ Invalid input. Use 'yes', 'y', 'no', or 'n'")
            
            elif choice == '4':
                print("❌ Cancelled")
            
            else:
                print("❌ Invalid choice")
                
        except Exception as e:
            print(f"❌ Error updating settings: {e}")
    
    async def _view_test_results(self):
        """عرض نتائج الاختبارات السابقة"""
        try:
            print("\n📊 Test Results History")
            print("="*50)
            
            results_dir = "data/strategy_tests"
            
            if not os.path.exists(results_dir):
                print("❌ No test results found")
                return
            
            test_files = [f for f in os.listdir(results_dir) if f.startswith('strategy_test_') and f.endswith('.json')]
            
            if not test_files:
                print("❌ No test results found")
                return
            
            # ترتيب الملفات حسب التاريخ
            test_files.sort(reverse=True)
            
            print(f"Found {len(test_files)} test results:")
            print("-" * 50)
            
            for i, test_file in enumerate(test_files[:10], 1):  # آخر 10 نتائج
                test_path = os.path.join(results_dir, test_file)
                
                try:
                    with open(test_path, 'r', encoding='utf-8') as f:
                        test_data = json.load(f)
                    
                    timestamp = test_data.get('test_timestamp', 'Unknown')
                    total_pairs = test_data.get('total_pairs_tested', 0)
                    success_rate = test_data.get('success_rate', 0)
                    
                    overall_analysis = test_data.get('overall_analysis', {})
                    avg_score = overall_analysis.get('average_score', 0)
                    
                    print(f"{i:2d}. {timestamp[:19]} | Pairs: {total_pairs:2d} | "
                          f"Success: {success_rate:5.1f}% | Avg Score: {avg_score:5.1f}")
                    
                except Exception as e:
                    print(f"{i:2d}. Error reading {test_file}: {e}")
            
        except Exception as e:
            print(f"❌ Error viewing test results: {e}")
    
    def _get_available_pairs(self) -> List[str]:
        """الحصول على الأزواج المتاحة"""
        try:
            historical_dir = "data/historical"
            
            if not os.path.exists(historical_dir):
                return []
            
            pairs = []
            for filename in os.listdir(historical_dir):
                if filename.endswith('.json'):
                    pair_name = filename.replace('.json', '')
                    pairs.append(pair_name)
            
            return sorted(pairs)
            
        except Exception as e:
            print(f"Error getting available pairs: {e}")
            return []
    
    def _load_pair_data(self, pair_name: str) -> Optional[List[Dict[str, Any]]]:
        """تحميل بيانات زوج العملات"""
        try:
            file_path = f"data/historical/{pair_name}.json"
            
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data.get('candles', [])
            
            return None
            
        except Exception as e:
            print(f"Error loading data for {pair_name}: {e}")
            return None
    
    def _display_single_test_result(self, result: Dict[str, Any]):
        """عرض نتيجة اختبار زوج واحد"""
        print("\n📊 Test Result")
        print("="*40)
        
        if result.get('status') == 'SUCCESS':
            pair_name = result.get('pair_name', 'Unknown')
            data_points = result.get('data_points', 0)
            
            analysis_result = result.get('analysis_result', {})
            trading_decision = analysis_result.get('trading_decision', {})
            
            print(f"Pair: {pair_name}")
            print(f"Data Points: {data_points}")
            print(f"Decision: {trading_decision.get('decision', 'Unknown')}")
            
            if trading_decision.get('decision') == 'TRADE':
                print(f"Direction: {trading_decision.get('direction', 'Unknown')}")
                print(f"Confidence: {trading_decision.get('confidence', 0):.1f}%")
                print(f"Expiry: {trading_decision.get('expiry_minutes', 0)} minutes")
                print(f"Reasoning: {trading_decision.get('reasoning', 'N/A')}")
            
            # عرض تقييم النتيجة
            evaluation = result.get('evaluation', {})
            print(f"\nEvaluation:")
            print(f"Overall Score: {evaluation.get('overall_score', 0):.1f}/100")
            print(f"Decision Quality: {evaluation.get('decision_quality', 'Unknown')}")
            print(f"Confidence Level: {evaluation.get('confidence_level', 'Unknown')}")
            
        else:
            print(f"❌ Test failed: {result.get('error', 'Unknown error')}")
    
    def _display_multiple_test_results(self, results: Dict[str, Any]):
        """عرض نتائج اختبار عدة أزواج"""
        print("\n📊 Multiple Pairs Test Results")
        print("="*60)
        
        if 'error' in results:
            print(f"❌ Test failed: {results['error']}")
            return
        
        print(f"Total Pairs Tested: {results.get('total_pairs_tested', 0)}")
        print(f"Successful Tests: {results.get('successful_tests', 0)}")
        print(f"Failed Tests: {results.get('failed_tests', 0)}")
        print(f"Success Rate: {results.get('success_rate', 0):.1f}%")
        
        overall_analysis = results.get('overall_analysis', {})
        print(f"Average Score: {overall_analysis.get('average_score', 0):.1f}")
        
        decision_dist = overall_analysis.get('decision_distribution', {})
        print(f"Trade Decisions: {decision_dist.get('TRADE', 0)}")
        print(f"No-Trade Decisions: {decision_dist.get('NO_TRADE', 0)}")
        
        confidence_dist = overall_analysis.get('confidence_distribution', {})
        print(f"High Confidence: {confidence_dist.get('HIGH', 0)}")
        print(f"Medium Confidence: {confidence_dist.get('MEDIUM', 0)}")
        print(f"Low Confidence: {confidence_dist.get('LOW', 0)}")
    
    def _display_comprehensive_results(self, results: Dict[str, Any]):
        """عرض نتائج الاختبار الشامل"""
        self._display_multiple_test_results(results)
        
        # إضافة تحليل مفصل
        print(f"\n🎯 Strategy Performance Analysis:")
        
        overall_analysis = results.get('overall_analysis', {})
        performance_summary = overall_analysis.get('performance_summary', {})
        
        excellent = performance_summary.get('excellent_decisions', 0)
        good = performance_summary.get('good_decisions', 0)
        poor = performance_summary.get('poor_decisions', 0)
        trade_rate = performance_summary.get('trade_rate', 0)
        
        print(f"Excellent Decisions (80%+): {excellent}")
        print(f"Good Decisions (60-80%): {good}")
        print(f"Poor Decisions (<60%): {poor}")
        print(f"Trade Rate: {trade_rate:.1f}%")
        
        # تقييم الأداء العام
        total_tests = results.get('successful_tests', 0)
        if total_tests > 0:
            excellent_rate = (excellent / total_tests) * 100
            
            if excellent_rate >= 80:
                print("\n🎉 EXCELLENT STRATEGY PERFORMANCE!")
                print("   Strategy meets the 80% target!")
            elif excellent_rate >= 70:
                print("\n👍 GOOD STRATEGY PERFORMANCE")
                print("   Strategy shows strong potential")
            elif excellent_rate >= 60:
                print("\n👌 ACCEPTABLE STRATEGY PERFORMANCE")
                print("   Strategy needs some improvements")
            else:
                print("\n⚠️  STRATEGY NEEDS IMPROVEMENT")
                print("   Consider adjusting parameters")
    
    def _display_live_analysis_result(self, result: Dict[str, Any]):
        """عرض نتيجة التحليل الحي"""
        print("\n📊 Live Analysis Result")
        print("="*40)
        
        if 'error' in result:
            print(f"❌ Analysis failed: {result['error']}")
            return
        
        trading_decision = result.get('trading_decision', {})
        
        print(f"Decision: {trading_decision.get('decision', 'Unknown')}")
        
        if trading_decision.get('decision') == 'TRADE':
            print(f"Direction: {trading_decision.get('direction', 'Unknown')}")
            print(f"Confidence: {trading_decision.get('confidence', 0):.1f}%")
            print(f"Expiry: {trading_decision.get('expiry_minutes', 0)} minutes")
            print(f"Reasoning: {trading_decision.get('reasoning', 'N/A')}")
            
            # عرض اتفاق الطبقات
            layer_agreement = trading_decision.get('layer_agreement', {})
            print(f"\nLayer Agreement:")
            print(f"Agreeing Layers: {layer_agreement.get('agreeing_layers', 0)}/4")
            print(f"Agreement Level: {layer_agreement.get('agreement_level', 'Unknown')}")
            print(f"Consensus: {layer_agreement.get('consensus', 'Unknown')}")
            
            if trading_decision.get('confidence', 0) >= 80:
                print("\n✅ HIGH QUALITY SIGNAL - Ready for trading!")
            else:
                print("\n⚠️  Signal quality below threshold")
        else:
            print(f"Reason: {trading_decision.get('reasoning', 'N/A')}")

async def main():
    """الدالة الرئيسية"""
    app = StrategyTestApp()
    await app.run()

if __name__ == "__main__":
    asyncio.run(main())
