"""
Strategy Tester - اختبار الاستراتيجية
اختبار شامل للاستراتيجية الرباعية الطبقات
"""

import asyncio
import json
import os
from datetime import datetime
from typing import Dict, List, Any, Optional
import logging

from .decision_maker import StrategyDecisionMaker
from ..historical_data.historical_manager import HistoricalDataManager
from ..indicators.indicators_manager import IndicatorsManager

logger = logging.getLogger(__name__)

class StrategyTester:
    """فئة اختبار الاستراتيجية"""
    
    def __init__(self, data_dir: str = "data"):
        """تهيئة اختبار الاستراتيجية"""
        self.data_dir = data_dir
        self.strategy = StrategyDecisionMaker(data_dir)
        self.test_results = []
        
        logger.info("✅ Strategy Tester initialized")
    
    async def test_strategy_on_pair(self, pair_name: str) -> Dict[str, Any]:
        """
        اختبار الاستراتيجية على زوج عملات محدد
        
        Args:
            pair_name: اسم زوج العملات
            
        Returns:
            نتائج الاختبار
        """
        try:
            logger.info(f"🧪 Testing strategy on {pair_name}")
            
            # تحميل البيانات التاريخية
            historical_data = self._load_historical_data(pair_name)
            if not historical_data:
                return {
                    'pair_name': pair_name,
                    'error': 'No historical data found',
                    'status': 'FAILED'
                }
            
            candles = historical_data.get('candles', [])
            if len(candles) < 100:
                return {
                    'pair_name': pair_name,
                    'error': f'Insufficient data: {len(candles)} candles',
                    'status': 'FAILED'
                }
            
            # تحميل بيانات المؤشرات
            indicators_data = self._load_indicators_data(pair_name)
            
            # تشغيل التحليل
            analysis_result = self.strategy.run_strategy_analysis(
                pair_name, candles, indicators_data
            )
            
            # تقييم النتائج
            evaluation = self._evaluate_analysis_result(analysis_result)
            
            test_result = {
                'pair_name': pair_name,
                'test_timestamp': datetime.now().isoformat(),
                'data_points': len(candles),
                'analysis_result': analysis_result,
                'evaluation': evaluation,
                'status': 'SUCCESS'
            }
            
            self.test_results.append(test_result)
            
            logger.info(f"✅ Strategy test completed for {pair_name}")
            return test_result
            
        except Exception as e:
            logger.error(f"Error testing strategy on {pair_name}: {e}")
            return {
                'pair_name': pair_name,
                'error': str(e),
                'status': 'ERROR'
            }
    
    async def test_strategy_on_multiple_pairs(self, pair_names: List[str]) -> Dict[str, Any]:
        """
        اختبار الاستراتيجية على عدة أزواج
        
        Args:
            pair_names: قائمة أسماء أزواج العملات
            
        Returns:
            نتائج الاختبار الشاملة
        """
        try:
            logger.info(f"🧪 Testing strategy on {len(pair_names)} pairs")
            
            all_results = []
            successful_tests = 0
            failed_tests = 0
            
            for pair_name in pair_names:
                result = await self.test_strategy_on_pair(pair_name)
                all_results.append(result)
                
                if result.get('status') == 'SUCCESS':
                    successful_tests += 1
                else:
                    failed_tests += 1
            
            # تحليل النتائج الإجمالية
            overall_analysis = self._analyze_overall_results(all_results)
            
            summary = {
                'test_timestamp': datetime.now().isoformat(),
                'total_pairs_tested': len(pair_names),
                'successful_tests': successful_tests,
                'failed_tests': failed_tests,
                'success_rate': (successful_tests / len(pair_names)) * 100,
                'individual_results': all_results,
                'overall_analysis': overall_analysis
            }
            
            # حفظ النتائج
            self._save_test_results(summary)
            
            logger.info(f"✅ Multi-pair strategy test completed. Success rate: {summary['success_rate']:.1f}%")
            return summary
            
        except Exception as e:
            logger.error(f"Error in multi-pair strategy test: {e}")
            return {'error': str(e)}
    
    def _load_historical_data(self, pair_name: str) -> Optional[Dict[str, Any]]:
        """تحميل البيانات التاريخية"""
        try:
            file_path = os.path.join(self.data_dir, "historical", f"{pair_name}.json")
            
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            
            return None
            
        except Exception as e:
            logger.error(f"Error loading historical data for {pair_name}: {e}")
            return None
    
    def _load_indicators_data(self, pair_name: str) -> Optional[Dict[str, Any]]:
        """تحميل بيانات المؤشرات"""
        try:
            file_path = os.path.join(self.data_dir, "indicators", f"{pair_name}_indicators.json")
            
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            
            return None
            
        except Exception as e:
            logger.error(f"Error loading indicators data for {pair_name}: {e}")
            return None
    
    def _evaluate_analysis_result(self, analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """تقييم نتيجة التحليل"""
        evaluation = {
            'overall_score': 0,
            'layer_scores': {},
            'decision_quality': 'UNKNOWN',
            'confidence_level': 'UNKNOWN',
            'recommendations': []
        }
        
        try:
            if 'error' in analysis_result:
                evaluation['decision_quality'] = 'ERROR'
                evaluation['recommendations'].append('Fix analysis errors')
                return evaluation
            
            # تقييم قرار التداول
            trading_decision = analysis_result.get('trading_decision', {})
            decision = trading_decision.get('decision', 'NO_TRADE')
            confidence = trading_decision.get('confidence', 0)
            
            # تقييم جودة القرار
            if decision == 'TRADE':
                if confidence >= 80:
                    evaluation['decision_quality'] = 'EXCELLENT'
                    evaluation['overall_score'] += 30
                elif confidence >= 70:
                    evaluation['decision_quality'] = 'GOOD'
                    evaluation['overall_score'] += 20
                elif confidence >= 60:
                    evaluation['decision_quality'] = 'FAIR'
                    evaluation['overall_score'] += 10
                else:
                    evaluation['decision_quality'] = 'POOR'
                    evaluation['recommendations'].append('Increase confidence threshold')
            else:
                evaluation['decision_quality'] = 'NO_TRADE'
                evaluation['overall_score'] += 5  # نقاط للحذر
            
            # تقييم مستوى الثقة
            if confidence >= 80:
                evaluation['confidence_level'] = 'HIGH'
            elif confidence >= 60:
                evaluation['confidence_level'] = 'MEDIUM'
            else:
                evaluation['confidence_level'] = 'LOW'
            
            # تقييم الطبقات
            market_analysis = analysis_result.get('market_analysis', {})
            layer_signals = market_analysis.get('layer_signals', {})
            
            for layer_name, layer_signal in layer_signals.items():
                layer_score = 0
                
                if 'error' not in layer_signal:
                    layer_confidence = layer_signal.get('confidence', 0)
                    layer_strength = layer_signal.get('strength', 0)
                    
                    layer_score = (layer_confidence + layer_strength * 100) / 2
                    evaluation['overall_score'] += layer_score / 4  # تقسيم على 4 طبقات
                
                evaluation['layer_scores'][layer_name] = layer_score
            
            # توصيات التحسين
            if evaluation['overall_score'] < 50:
                evaluation['recommendations'].append('Review strategy parameters')
            
            if len([s for s in layer_signals.values() if 'error' in s]) > 1:
                evaluation['recommendations'].append('Fix layer analysis errors')
            
            # تطبيع النتيجة الإجمالية
            evaluation['overall_score'] = min(100, max(0, evaluation['overall_score']))
            
        except Exception as e:
            logger.error(f"Error evaluating analysis result: {e}")
            evaluation['decision_quality'] = 'ERROR'
            evaluation['recommendations'].append(f'Evaluation error: {str(e)}')
        
        return evaluation
    
    def _analyze_overall_results(self, all_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """تحليل النتائج الإجمالية"""
        analysis = {
            'average_score': 0,
            'layer_performance': {},
            'decision_distribution': {},
            'confidence_distribution': {},
            'common_issues': [],
            'performance_summary': {}
        }
        
        try:
            successful_results = [r for r in all_results if r.get('status') == 'SUCCESS']
            
            if not successful_results:
                return analysis
            
            # حساب المتوسطات
            scores = []
            decisions = []
            confidences = []
            
            for result in successful_results:
                evaluation = result.get('evaluation', {})
                scores.append(evaluation.get('overall_score', 0))
                
                trading_decision = result.get('analysis_result', {}).get('trading_decision', {})
                decisions.append(trading_decision.get('decision', 'NO_TRADE'))
                confidences.append(trading_decision.get('confidence', 0))
            
            analysis['average_score'] = sum(scores) / len(scores) if scores else 0
            
            # توزيع القرارات
            for decision in decisions:
                analysis['decision_distribution'][decision] = analysis['decision_distribution'].get(decision, 0) + 1
            
            # توزيع الثقة
            high_conf = len([c for c in confidences if c >= 80])
            medium_conf = len([c for c in confidences if 60 <= c < 80])
            low_conf = len([c for c in confidences if c < 60])
            
            analysis['confidence_distribution'] = {
                'HIGH': high_conf,
                'MEDIUM': medium_conf,
                'LOW': low_conf
            }
            
            # ملخص الأداء
            analysis['performance_summary'] = {
                'excellent_decisions': len([s for s in scores if s >= 80]),
                'good_decisions': len([s for s in scores if 60 <= s < 80]),
                'poor_decisions': len([s for s in scores if s < 60]),
                'trade_rate': analysis['decision_distribution'].get('TRADE', 0) / len(successful_results) * 100
            }
            
        except Exception as e:
            logger.error(f"Error analyzing overall results: {e}")
            analysis['error'] = str(e)
        
        return analysis
    
    def _convert_numpy_types(self, obj):
        """تحويل numpy types إلى Python types للـ JSON serialization"""
        import numpy as np

        if isinstance(obj, np.bool_):
            return bool(obj)
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, dict):
            return {key: self._convert_numpy_types(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._convert_numpy_types(item) for item in obj]
        else:
            return obj

    def _save_test_results(self, results: Dict[str, Any]):
        """حفظ نتائج الاختبار"""
        try:
            results_dir = os.path.join(self.data_dir, "strategy_tests")
            os.makedirs(results_dir, exist_ok=True)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"strategy_test_{timestamp}.json"
            filepath = os.path.join(results_dir, filename)

            # تحويل numpy types قبل الحفظ
            clean_results = self._convert_numpy_types(results)

            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(clean_results, f, indent=2, ensure_ascii=False)

            logger.info(f"Test results saved to {filepath}")

        except Exception as e:
            logger.error(f"Error saving test results: {e}")
    
    def get_test_summary(self) -> Dict[str, Any]:
        """الحصول على ملخص الاختبارات"""
        return {
            'total_tests_run': len(self.test_results),
            'recent_tests': self.test_results[-5:] if self.test_results else [],
            'strategy_settings': self.strategy.get_current_settings()
        }
