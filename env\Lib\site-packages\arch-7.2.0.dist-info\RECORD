arch-7.2.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
arch-7.2.0.dist-info/LICENSE.md,sha256=IgtkBYBy0AuNg9SulZ0U-6UpI0Zr52qSeGx06K50KeI,1693
arch-7.2.0.dist-info/METADATA,sha256=2iZnFsqoCvCuFNFihkxhd7Nrqi5eAHMdJYeNMDfpjHw,13822
arch-7.2.0.dist-info/RECORD,,
arch-7.2.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
arch-7.2.0.dist-info/WHEEL,sha256=bkFTgQblV2VuwaX_c6St1wl4KjbN5AW_if3TQfQ0fhI,101
arch-7.2.0.dist-info/top_level.txt,sha256=FK2ixoCSymm4HFczRBsh3wRRbJt26OcpLmlpWYHKK3E,21
arch/__future__/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
arch/__future__/__pycache__/__init__.cpython-312.pyc,,
arch/__future__/__pycache__/reindexing.cpython-312.pyc,,
arch/__future__/reindexing.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
arch/__init__.py,sha256=E5OubzdIBvmk8waxZyChw9VWRj8TG5xn7VehycFtU0Q,312
arch/__pycache__/__init__.cpython-312.pyc,,
arch/__pycache__/_version.cpython-312.pyc,,
arch/__pycache__/conftest.cpython-312.pyc,,
arch/__pycache__/typing.cpython-312.pyc,,
arch/_version.py,sha256=2RlHCOslg2vfhdDf84EaCC7Vls7xORJoKCP1CYlAZI8,427
arch/bootstrap/__init__.py,sha256=by_QJFi5oTN8cnsQlVJX9uucx_a1I_K0qnRoUbfDJDc,843
arch/bootstrap/__pycache__/__init__.cpython-312.pyc,,
arch/bootstrap/__pycache__/_samplers_python.cpython-312.pyc,,
arch/bootstrap/__pycache__/base.cpython-312.pyc,,
arch/bootstrap/__pycache__/multiple_comparison.cpython-312.pyc,,
arch/bootstrap/_samplers.cp312-win_amd64.pyd,sha256=n3Jg7anj0tYPlcFDFGQIJpHSStt6kva6aCNPpDlB7Y0,152064
arch/bootstrap/_samplers.pyi,sha256=JwN9PcbFZiJ0BII1gp92Cy7psY3R0XyxoFgbGI0qW4g,160
arch/bootstrap/_samplers_python.py,sha256=uDcJ_wKHyk32i8imBA_fyZqZofxDI-Ea9RSlz2ynBi8,1106
arch/bootstrap/base.py,sha256=opI0bQ-JGCrR46CG-nIO1ZAYwnNh9SCY1R7_PbjVP20,67063
arch/bootstrap/multiple_comparison.py,sha256=jniVZCHtfdzzH5qjw9J14h6c06VVBwqMjC6eXabRjvk,31395
arch/compat/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
arch/compat/__pycache__/__init__.cpython-312.pyc,,
arch/compat/__pycache__/numba.cpython-312.pyc,,
arch/compat/__pycache__/pandas.cpython-312.pyc,,
arch/compat/__pycache__/statsmodels.cpython-312.pyc,,
arch/compat/numba.py,sha256=TG1Cs9kQyI02zjBJJVSz56HCCuscmFWZoA3ZadEF7AE,1672
arch/compat/pandas.py,sha256=j8W2ueNKG9iCk96tqgKwzkOVge0odYbO1efu3hkpkFo,488
arch/compat/statsmodels.py,sha256=m4KXs27PrPbXmydpwBw-n9N_KyjK-FiiCyskCbHJb78,338
arch/conftest.py,sha256=Af4qP9w1QrrvaCqy3NAl2ZQpWyo92xaRnKoLCk2kP4A,1324
arch/covariance/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
arch/covariance/__pycache__/__init__.cpython-312.pyc,,
arch/covariance/__pycache__/kernel.cpython-312.pyc,,
arch/covariance/kernel.py,sha256=o1Nhq7di6fdc1BsuyNqqHzoXqSs0qoU35FpYLg3RWpc,19202
arch/data/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
arch/data/__pycache__/__init__.cpython-312.pyc,,
arch/data/__pycache__/utility.cpython-312.pyc,,
arch/data/binary/__init__.py,sha256=ZxIgwJBvmvX707_-cPyMMYxYafQPWLaVcsf8X14od38,363
arch/data/binary/__pycache__/__init__.cpython-312.pyc,,
arch/data/binary/binary.csv.gz,sha256=YlT3n-9fbLMeq60NZxVYK0EDSDzI3CtnYJ_7gmRNlss,1609
arch/data/core_cpi/__init__.py,sha256=gL-hJlA2Rrc_rXZK7niuxTLcW7noyLXZ5R_mJb4M7t0,381
arch/data/core_cpi/__pycache__/__init__.cpython-312.pyc,,
arch/data/core_cpi/core-cpi.csv.gz,sha256=gfyRLVlnrSP0s3IqEPdoZwNEBTVLjwPzlR684x5b1BI,2749
arch/data/crude/__init__.py,sha256=mGq984ysO2-UOGav2TOLE11vf4eDExcug0GvEMHCtgg,378
arch/data/crude/__pycache__/__init__.cpython-312.pyc,,
arch/data/crude/crude.csv.gz,sha256=QZCzT2EwBppbkewD6OoaTf_7qNN5Az-c85NPppXafvc,3078
arch/data/default/__init__.py,sha256=nZx2mwALXiHSql9vCRDdiwDSDPZaeXxGRyZi9z0g07w,341
arch/data/default/__pycache__/__init__.cpython-312.pyc,,
arch/data/default/default.csv.gz,sha256=zFRTBoztx-9INpVjWNzh0g6vHmObacMQenUxqOa7mC0,6572
arch/data/frenchdata/__init__.py,sha256=MmziuHV3IsCuMeMRKUf4Vb31lK5ii4MjZ7Nm7o084ug,513
arch/data/frenchdata/__pycache__/__init__.cpython-312.pyc,,
arch/data/frenchdata/frenchdata.csv.gz,sha256=8jQ2cnsB2HnhNy4-4ndRSwlioj0rFY8a7mpAKpCZT1I,10817
arch/data/nasdaq/__init__.py,sha256=_Cm2DIFwbjqrE2DzO4Sxvt-HpZg_j5PzRC0eAdGJKwA,351
arch/data/nasdaq/__pycache__/__init__.cpython-312.pyc,,
arch/data/nasdaq/nasdaq.csv.gz,sha256=sgCVNTQ-uxTgqhHtCGeI0pl6QW_-T5RQ7tmlYa4wrFk,114929
arch/data/sp500/__init__.py,sha256=QLfRsoPsLG4U77hdj45s8sGaYhY5-EQubWHz2JHvM1k,341
arch/data/sp500/__pycache__/__init__.cpython-312.pyc,,
arch/data/sp500/sp500.csv.gz,sha256=HgKMu5xADMAYyBbMxDmzPJGTh-cmw-1cosBcgnRgWd4,102735
arch/data/utility.py,sha256=vZl6gTKyxzd7lS3u69kkRvoP0OyjWJYksK7y7Tc_drk,724
arch/data/vix/__init__.py,sha256=SfZFyitxUJB1F5TBIA6NhGWcGjhGMUmF0cyevNjI-VY,311
arch/data/vix/__pycache__/__init__.cpython-312.pyc,,
arch/data/vix/vix.csv.gz,sha256=QQvQc2DceY9yBtaBuDR8YVDn-9iwX7GuKbviyZhdZu8,6031
arch/data/wti/__init__.py,sha256=oP7Ajdy_9rRbySrXvhq66E558hjRZhJ-_UHoou1q2D8,395
arch/data/wti/__pycache__/__init__.cpython-312.pyc,,
arch/data/wti/wti.csv.gz,sha256=uDSYiUE4plzcFKZ_wXFqqQs8_aJ0-Xa9NpLMPxUACE0,39069
arch/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
arch/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
arch/tests/__pycache__/__init__.cpython-312.pyc,,
arch/tests/__pycache__/test_compat.cpython-312.pyc,,
arch/tests/__pycache__/test_data.cpython-312.pyc,,
arch/tests/__pycache__/test_examples.cpython-312.pyc,,
arch/tests/__pycache__/test_tester.cpython-312.pyc,,
arch/tests/bootstrap/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
arch/tests/bootstrap/__pycache__/__init__.cpython-312.pyc,,
arch/tests/bootstrap/__pycache__/test_block_length.cpython-312.pyc,,
arch/tests/bootstrap/__pycache__/test_bootstrap.cpython-312.pyc,,
arch/tests/bootstrap/__pycache__/test_multiple_comparison.cpython-312.pyc,,
arch/tests/bootstrap/test_block_length.py,sha256=UlseJV71ArP5poP_EfwMAPpljntTQkn_vitEdbWEgR0,967
arch/tests/bootstrap/test_bootstrap.py,sha256=AcUpKoU62NlBZN-cOvcrW4eDT2Js2LC3KIZPVnPzJoE,34722
arch/tests/bootstrap/test_multiple_comparison.py,sha256=NhyRfrinnNuDZpTqtJIVXldgsmUt6W022j8i20axxFI,19070
arch/tests/covariance/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
arch/tests/covariance/__pycache__/__init__.cpython-312.pyc,,
arch/tests/covariance/__pycache__/test_covariance.cpython-312.pyc,,
arch/tests/covariance/test_covariance.py,sha256=0aHEEDz8ACzvEsT1pd-e5JP3fb6ZuaIWJ2D_ZxLwenw,9019
arch/tests/test_compat.py,sha256=aZOpxZnhCM_1Hs8alvypoCBiApp9hd3MWiVQTVlBeHM,745
arch/tests/test_data.py,sha256=yD6ACDF_flyYEqTocpDQRB3kWOy54LQ0g8d1jCN-VAk,521
arch/tests/test_examples.py,sha256=atPjw6pn_SV1qttchMbThL9wwqEiRkT5SL_SW9_31uU,2099
arch/tests/test_tester.py,sha256=YdjO-7fJKHZQcVVHNqw-1HNA7ZApppGvpdxTPT2VtkY,662
arch/tests/unitroot/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
arch/tests/unitroot/__pycache__/__init__.cpython-312.pyc,,
arch/tests/unitroot/__pycache__/cointegration_data.cpython-312.pyc,,
arch/tests/unitroot/__pycache__/test_dynamic_ols.cpython-312.pyc,,
arch/tests/unitroot/__pycache__/test_engle_granger.cpython-312.pyc,,
arch/tests/unitroot/__pycache__/test_fmols_ccr.cpython-312.pyc,,
arch/tests/unitroot/__pycache__/test_phillips_ouliaris.cpython-312.pyc,,
arch/tests/unitroot/__pycache__/test_unitroot.cpython-312.pyc,,
arch/tests/unitroot/cointegration_data.py,sha256=C1wDtpLdDSQhpKwVRnmKnFR-pSuGWWqaODTa8anInBo,2343
arch/tests/unitroot/data/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
arch/tests/unitroot/data/__pycache__/__init__.cpython-312.pyc,,
arch/tests/unitroot/data/zivot-andrews.csv,sha256=lUMmeB9GpibYAT9AHdPAmH_eYoKdA1FLEJkdI6NLa2g,233091
arch/tests/unitroot/test_dynamic_ols.py,sha256=md4HOmM2SxfeKKFOqPH2H6mE6ICWjhlOaYsqvCRbHzs,7470
arch/tests/unitroot/test_engle_granger.py,sha256=Jl1sXnLIQ3G6MYx4HHZyiYHsbfPL1quAm_qrT-Cz2E8,9244
arch/tests/unitroot/test_fmols_ccr.py,sha256=rpj06IlQEVrMaIfTYH5NI-ks_9aUN3eU3biSTKypF6Y,13671
arch/tests/unitroot/test_phillips_ouliaris.py,sha256=DXP-C0vXcNhfPKNBLJjjiC97kcaWAvkGnzAHUNhKdNQ,6250
arch/tests/unitroot/test_unitroot.py,sha256=A7_6eafP12B4gUkgwtBn69BXkv1_jVe2eCOsg7mhrAU,26139
arch/tests/univariate/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
arch/tests/univariate/__pycache__/__init__.cpython-312.pyc,,
arch/tests/univariate/__pycache__/test_arch_in_mean.cpython-312.pyc,,
arch/tests/univariate/__pycache__/test_base.cpython-312.pyc,,
arch/tests/univariate/__pycache__/test_distribution.cpython-312.pyc,,
arch/tests/univariate/__pycache__/test_forecast.cpython-312.pyc,,
arch/tests/univariate/__pycache__/test_mean.cpython-312.pyc,,
arch/tests/univariate/__pycache__/test_moment.cpython-312.pyc,,
arch/tests/univariate/__pycache__/test_recursions.cpython-312.pyc,,
arch/tests/univariate/__pycache__/test_rescale.cpython-312.pyc,,
arch/tests/univariate/__pycache__/test_variance_forecasting.cpython-312.pyc,,
arch/tests/univariate/__pycache__/test_volatility.cpython-312.pyc,,
arch/tests/univariate/test_arch_in_mean.py,sha256=8rE23nclF6kxVzNFFXQOW6oyGHheDkl2UyUMKEqk2a4,5850
arch/tests/univariate/test_base.py,sha256=-Xupx_9Ya426Ru37vXgi_hGeBmtyRgIk_d1BlEjlZLI,298
arch/tests/univariate/test_distribution.py,sha256=uVL8gN0hJu_6QYYs2g1zJCoKz7N207zsjJ6Yxl1IJZE,7588
arch/tests/univariate/test_forecast.py,sha256=nwNtqdTpvY609c6BjA26_9InlyPau1eY5GofbBzKk1s,44580
arch/tests/univariate/test_mean.py,sha256=RzyCaNjB9PVVTZOWoBLRTEM98WsUX7qOofmQrgfNaqU,55516
arch/tests/univariate/test_moment.py,sha256=VcyYjFa3kkK-eteP6sSqMVSHC1phNH5_NrjAVNEGbw4,2488
arch/tests/univariate/test_recursions.py,sha256=xNR7zU2vyqZkP11BcVkDor8hcobZ7ZC1OGwhN9tYun0,47544
arch/tests/univariate/test_rescale.py,sha256=GAAwJHRpPYmsg0jt8JeVeQAwFFC8PCOfdfiJ5nqbncg,2103
arch/tests/univariate/test_variance_forecasting.py,sha256=p2Y9ubuY9uh9EuARvargnFAAfpb154_ZqPL4ZEt6fvc,84079
arch/tests/univariate/test_volatility.py,sha256=5FOR6-AhY2u6wE1dUllyxsje2ehMgTOXC9RMvP8QOSo,62011
arch/tests/utility/__init__.py,sha256=dM7sjnymK3tGieAFDMGyevzqf75xJvO1oRNkkqD1DH4,22
arch/tests/utility/__pycache__/__init__.cpython-312.pyc,,
arch/tests/utility/__pycache__/test_array.cpython-312.pyc,,
arch/tests/utility/__pycache__/test_cov.cpython-312.pyc,,
arch/tests/utility/__pycache__/test_io.cpython-312.pyc,,
arch/tests/utility/__pycache__/test_timeseries.cpython-312.pyc,,
arch/tests/utility/__pycache__/test_utility.cpython-312.pyc,,
arch/tests/utility/test_array.py,sha256=vltlfoLyliGMfV7eRWRfGrEX8Ai2L5EyflbXHeo98js,9492
arch/tests/utility/test_cov.py,sha256=s8lTfQhARqFkXPsYnC6j4A-WAeCDqGQxKWKIGLvlt-I,2434
arch/tests/utility/test_io.py,sha256=zXWMoEZlaKSyYQ3uu9em8enE_DeQK7fupOSb7w7qLeA,598
arch/tests/utility/test_timeseries.py,sha256=zWqVuYFD9Jld_xAxQ7G7djPSbZ9l0Hl3_n26FZ4LOUs,3708
arch/tests/utility/test_utility.py,sha256=8v6v5BWudf0GQAIMu_hk2EhV2B82G4rxJYJe95cOVg8,197
arch/typing.py,sha256=Azh_uQs2lS3LxjZXUX7AcWJ40SPJiazoKK246rBsFXQ,1897
arch/unitroot/__init__.py,sha256=SiXpAPmyQH8rDxeELsJ0fTxUyoVvQ2-C2c4JJxYhwIo,435
arch/unitroot/__pycache__/__init__.cpython-312.pyc,,
arch/unitroot/__pycache__/_engle_granger.cpython-312.pyc,,
arch/unitroot/__pycache__/_phillips_ouliaris.cpython-312.pyc,,
arch/unitroot/__pycache__/_shared.cpython-312.pyc,,
arch/unitroot/__pycache__/cointegration.cpython-312.pyc,,
arch/unitroot/__pycache__/unitroot.cpython-312.pyc,,
arch/unitroot/_engle_granger.py,sha256=05LCfu24nvfz1EirBXetI4dFQwe80_FvWeORfCkAo3E,9946
arch/unitroot/_phillips_ouliaris.py,sha256=YCaMSAlilIXMje0dRYMRBBIvJfyJnf69GBgFmFH9vEI,17085
arch/unitroot/_shared.py,sha256=ULviVaZOFVzA0l-cvgKJJyuJ-RorHtVounglCEAAmEo,7922
arch/unitroot/cointegration.py,sha256=w7qEXT8k66BxKpfBI-eGb1kWHRY7MdJeXtnYIZe-Wu0,42163
arch/unitroot/critical_values/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
arch/unitroot/critical_values/__pycache__/__init__.cpython-312.pyc,,
arch/unitroot/critical_values/__pycache__/dfgls.cpython-312.pyc,,
arch/unitroot/critical_values/__pycache__/dickey_fuller.cpython-312.pyc,,
arch/unitroot/critical_values/__pycache__/engle_granger.cpython-312.pyc,,
arch/unitroot/critical_values/__pycache__/kpss.cpython-312.pyc,,
arch/unitroot/critical_values/__pycache__/phillips_ouliaris.cpython-312.pyc,,
arch/unitroot/critical_values/__pycache__/zivot_andrews.cpython-312.pyc,,
arch/unitroot/critical_values/dfgls.py,sha256=778DtswQQ53iVBecGBeklqg59aoNDJP8xGBtDCeXPUA,1327
arch/unitroot/critical_values/dickey_fuller.py,sha256=ywSHvpXdbYGoa6Gy0LpWlz8RQywn6XXssTpcDORK1So,12701
arch/unitroot/critical_values/engle_granger.py,sha256=qQAJm5m7j_o7A6N-PTrX8RjTJtdKnaCRXEQDkL2DRks,23346
arch/unitroot/critical_values/kpss.py,sha256=NTNGjEgYACYCTfEbACVZ-X9bNkb5qTj4_oV3V-DmuSg,3539
arch/unitroot/critical_values/phillips_ouliaris.py,sha256=QYQ_tX1iUQZhtDu5Ko24xZVe2qelKN7kTavx4WFF_dA,93751
arch/unitroot/critical_values/simulation/__pycache__/adf_simulation.cpython-312.pyc,,
arch/unitroot/critical_values/simulation/__pycache__/adf_z_critical_values_simulation.cpython-312.pyc,,
arch/unitroot/critical_values/simulation/__pycache__/adf_z_critical_values_simulation_joblib.cpython-312.pyc,,
arch/unitroot/critical_values/simulation/__pycache__/adf_z_critical_values_simulation_large_cluster.cpython-312.pyc,,
arch/unitroot/critical_values/simulation/__pycache__/adf_z_simlation_process.cpython-312.pyc,,
arch/unitroot/critical_values/simulation/__pycache__/dfgls_critical_values_simulation.cpython-312.pyc,,
arch/unitroot/critical_values/simulation/__pycache__/dfgls_simulation_process.cpython-312.pyc,,
arch/unitroot/critical_values/simulation/__pycache__/engle_granger_simulation.cpython-312.pyc,,
arch/unitroot/critical_values/simulation/__pycache__/engle_granger_simulation_process.cpython-312.pyc,,
arch/unitroot/critical_values/simulation/__pycache__/kpss_critical_values_simulation.cpython-312.pyc,,
arch/unitroot/critical_values/simulation/__pycache__/kpss_simulation_process.cpython-312.pyc,,
arch/unitroot/critical_values/simulation/__pycache__/phillips-ouliaris-simulation-process.cpython-312.pyc,,
arch/unitroot/critical_values/simulation/__pycache__/phillips-ouliaris-simulation.cpython-312.pyc,,
arch/unitroot/critical_values/simulation/__pycache__/phillips_ouliaris.cpython-312.pyc,,
arch/unitroot/critical_values/simulation/__pycache__/shared.cpython-312.pyc,,
arch/unitroot/critical_values/simulation/adf_simulation.py,sha256=HjgGKQf1kNvnt0DHwYiuCnlRdTSpGIRo9Y5HMjXLpdU,2243
arch/unitroot/critical_values/simulation/adf_z_critical_values_simulation.py,sha256=c7dVlyuJDifBL_K3RQZcBxrYq1Mc4aYCTLieRkIBlHg,3487
arch/unitroot/critical_values/simulation/adf_z_critical_values_simulation_joblib.py,sha256=IHmeLowfskB8McX3gMsatePLdpWW0T0MyMHZhZWtr24,3972
arch/unitroot/critical_values/simulation/adf_z_critical_values_simulation_large_cluster.py,sha256=mkAp58mf3ado5yVKyeF0Q6n0NmnSrQ9aIsqGAPLX7qE,4432
arch/unitroot/critical_values/simulation/adf_z_simlation_process.py,sha256=CF7fQFdwqYDboAjfKEQFiHpwBwk9JkkJ8couvTdfV3U,2402
arch/unitroot/critical_values/simulation/dfgls_critical_values_simulation.py,sha256=P0WmDP3QrHAAzbv01ck77Kq3aZSdX-VrIWvzhkcKBAY,4463
arch/unitroot/critical_values/simulation/dfgls_simulation_process.py,sha256=Pavm2e0zVv17FiBZbEYfikP0xzJZi0EM4aePPv2r9tY,4493
arch/unitroot/critical_values/simulation/engle_granger_simulation.py,sha256=NRH8Eab4OyQFgbeXeHVPxZK5LxmVOChvKUsUfLCvBPc,7744
arch/unitroot/critical_values/simulation/engle_granger_simulation_process.py,sha256=uRgjnxE8E52_8rXzS48wHkIGqNuriWrd4VH5BMVu5w8,4838
arch/unitroot/critical_values/simulation/kpss_critical_values_simulation.py,sha256=WfMH7znOo3_RNs-NxQMr1YEye92eosWQ_Xz9eoLtwBE,3275
arch/unitroot/critical_values/simulation/kpss_simulation_process.py,sha256=o_80cucQ-DHFfTN8mS-KeZNy6_NmA51ikJZUW6aJIrA,1777
arch/unitroot/critical_values/simulation/phillips-ouliaris-simulation-process.py,sha256=vH1ydDJLnwrmZ7zdpbvefziEaWmnJ8A5KjHyDHPERk4,10926
arch/unitroot/critical_values/simulation/phillips-ouliaris-simulation.py,sha256=XYbnC7n9r3GIkiN8tKWRYHEq2BTx2rf3vsSuXRIw0So,13626
arch/unitroot/critical_values/simulation/phillips_ouliaris.py,sha256=W6W7Sw95_VMTn5iSMwWdGzraiYXOMlCyl_UwXBdY8xQ,784
arch/unitroot/critical_values/simulation/shared.py,sha256=vV0kZTTdmtzCdocyLyaYZ4jpreMc1g7WKt9M7Irz1kU,4590
arch/unitroot/critical_values/zivot_andrews.py,sha256=UCGGzFh2yAywecIqTEbm2aIiBlmGSGgW9SWTkJP0-5A,4034
arch/unitroot/unitroot.py,sha256=2CLVrSgRdXW3-Vw-1cG2LjU_9zckftK85FVkyeT9thA,72793
arch/univariate/__init__.py,sha256=GvV4CoHhbRv1VSQht8-XxZewYugQfDQazivAcZ5Qyw4,1178
arch/univariate/__pycache__/__init__.cpython-312.pyc,,
arch/univariate/__pycache__/base.cpython-312.pyc,,
arch/univariate/__pycache__/distribution.cpython-312.pyc,,
arch/univariate/__pycache__/mean.cpython-312.pyc,,
arch/univariate/__pycache__/recursions_python.cpython-312.pyc,,
arch/univariate/__pycache__/volatility.cpython-312.pyc,,
arch/univariate/base.py,sha256=chkwfAfxQrgqyXiRQm8p9eLW6r9S40XEk2oGXXMhjPE,78039
arch/univariate/distribution.py,sha256=7Su3aUwvq82fcAHmsHd6QVtOHllW0HiEgJnKgYqjDkg,42717
arch/univariate/mean.py,sha256=4p5LkLbldQetXdPFpeUXfaY-TeC3ocHllw1eoWoVVv0,72704
arch/univariate/recursions.cp312-win_amd64.pyd,sha256=FUtjX_NXt-LolQs3337sTF92N7l5pfldhOhkEBpOvug,384000
arch/univariate/recursions.pyi,sha256=ZIV8tknoh2GDHcPwW2JDWVj1Rvg4DIYmo_4ybHv7MO4,3991
arch/univariate/recursions_python.py,sha256=Aff-XHFfQZ00kYT1lZs7CObwjINzOIaCNyHtqNU9fes,35002
arch/univariate/volatility.py,sha256=LmXz-_c3JSBL-_b6P4HEzJgNkmSoWLeckR2gpWpD6IE,127490
arch/utility/__init__.py,sha256=zs94q7IIkoEzkxaSqpkz2eh1ud2pCM71d2MXFlwblIY,1603
arch/utility/__pycache__/__init__.cpython-312.pyc,,
arch/utility/__pycache__/array.cpython-312.pyc,,
arch/utility/__pycache__/cov.cpython-312.pyc,,
arch/utility/__pycache__/exceptions.cpython-312.pyc,,
arch/utility/__pycache__/io.cpython-312.pyc,,
arch/utility/__pycache__/testing.cpython-312.pyc,,
arch/utility/__pycache__/timeseries.cpython-312.pyc,,
arch/utility/array.py,sha256=C0cCvcsrlzewvHWJpbWbBsU7GyUe-3Y5Tbod92pSYO0,9835
arch/utility/cov.py,sha256=ASiR36YOfKhlaWGHqbAm8fFSFCsJ4kujbtT3OZRj0Uk,1838
arch/utility/exceptions.py,sha256=T9gjImuK1m1bAAh1ZlS4R5u1ndB5HbIQQAf-Ff-ro68,2384
arch/utility/io.py,sha256=dzCA62HpqZLjXJZljHm5VEo12H4F0yZS0Dz7V3des4A,662
arch/utility/testing.py,sha256=XTO9J5j2AWF97QUqRa1PaNXZ2JQsNMWQt8ZiEBzACsE,2005
arch/utility/timeseries.py,sha256=QqWugAX75Q4FBLqe016R2YiJBAZltnQi-fEC2cSPmLo,5667
ci/__pycache__/performance.cpython-312.pyc,,
ci/performance.py,sha256=SecAEOaNqGG9FR4SPdzZT6yiGZs0PO0REYWq8wRshDk,511
doc/source/__pycache__/conf.cpython-312.pyc,,
doc/source/conf.py,sha256=E6VVzmQIabHCw8z5WyQ4MwfH3LJvUKtXgDn--uWGePY,11890
doc/source/images/__pycache__/favicon.cpython-312.pyc,,
doc/source/images/__pycache__/hero.cpython-312.pyc,,
doc/source/images/favicon.py,sha256=d4i-J4dz3neFFicdtzQV53ZFce9Efclrq8Hi7sONT4g,2121
doc/source/images/hero.py,sha256=uHyJUBRcXDJaVFgtFl60_v8g0pDPcb8mldasSZesiUQ,1431
