"""
Critical values produced by phillips-ouliaris-simulation.py

Z-type statistics with trend n based on 25,000,000 simulations
Z-type statistics with trend c based on 25,000,000 simulations
Z-type statistics with trend ct based on 25,000,000 simulations
Z-type statistics with trend ctt based on 25,000,000 simulations
P-type statistics with trend n based on 11,000,000 simulations
P-type statistics with trend c based on 13,250,000 simulations
P-type statistics with trend ct based on 11,250,000 simulations
P-type statistics with trend ctt based on 13,250,000 simulations
"""

from math import inf

CV_PARAMETERS = {
    ("Za", "n", 1): {
        1: [-13.70663, 52.58043, -334.09635, 3399.67403],
        5: [-8.04208, 18.43377, -48.70429, 0.0],
        10: [-5.71462, 9.45685, 11.88392, 0.0],
    },
    ("Za", "n", 2): {
        1: [-22.95117, 144.38863, -817.82717, 4747.97432],
        5: [-15.84358, 70.12077, -281.45532, 1445.38223],
        10: [-12.68278, 45.39971, -130.25376, 0.0],
    },
    ("Za", "n", 3): {
        1: [-30.43062, 256.63022, -1565.11653, 5938.79717],
        5: [-22.36019, 142.45683, -648.07337, 0.0],
        10: [-18.66532, 101.70211, -422.38002, 0.0],
    },
    ("Za", "n", 4): {
        1: [-37.19862, 392.96046, -3133.68156, 17626.38038],
        5: [-28.34843, 237.35694, -1628.3452, 9206.51627],
        10: [-24.22399, 177.0462, -1074.14068, 5363.13208],
    },
    ("Za", "n", 5): {
        1: [-43.57319, 552.85757, -5496.96327, 39273.37984],
        5: [-34.0381, 347.4872, -2753.90307, 15172.87495],
        10: [-29.54506, 269.27351, -2088.74024, 13997.82293],
    },
    ("Za", "n", 6): {
        1: [-49.66985, 724.47759, -7769.12387, 49346.97303],
        5: [-39.54215, 478.13727, -4544.9269, 29077.32773],
        10: [-34.71892, 376.76501, -3281.47497, 20009.38781],
    },
    ("Za", "n", 7): {
        1: [-55.5792, 917.26086, -11086.49953, 77907.46395],
        5: [-44.89541, 622.16906, -6679.11424, 46030.71357],
        10: [-39.77643, 499.31178, -4944.52095, 32514.06133],
    },
    ("Za", "n", 8): {
        1: [-61.35558, 1133.68096, -15650.97247, 128911.49908],
        5: [-50.15684, 783.99097, -9351.83981, 67999.21095],
        10: [-44.75818, 638.12594, -7096.19031, 50271.38454],
    },
    ("Za", "n", 9): {
        1: [-66.99564, 1357.0669, -20006.36723, 165789.40825],
        5: [-55.33049, 961.95492, -12709.38913, 100394.4466],
        10: [-49.6694, 790.49734, -9620.65593, 70624.12199],
    },
    ("Za", "n", 10): {
        1: [-72.55975, 1606.24937, -26104.26824, 241500.91221],
        5: [-60.43707, 1156.32194, -16891.13217, 149077.43046],
        10: [-54.53841, 962.37937, -13200.71643, 112452.33847],
    },
    ("Za", "n", 11): {
        1: [-78.0165, 1861.50683, -31883.67229, 297167.08289],
        5: [-65.47687, 1362.63836, -21291.92001, 192628.80622],
        10: [-59.35366, 1146.45643, -17112.97887, 154454.78104],
    },
    ("Za", "n", 12): {
        1: [-83.42088, 2141.89283, -39569.42675, 395696.67402],
        5: [-70.47307, 1584.8878, -26523.50762, 250797.62979],
        10: [-64.1243, 1341.43854, -21366.7718, 199012.67024],
    },
    ("Za", "n", 13): {
        1: [-88.77242, 2439.12566, -48328.29306, 519973.72273],
        5: [-75.4304, 1827.30039, -33242.23476, 347006.38224],
        10: [-68.86929, 1553.60394, -26592.32589, 263943.25856],
    },
    ("Zt", "n", 1): {
        1: [-2.56682, -4.49663, -33.37287, 330.26465],
        5: [-1.94119, -2.23448, -3.42815, 0.0],
        10: [-1.61676, -1.42924, 2.68464, -125.19667],
    },
    ("Zt", "n", 2): {
        1: [-3.34447, -9.50408, -41.80773, 0.0],
        5: [-2.76218, -5.52773, -18.75475, 0.0],
        10: [-2.45923, -4.07018, -7.47109, 0.0],
    },
    ("Zt", "n", 3): {
        1: [-3.86118, -14.17713, -33.7365, 0.0],
        5: [-3.29665, -8.78593, -18.30547, 0.0],
        10: [-3.0026, -6.63529, -12.24551, 0.0],
    },
    ("Zt", "n", 4): {
        1: [-4.27568, -18.34025, -62.47509, 0.0],
        5: [-3.72134, -11.74671, -39.63135, 0.0],
        10: [-3.43218, -9.12577, -26.77711, 0.0],
    },
    ("Zt", "n", 5): {
        1: [-4.63263, -22.19505, -103.31361, 595.78261],
        5: [-4.08459, -14.92665, -42.35806, 0.0],
        10: [-3.79857, -11.68845, -43.71663, 374.20393],
    },
    ("Zt", "n", 6): {
        1: [-4.95038, -26.48227, -96.37322, 0.0],
        5: [-4.40767, -18.01057, -55.31091, 0.0],
        10: [-4.12395, -14.46214, -39.03082, 0.0],
    },
    ("Zt", "n", 7): {
        1: [-5.2397, -30.64544, -122.28774, 0.0],
        5: [-4.70081, -21.29746, -67.52762, 0.0],
        10: [-4.41907, -17.29066, -47.09197, 0.0],
    },
    ("Zt", "n", 8): {
        1: [-5.50783, -34.92027, -116.03026, 0.0],
        5: [-4.97212, -24.7081, -59.3081, 0.0],
        10: [-4.69177, -20.20422, -45.21211, 0.0],
    },
    ("Zt", "n", 9): {
        1: [-5.75789, -39.42148, -117.30551, -1252.62702],
        5: [-5.22528, -28.08394, -66.37919, 0.0],
        10: [-4.94601, -23.31114, -33.36175, -485.40216],
    },
    ("Zt", "n", 10): {
        1: [-5.99425, -43.69333, -136.43079, 0.0],
        5: [-5.46386, -31.50911, -82.24939, 0.0],
        10: [-5.18594, -26.15976, -56.72523, 0.0],
    },
    ("Zt", "n", 11): {
        1: [-6.21762, -48.40222, -106.11966, -2762.72012],
        5: [-5.68949, -35.19977, -63.34933, -1093.31733],
        10: [-5.41284, -29.21854, -61.77846, 0.0],
    },
    ("Zt", "n", 12): {
        1: [-6.43081, -52.92157, -114.57805, -3255.91108],
        5: [-5.90474, -38.88294, -62.08863, -1353.31841],
        10: [-5.62856, -32.566, -48.6554, 0.0],
    },
    ("Zt", "n", 13): {
        1: [-6.63549, -57.44714, -118.41577, -3907.39299],
        5: [-6.11089, -42.41818, -79.9238, -1168.12396],
        10: [-5.8351, -35.98536, -27.82918, -1356.57746],
    },
    ("Za", "c", 1): {
        1: [-20.62307, 95.87943, -238.70793, 0.0],
        5: [-14.09016, 41.70218, -31.04505, 0.0],
        10: [-11.24682, 24.67707, 17.20067, -950.63449],
    },
    ("Za", "c", 2): {
        1: [-28.26682, 200.26369, -966.05867, 0.0],
        5: [-20.60313, 104.13205, -333.97506, 0.0],
        10: [-17.12966, 70.71077, -159.47083, 0.0],
    },
    ("Za", "c", 3): {
        1: [-35.17168, 325.53604, -2104.3758, 7779.36447],
        5: [-26.65203, 187.96533, -942.9263, 2949.70249],
        10: [-22.70469, 135.94049, -536.11698, 0.0],
    },
    ("Za", "c", 4): {
        1: [-41.65885, 473.94848, -3961.63091, 21152.00409],
        5: [-32.41659, 291.93399, -2005.854, 9674.20893],
        10: [-28.07236, 220.68502, -1341.28198, 6027.59094],
    },
    ("Za", "c", 5): {
        1: [-47.84681, 641.66929, -6469.62598, 43045.48535],
        5: [-37.97017, 411.77264, -3394.15941, 18875.15323],
        10: [-33.28354, 320.4892, -2378.12484, 12128.38813],
    },
    ("Za", "c", 6): {
        1: [-53.83601, 826.66309, -9455.10391, 67814.70479],
        5: [-43.38688, 551.83103, -5490.50368, 37424.94335],
        10: [-38.38511, 437.30801, -3901.07209, 23946.3075],
    },
    ("Za", "c", 7): {
        1: [-59.66333, 1030.12987, -13130.4067, 100368.84408],
        5: [-48.68477, 706.15226, -7939.86338, 58185.99257],
        10: [-43.39363, 569.40086, -5871.41942, 40546.69972],
    },
    ("Za", "c", 8): {
        1: [-65.37183, 1254.11572, -17861.70832, 151861.05841],
        5: [-53.89269, 878.53701, -11204.56699, 93612.20614],
        10: [-48.33889, 718.13685, -8461.88453, 67125.11952],
    },
    ("Za", "c", 9): {
        1: [-70.95533, 1488.61963, -22824.76736, 200769.69044],
        5: [-59.01867, 1060.32937, -14382.57549, 117749.39925],
        10: [-53.21639, 877.61271, -11174.47003, 89477.10858],
    },
    ("Za", "c", 10): {
        1: [-76.47476, 1751.31091, -29938.47112, 302367.91634],
        5: [-64.094, 1266.15306, -19200.24802, 179581.13959],
        10: [-58.05216, 1056.39131, -14994.32795, 134971.64602],
    },
    ("Za", "c", 11): {
        1: [-81.90284, 2022.98526, -37063.74992, 392618.80074],
        5: [-69.10446, 1481.36726, -24090.94444, 233899.85644],
        10: [-62.84552, 1248.4252, -19233.21596, 182870.00061],
    },
    ("Za", "c", 12): {
        1: [-87.25538, 2303.05442, -44292.10383, 476062.71351],
        5: [-74.07328, 1713.76556, -29928.22535, 305470.86328],
        10: [-67.59832, 1453.38536, -24041.51631, 239209.66988],
    },
    ("Za", "c", 13): {
        1: [-92.56861, 2609.22694, -53933.41146, 631289.60155],
        5: [-79.00034, 1959.73715, -36545.62929, 395123.54882],
        10: [-72.32353, 1674.95205, -29878.18514, 319751.39436],
    },
    ("Zt", "c", 1): {
        1: [-3.42985, -11.21462, -18.68041, -345.02699],
        5: [-2.86123, -6.7958, -9.39414, -141.00669],
        10: [-2.56643, -5.10802, -4.92151, -120.50624],
    },
    ("Zt", "c", 2): {
        1: [-3.89678, -14.89947, -43.59473, -260.07485],
        5: [-3.3359, -9.5675, -17.85839, -137.85381],
        10: [-3.04433, -7.37525, -11.07111, -99.16409],
    },
    ("Zt", "c", 3): {
        1: [-4.29395, -18.67042, -71.97128, 0.0],
        5: [-3.74063, -12.3027, -35.6017, 0.0],
        10: [-3.45215, -9.71, -19.32006, 0.0],
    },
    ("Zt", "c", 4): {
        1: [-4.64325, -22.76383, -81.49609, 0.0],
        5: [-4.09629, -15.26025, -43.17831, 0.0],
        10: [-3.81051, -12.10025, -34.79855, 0.0],
    },
    ("Zt", "c", 5): {
        1: [-4.95741, -26.72877, -106.85007, 0.0],
        5: [-4.41529, -18.29324, -61.48141, 0.0],
        10: [-4.13174, -14.76132, -40.26481, 0.0],
    },
    ("Zt", "c", 6): {
        1: [-5.24539, -30.80439, -133.19236, 0.0],
        5: [-4.70724, -21.37289, -80.50928, 424.16579],
        10: [-4.42531, -17.44111, -51.29961, 0.0],
    },
    ("Zt", "c", 7): {
        1: [-5.51192, -35.17684, -128.67493, 0.0],
        5: [-4.97715, -24.69532, -87.36058, 386.26042],
        10: [-4.69667, -20.2491, -64.18839, 415.96465],
    },
    ("Zt", "c", 8): {
        1: [-5.76195, -39.43233, -138.42924, 0.0],
        5: [-5.22953, -27.98333, -103.38469, 603.35123],
        10: [-4.95043, -23.10689, -76.41775, 650.30004],
    },
    ("Zt", "c", 9): {
        1: [-5.99742, -43.80823, -145.51316, -1046.52972],
        5: [-5.46704, -31.67523, -84.41552, 0.0],
        10: [-5.18914, -26.23005, -71.84303, 447.02202],
    },
    ("Zt", "c", 10): {
        1: [-6.22132, -47.85207, -194.22875, 0.0],
        5: [-5.69294, -34.94509, -117.80642, 0.0],
        10: [-5.41563, -29.23969, -84.64873, 657.47046],
    },
    ("Zt", "c", 11): {
        1: [-6.43476, -52.04307, -245.02933, 0.0],
        5: [-5.90751, -38.65371, -112.89684, 0.0],
        10: [-5.63133, -32.39074, -89.66671, 649.20105],
    },
    ("Zt", "c", 12): {
        1: [-6.63803, -56.96635, -217.91489, 0.0],
        5: [-6.11319, -42.18154, -133.01218, 0.0],
        10: [-5.83765, -35.61695, -92.71352, 0.0],
    },
    ("Zt", "c", 13): {
        1: [-6.83418, -61.44253, -250.90477, 0.0],
        5: [-6.31044, -46.10853, -110.76789, 0.0],
        10: [-6.03572, -38.90718, -98.91322, 0.0],
    },
    ("Za", "ct", 1): {
        1: [-29.36173, 197.30769, -802.86967, 0.0],
        5: [-21.71069, 102.00963, -263.48383, 0.0],
        10: [-18.24402, 68.57554, -128.46896, 0.0],
    },
    ("Za", "ct", 2): {
        1: [-35.80504, 322.32048, -1982.79047, 7026.1697],
        5: [-27.2946, 184.79102, -839.80254, 0.0],
        10: [-23.35453, 133.62742, -509.16427, 0.0],
    },
    ("Za", "ct", 3): {
        1: [-42.04159, 469.35388, -3818.05723, 20491.53616],
        5: [-32.80339, 286.38804, -1797.98941, 7379.31048],
        10: [-28.4642, 215.97974, -1193.9308, 4707.22013],
    },
    ("Za", "ct", 4): {
        1: [-48.09314, 634.73249, -6084.11708, 36009.7334],
        5: [-38.21888, 406.25471, -3163.52388, 15691.87937],
        10: [-33.53217, 314.94129, -2168.50345, 9559.93145],
    },
    ("Za", "ct", 5): {
        1: [-53.99866, 822.20362, -9376.36213, 68796.54353],
        5: [-43.53916, 542.81089, -5039.65931, 30268.26286],
        10: [-38.5412, 429.83698, -3545.56115, 18323.13282],
    },
    ("Za", "ct", 6): {
        1: [-59.77585, 1024.62546, -12942.69604, 98829.10417],
        5: [-48.79062, 698.48149, -7549.03119, 51855.9266],
        10: [-43.5035, 562.35308, -5496.83223, 34157.8746],
    },
    ("Za", "ct", 7): {
        1: [-65.44452, 1245.84886, -17421.79526, 144034.0905],
        5: [-53.96636, 870.2052, -10723.56641, 84720.20161],
        10: [-48.41429, 710.8102, -8063.99245, 60138.94435],
    },
    ("Za", "ct", 8): {
        1: [-71.01902, 1484.56889, -22794.72978, 203961.86403],
        5: [-59.07499, 1054.50943, -14138.41118, 114652.44621],
        10: [-53.27434, 872.47544, -10948.00299, 86094.97597],
    },
    ("Za", "ct", 9): {
        1: [-76.50181, 1735.14389, -28503.46805, 264138.51401],
        5: [-64.12958, 1255.10714, -18318.97033, 157160.66904],
        10: [-58.09096, 1047.06078, -14245.49962, 115943.98114],
    },
    ("Za", "ct", 10): {
        1: [-81.92961, 2010.59514, -36042.80615, 367564.97788],
        5: [-69.14013, 1474.78134, -23703.18263, 226821.08019],
        10: [-62.87812, 1240.44595, -18640.12252, 169015.87015],
    },
    ("Za", "ct", 11): {
        1: [-87.29077, 2300.80976, -44394.90147, 483960.69014],
        5: [-74.10413, 1709.78541, -29780.56623, 304368.91929],
        10: [-67.62598, 1447.89275, -23674.46051, 231069.30373],
    },
    ("Za", "ct", 12): {
        1: [-92.60071, 2611.65632, -54485.05863, 651265.77568],
        5: [-79.02901, 1960.30914, -37019.42983, 415187.2111],
        10: [-72.34973, 1671.95535, -29794.16656, 320520.89273],
    },
    ("Za", "ct", 13): {
        1: [-97.86031, 2932.75044, -64743.47, 805903.986],
        5: [-83.91654, 2221.78299, -44461.29192, 516961.26843],
        10: [-77.03991, 1905.83375, -36111.44391, 403542.18493],
    },
    ("Zt", "ct", 1): {
        1: [-3.95947, -16.01912, -64.83551, 0.0],
        5: [-3.41062, -10.70715, -26.70909, -134.72127],
        10: [-3.12687, -8.52747, -18.05766, -99.12532],
    },
    ("Zt", "ct", 2): {
        1: [-4.32794, -19.69705, -77.14504, 0.0],
        5: [-3.78083, -13.25547, -36.28472, 0.0],
        10: [-3.49638, -10.58813, -24.93345, 0.0],
    },
    ("Zt", "ct", 3): {
        1: [-4.66312, -23.38395, -100.64981, 0.0],
        5: [-4.11896, -16.04047, -46.89642, 0.0],
        10: [-3.83496, -12.97939, -28.27961, 0.0],
    },
    ("Zt", "ct", 4): {
        1: [-4.97002, -27.13941, -130.85459, 0.0],
        5: [-4.42903, -18.92798, -61.67676, 0.0],
        10: [-4.14629, -15.43596, -36.66773, 0.0],
    },
    ("Zt", "ct", 5): {
        1: [-5.25323, -31.11413, -161.04393, 783.21967],
        5: [-4.71524, -22.05398, -71.32856, 0.0],
        10: [-4.43393, -18.08376, -43.61929, 0.0],
    },
    ("Zt", "ct", 6): {
        1: [-5.51761, -35.25119, -168.33132, 0.0],
        5: [-4.98247, -25.13872, -89.62619, 362.25407],
        10: [-4.70227, -20.81954, -51.66455, 0.0],
    },
    ("Zt", "ct", 7): {
        1: [-5.76539, -39.74476, -149.73476, 0.0],
        5: [-5.23305, -28.4902, -94.89255, 0.0],
        10: [-4.95413, -23.64969, -58.83023, 0.0],
    },
    ("Zt", "ct", 8): {
        1: [-6.00004, -43.88576, -189.55578, 0.0],
        5: [-5.46962, -31.9938, -89.02389, 0.0],
        10: [-5.19185, -26.60323, -65.62618, 0.0],
    },
    ("Zt", "ct", 9): {
        1: [-6.22278, -48.28585, -195.38289, 0.0],
        5: [-5.69439, -35.51606, -94.97141, 0.0],
        10: [-5.41743, -29.72825, -64.88499, 0.0],
    },
    ("Zt", "ct", 10): {
        1: [-6.43545, -52.77234, -200.79953, 0.0],
        5: [-5.90892, -39.03089, -107.20103, 0.0],
        10: [-5.63279, -32.81285, -71.91141, 0.0],
    },
    ("Zt", "ct", 11): {
        1: [-6.63939, -56.88162, -264.06155, 0.0],
        5: [-6.1144, -42.43975, -130.91552, 0.0],
        10: [-5.83879, -35.904, -89.43013, 0.0],
    },
    ("Zt", "ct", 12): {
        1: [-6.83537, -61.30423, -301.01724, 0.0],
        5: [-6.31174, -45.99895, -160.0026, 1160.94458],
        10: [-6.03674, -39.14028, -95.06468, 0.0],
    },
    ("Zt", "ct", 13): {
        1: [-7.02446, -65.61399, -356.86978, 3103.43823],
        5: [-6.50168, -49.76837, -164.25447, 1060.43465],
        10: [-6.22739, -42.461, -100.86098, 0.0],
    },
    ("Za", "ctt", 1): {
        1: [-36.60289, 306.36376, -1626.30181, 6428.63693],
        5: [-28.11176, 170.02174, -525.75571, 0.0],
        10: [-24.18354, 119.48768, -224.35408, 0.0],
    },
    ("Za", "ctt", 2): {
        1: [-42.62015, 452.57469, -3364.88099, 18570.73358],
        5: [-33.39657, 270.78574, -1334.03931, 3324.71543],
        10: [-29.0694, 201.15652, -758.59533, 0.0],
    },
    ("Za", "ctt", 3): {
        1: [-48.51117, 614.07751, -5307.68348, 27810.66468],
        5: [-38.66143, 392.05126, -2741.87562, 12318.2724],
        10: [-33.98134, 301.01013, -1738.73251, 5372.43316],
    },
    ("Za", "ctt", 4): {
        1: [-54.31627, 802.83265, -8604.5467, 60376.16321],
        5: [-43.87778, 530.0097, -4647.03155, 26898.61809],
        10: [-38.88159, 417.55561, -3222.12927, 16591.54672],
    },
    ("Za", "ctt", 5): {
        1: [-60.00698, 1003.97943, -12085.049, 89220.12809],
        5: [-49.04152, 684.13619, -7054.03703, 47057.13823],
        10: [-43.75957, 549.24911, -5084.37852, 30633.30677],
    },
    ("Za", "ctt", 6): {
        1: [-65.62374, 1225.97811, -16478.15421, 130135.62648],
        5: [-54.15814, 854.62662, -10030.24945, 73643.93349],
        10: [-48.60767, 696.23807, -7451.6645, 50634.08349],
    },
    ("Za", "ctt", 7): {
        1: [-71.16183, 1469.02351, -22245.48496, 201077.92479],
        5: [-59.23151, 1043.13659, -13907.46135, 117209.01232],
        10: [-53.43088, 859.68462, -10531.8869, 82973.4666],
    },
    ("Za", "ctt", 8): {
        1: [-76.62106, 1724.2939, -28329.12939, 269430.3891],
        5: [-64.24864, 1243.17406, -18006.15335, 157623.78836],
        10: [-58.21652, 1037.30056, -14094.2595, 119505.84278],
    },
    ("Za", "ctt", 9): {
        1: [-82.02729, 1998.1072, -35698.29021, 370301.59578],
        5: [-69.23511, 1462.42742, -23323.08833, 226004.86952],
        10: [-62.97417, 1228.14116, -18240.38062, 166912.91986],
    },
    ("Za", "ctt", 10): {
        1: [-87.36909, 2284.68443, -43482.37098, 468046.3373],
        5: [-74.17891, 1695.44805, -29214.43644, 299861.85986],
        10: [-67.70761, 1436.26195, -23324.89758, 231102.3721],
    },
    ("Za", "ctt", 11): {
        1: [-92.65299, 2587.7188, -52359.3402, 588058.41554],
        5: [-79.0891, 1941.80489, -35705.42434, 382055.99233],
        10: [-72.40907, 1654.75735, -28719.75695, 296124.21306],
    },
    ("Za", "ctt", 12): {
        1: [-97.9008, 2913.10131, -63331.18648, 772450.95099],
        5: [-83.97275, 2207.86614, -43750.23902, 506019.06902],
        10: [-77.09543, 1892.87557, -35554.46949, 398331.25964],
    },
    ("Za", "ctt", 13): {
        1: [-103.09732, 3244.29694, -73895.49782, 924746.45041],
        5: [-88.81575, 2480.93365, -51690.81789, 613770.49938],
        10: [-81.75283, 2139.88604, -42536.30862, 492953.96062],
    },
    ("Zt", "ctt", 1): {
        1: [-4.37214, -21.18132, -109.74361, 0.0],
        5: [-3.83256, -14.85713, -55.01483, 0.0],
        10: [-3.55319, -12.23815, -38.58411, 0.0],
    },
    ("Zt", "ctt", 2): {
        1: [-4.69292, -24.79476, -132.08773, 0.0],
        5: [-4.15373, -17.53079, -59.31958, 0.0],
        10: [-3.8733, -14.46826, -35.51145, -175.30797],
    },
    ("Zt", "ctt", 3): {
        1: [-4.99011, -28.70601, -132.96649, 0.0],
        5: [-4.45312, -20.29426, -65.69709, 0.0],
        10: [-4.17272, -16.76548, -41.39376, -158.45806],
    },
    ("Zt", "ctt", 4): {
        1: [-5.2678, -32.52703, -156.67657, 0.0],
        5: [-4.73264, -23.14857, -81.89282, 0.0],
        10: [-4.45267, -19.21956, -52.7004, 0.0],
    },
    ("Zt", "ctt", 5): {
        1: [-5.52782, -36.50452, -176.32802, 0.0],
        5: [-4.99469, -26.26502, -91.362, 0.0],
        10: [-4.71558, -21.83411, -61.24154, 0.0],
    },
    ("Zt", "ctt", 6): {
        1: [-5.77369, -40.39506, -208.2686, 798.50524],
        5: [-5.24225, -29.30128, -118.85694, 690.27092],
        10: [-4.96386, -24.54321, -72.77609, 261.99755],
    },
    ("Zt", "ctt", 7): {
        1: [-6.00659, -44.37478, -237.02007, 1018.41789],
        5: [-5.47682, -32.55066, -134.77973, 946.59155],
        10: [-5.19934, -27.33618, -90.68413, 678.71645],
    },
    ("Zt", "ctt", 8): {
        1: [-6.22803, -48.50864, -273.20673, 1650.99407],
        5: [-5.69994, -35.92406, -144.653, 1004.46887],
        10: [-5.4232, -30.27199, -96.93164, 691.22903],
    },
    ("Zt", "ctt", 9): {
        1: [-6.43958, -53.04825, -268.03893, 0.0],
        5: [-5.9132, -39.38676, -162.05454, 1462.07734],
        10: [-5.63707, -33.40001, -98.75155, 709.94709],
    },
    ("Zt", "ctt", 10): {
        1: [-6.64244, -57.52584, -279.92569, 0.0],
        5: [-6.11766, -42.89603, -175.20488, 1593.64411],
        10: [-5.84232, -36.40157, -123.71447, 1412.83302],
    },
    ("Zt", "ctt", 11): {
        1: [-6.83768, -61.86427, -306.55317, 0.0],
        5: [-6.31426, -46.56548, -182.39412, 1725.69849],
        10: [-6.03939, -39.71133, -122.67024, 1384.59913],
    },
    ("Zt", "ctt", 12): {
        1: [-7.02608, -66.25834, -355.91209, 2497.84776],
        5: [-6.50376, -50.34876, -178.07838, 1243.3366],
        10: [-6.22974, -42.89551, -136.40207, 1653.11473],
    },
    ("Zt", "ctt", 13): {
        1: [-7.20802, -70.96868, -360.6143, 2257.93569],
        5: [-6.68695, -54.10216, -192.37705, 1641.10593],
        10: [-6.41334, -46.30107, -139.76668, 1680.5623],
    },
    ("Pu", "n", 1): {
        1: [29.02364, -297.58715, 3221.00195, -22499.12557],
        5: [17.72066, -113.21737, 739.63624, -3632.04826],
        10: [13.07205, -61.92651, 237.17774, 0.0],
    },
    ("Pu", "n", 2): {
        1: [39.24227, -581.70901, 6451.24821, -38605.20731],
        5: [26.24874, -269.60574, 1931.60116, -8874.32795],
        10: [20.61838, -172.21069, 922.37316, -3265.7599],
    },
    ("Pu", "n", 3): {
        1: [47.76219, -904.88855, 11052.69065, -64107.36184],
        5: [33.53722, -473.28647, 4234.95415, -23764.92766],
        10: [27.20015, -322.95973, 2099.70337, -7324.67251],
    },
    ("Pu", "n", 4): {
        1: [55.48854, -1271.83224, 17419.94783, -105733.96755],
        5: [40.22786, -712.91908, 7201.93831, -39844.32692],
        10: [33.32153, -514.17713, 4253.76068, -21838.07961],
    },
    ("Pu", "n", 5): {
        1: [62.68924, -1675.48195, 25657.00988, -175915.7342],
        5: [46.54772, -990.32542, 11424.55306, -71490.55092],
        10: [39.14638, -737.30079, 6989.0383, -38921.80404],
    },
    ("Pu", "n", 6): {
        1: [69.57838, -2114.3281, 35067.50691, -248869.33857],
        5: [52.6237, -1301.0027, 16427.39349, -103799.63077],
        10: [44.77171, -993.16339, 10528.9247, -61456.97805],
    },
    ("Pu", "n", 7): {
        1: [76.22547, -2596.80177, 46957.82067, -362700.23201],
        5: [58.53015, -1652.46422, 23233.75146, -162572.2792],
        10: [50.26116, -1284.3437, 15254.60684, -97990.10186],
    },
    ("Pu", "n", 8): {
        1: [82.69834, -3118.597, 61205.71234, -520745.61034],
        5: [64.28386, -2034.55366, 31269.60824, -239089.37313],
        10: [55.64437, -1609.42899, 21330.3473, -156045.26077],
    },
    ("Pu", "n", 9): {
        1: [88.97294, -3665.15137, 76586.08287, -688720.17464],
        5: [69.91365, -2447.172, 40429.76258, -324633.89158],
        10: [60.92222, -1961.62215, 28157.24596, -217268.83771],
    },
    ("Pu", "n", 10): {
        1: [95.12244, -4247.52208, 94375.20468, -905668.40287],
        5: [75.45894, -2894.83799, 51516.96192, -448319.87415],
        10: [66.13424, -2346.29966, 36518.6973, -307089.87052],
    },
    ("Pu", "n", 11): {
        1: [101.17977, -4874.56041, 115329.74453, -1187948.254],
        5: [80.92549, -3377.34818, 64543.85891, -606304.1555],
        10: [71.28217, -2762.53518, 46199.22008, -412225.0283],
    },
    ("Pu", "n", 12): {
        1: [107.13785, -5533.14019, 138607.3015, -1525508.67355],
        5: [86.31845, -3885.89954, 78576.79123, -772892.90961],
        10: [76.37153, -3206.80705, 57096.12336, -535470.66672],
    },
    ("Pu", "n", 13): {
        1: [113.02781, -6232.29424, 164988.76458, -1933782.19466],
        5: [91.6515, -4429.62744, 95080.20768, -998001.34329],
        10: [81.41335, -3680.9375, 69728.64166, -697748.64714],
    },
    ("Pz", "n", 1): {
        1: [29.02364, -297.58715, 3221.00195, -22499.12557],
        5: [17.72066, -113.21737, 739.63624, -3632.04826],
        10: [13.07205, -61.92651, 237.17774, 0.0],
    },
    ("Pz", "n", 2): {
        1: [55.30933, -942.04803, 12849.15407, -72485.25561],
        5: [40.58842, -509.58599, 5084.36573, -30135.73251],
        10: [33.95841, -354.77947, 2771.81053, -13998.43324],
    },
    ("Pz", "n", 3): {
        1: [89.12858, -1930.52025, 34358.06527, -286682.92518],
        5: [71.02309, -1216.3256, 15637.86083, -98400.3238],
        10: [62.54067, -939.97718, 10077.28434, -55771.75162],
    },
    ("Pz", "n", 4): {
        1: [130.75603, -3296.49382, 64114.76786, -584874.7528],
        5: [109.35184, -2308.72875, 35644.56356, -272215.03829],
        10: [99.03991, -1889.82241, 25236.98657, -170239.10527],
    },
    ("Pz", "n", 5): {
        1: [180.29688, -5175.39127, 110386.06874, -1131576.85563],
        5: [155.55651, -3859.30761, 68120.25848, -608624.8525],
        10: [143.45397, -3281.60339, 51730.82586, -424108.87163],
    },
    ("Pz", "n", 6): {
        1: [237.74354, -7604.43263, 171653.83268, -1841011.1412],
        5: [209.69185, -5929.93066, 113823.98341, -1082704.31683],
        10: [195.79638, -5171.80249, 90183.71297, -797085.50773],
    },
    ("Pz", "n", 7): {
        1: [303.0288, -10649.76548, 251681.61567, -2786856.03209],
        5: [271.74537, -8606.1288, 177517.73924, -1780998.66179],
        10: [256.08641, -7659.27276, 146241.7055, -1390673.27332],
    },
    ("Pz", "n", 8): {
        1: [376.44887, -14544.8488, 372221.38389, -4551126.93855],
        5: [341.82451, -12030.83772, 271741.49903, -3034066.32554],
        10: [324.40047, -10856.99977, 228854.15568, -2436184.11109],
    },
    ("Pz", "n", 9): {
        1: [457.73699, -19165.15497, 513265.67393, -6480474.54344],
        5: [419.82496, -16189.95268, 388520.60302, -4532315.24654],
        10: [400.62396, -14769.33144, 332821.40724, -3711019.12253],
    },
    ("Pz", "n", 10): {
        1: [547.16766, -24828.11069, 714156.11089, -9840473.42751],
        5: [505.91453, -21303.31749, 554186.38511, -7115064.80671],
        10: [484.91178, -19602.37649, 481493.98416, -5937140.44431],
    },
    ("Pz", "n", 11): {
        1: [644.33905, -31313.99089, 938169.05066, -13289306.23354],
        5: [599.81105, -27243.91485, 745243.85429, -9903776.87827],
        10: [577.06782, -25266.75985, 656537.02851, -8416470.1819],
    },
    ("Pz", "n", 12): {
        1: [749.37834, -38788.62759, 1205034.18443, -17466342.81068],
        5: [701.65362, -34179.07759, 979505.12292, -13433329.47047],
        10: [677.16965, -31909.57524, 873305.12481, -11605059.36686],
    },
    ("Pz", "n", 13): {
        1: [862.76099, -47666.41129, 1570659.68641, -24399254.22595],
        5: [811.64189, -42370.8563, 1293735.66398, -19076187.25354],
        10: [785.3778, -39763.42315, 1163741.20122, -16681049.02702],
    },
    ("Pu", "c", 1): {
        1: [40.33007, -654.88907, 5424.25564, -22258.08923],
        5: [27.35078, -314.49347, 1913.87582, -6451.45923],
        10: [21.73107, -204.58018, 956.67809, 0.0],
    },
    ("Pu", "c", 2): {
        1: [48.43054, -975.44351, 9715.73639, -43826.31614],
        5: [34.21569, -514.7024, 3799.18889, -13009.0846],
        10: [27.9038, -358.61839, 2209.55892, -6119.94005],
    },
    ("Pu", "c", 3): {
        1: [55.92912, -1334.93346, 15338.21867, -75143.86858],
        5: [40.70148, -755.2686, 6588.0847, -24386.82853],
        10: [33.81359, -547.72633, 3942.60424, -10170.88918],
    },
    ("Pu", "c", 4): {
        1: [63.04326, -1737.37329, 22633.52965, -120852.24624],
        5: [46.91739, -1036.58513, 10652.24217, -47738.73066],
        10: [39.52384, -776.18219, 6840.80828, -27085.30387],
    },
    ("Pu", "c", 5): {
        1: [69.8614, -2179.76523, 31877.20979, -191035.67676],
        5: [52.90995, -1349.22541, 15633.59543, -77994.1795],
        10: [45.08003, -1037.61441, 10598.66386, -50878.44361],
    },
    ("Pu", "c", 6): {
        1: [76.46507, -2665.57391, 43315.02888, -287933.56922],
        5: [58.76112, -1703.37558, 22316.22779, -128783.44694],
        10: [50.52071, -1332.28819, 15349.39349, -83559.70031],
    },
    ("Pu", "c", 7): {
        1: [82.85922, -3179.94382, 56294.75139, -405639.46157],
        5: [64.48344, -2088.45336, 30092.91004, -189358.8895],
        10: [55.85506, -1657.07866, 21068.00605, -125186.73576],
    },
    ("Pu", "c", 8): {
        1: [89.13483, -3739.99291, 72132.58891, -572429.25607],
        5: [70.09663, -2508.75943, 39638.21559, -278005.04255],
        10: [61.11331, -2014.87096, 28087.53539, -183707.62695],
    },
    ("Pu", "c", 9): {
        1: [95.29027, -4336.37994, 90293.70138, -780439.4076],
        5: [75.62881, -2961.73153, 50663.29269, -385597.13802],
        10: [66.30549, -2404.67467, 36588.72423, -265523.81087],
    },
    ("Pu", "c", 10): {
        1: [101.33093, -4969.06062, 111179.86578, -1045520.67499],
        5: [81.08072, -3451.22316, 64113.08544, -545289.46228],
        10: [71.43788, -2825.60261, 46628.33426, -374166.35767],
    },
    ("Pu", "c", 11): {
        1: [107.27874, -5630.6403, 133755.75134, -1336499.87377],
        5: [86.47238, -3970.56985, 78910.99041, -720298.04296],
        10: [76.51825, -3277.07431, 58115.76302, -503985.93231],
    },
    ("Pu", "c", 12): {
        1: [113.16689, -6339.38022, 160421.23079, -1728916.007],
        5: [91.79069, -4514.08996, 94922.26161, -914188.61853],
        10: [81.55156, -3756.12482, 70839.13773, -650858.6219],
    },
    ("Pu", "c", 13): {
        1: [118.96143, -7067.94504, 188180.84753, -2134843.54461],
        5: [97.06825, -5092.01073, 113196.99802, -1155373.31626],
        10: [86.54134, -4264.07263, 85286.32666, -834313.02414],
    },
    ("Pz", "c", 1): {
        1: [40.33007, -654.88907, 5424.25564, -22258.08923],
        5: [27.35078, -314.49347, 1913.87582, -6451.45923],
        10: [21.73107, -204.58018, 956.67809, 0.0],
    },
    ("Pz", "c", 2): {
        1: [71.16355, -1531.30589, 18704.63158, -92436.11415],
        5: [54.689, -916.97449, 8157.02834, -32279.29091],
        10: [47.1135, -692.32867, 5517.76206, -25651.11683],
    },
    ("Pz", "c", 3): {
        1: [108.98234, -2775.04109, 43495.39947, -329090.27985],
        5: [89.19048, -1875.53829, 22379.13016, -122708.00195],
        10: [79.7747, -1509.90827, 15476.27206, -71884.72395],
    },
    ("Pz", "c", 4): {
        1: [154.57011, -4471.81869, 78954.0959, -672099.84929],
        5: [131.45985, -3265.73071, 47185.51456, -334565.90775],
        10: [120.25432, -2748.39542, 35335.62952, -224124.75938],
    },
    ("Pz", "c", 5): {
        1: [208.06804, -6736.21651, 132846.66674, -1290009.65562],
        5: [181.64632, -5179.55505, 86649.02905, -733121.22844],
        10: [168.63381, -4481.20063, 67779.79238, -522739.01742],
    },
    ("Pz", "c", 6): {
        1: [269.44866, -9598.82691, 203034.13329, -2073404.48953],
        5: [239.75514, -7668.75721, 141366.53605, -1293653.88093],
        10: [224.97627, -6784.32201, 115420.20252, -989377.73025],
    },
    ("Pz", "c", 7): {
        1: [338.76167, -13146.88242, 294879.49586, -3129583.78708],
        5: [305.80434, -10817.43795, 216068.36097, -2100647.66671],
        10: [289.25777, -9726.74584, 181627.32774, -1677310.26011],
    },
    ("Pz", "c", 8): {
        1: [416.14015, -17590.3332, 430148.85018, -5067478.81734],
        5: [379.88205, -14774.63793, 324402.14239, -3517453.65715],
        10: [361.54864, -13438.02734, 277348.73074, -2866431.10564],
    },
    ("Pz", "c", 9): {
        1: [501.40746, -22807.59758, 587499.49393, -7169201.82583],
        5: [461.85428, -19511.67063, 457404.31133, -5206192.70384],
        10: [441.77645, -17929.20509, 398323.23824, -4358573.4323],
    },
    ("Pz", "c", 10): {
        1: [594.8391, -29160.24837, 811973.82436, -10875698.70317],
        5: [551.93628, -25269.81692, 643267.64311, -8055892.97953],
        10: [530.0696, -23391.85485, 566219.84806, -6830009.69973],
    },
    ("Pz", "c", 11): {
        1: [695.96214, -36331.9335, 1056025.94315, -14523732.11009],
        5: [649.81624, -31905.54566, 857081.46973, -11141786.84936],
        10: [626.19241, -29738.57833, 763918.63868, -9621670.09653],
    },
    ("Pz", "c", 12): {
        1: [805.05439, -44617.75043, 1353942.45263, -19172159.97416],
        5: [755.621, -39583.25111, 1117210.87971, -15030308.53641],
        10: [730.28496, -37116.83024, 1006180.59998, -13161609.47003],
    },
    ("Pz", "c", 13): {
        1: [922.35591, -54324.87226, 1752699.21028, -26657657.00816],
        5: [869.60745, -48597.47585, 1464317.66553, -21220837.22458],
        10: [842.50039, -45774.6042, 1327828.69484, -18738773.04038],
    },
    ("Pu", "ct", 1): {
        1: [57.87306, -1313.24935, 14775.02371, -78358.24797],
        5: [42.7087, -734.41195, 6454.64125, -28937.31933],
        10: [35.85429, -526.84027, 3983.96951, -15920.94625],
    },
    ("Pu", "ct", 2): {
        1: [64.54104, -1690.8413, 21975.97283, -132101.30149],
        5: [48.44867, -988.91385, 10123.98828, -51238.66461],
        10: [41.09535, -732.77963, 6603.52144, -31623.40022],
    },
    ("Pu", "ct", 3): {
        1: [71.04854, -2100.6543, 30060.9244, -185836.06339],
        5: [54.13835, -1278.88338, 14617.28467, -76164.31495],
        10: [46.33513, -971.50694, 9829.20552, -48444.56846],
    },
    ("Pu", "ct", 4): {
        1: [77.42346, -2552.0703, 39969.54556, -258416.46283],
        5: [59.77399, -1608.93127, 20526.70548, -115553.87744],
        10: [51.55453, -1245.76297, 14110.04537, -75387.44902],
    },
    ("Pu", "ct", 5): {
        1: [83.67511, -3045.69742, 52316.39875, -369463.50827],
        5: [65.33522, -1974.97685, 27996.58694, -177237.09785],
        10: [56.73274, -1555.42799, 19760.33515, -121733.29054],
    },
    ("Pu", "ct", 6): {
        1: [89.81864, -3574.69149, 66371.73288, -499902.1489],
        5: [70.82789, -2371.88705, 36480.34405, -244735.55643],
        10: [61.87275, -1893.38591, 26075.77549, -167830.6833],
    },
    ("Pu", "ct", 7): {
        1: [95.87552, -4148.7851, 83494.71615, -687936.21385],
        5: [76.26899, -2807.93994, 47044.56577, -347833.4978],
        10: [66.97884, -2268.9793, 34290.20652, -247471.82101],
    },
    ("Pu", "ct", 8): {
        1: [101.83916, -4753.81537, 102436.12487, -903328.53466],
        5: [81.64566, -3273.49452, 58926.46969, -466105.18809],
        10: [72.03047, -2669.08789, 43220.63705, -328974.05102],
    },
    ("Pu", "ct", 9): {
        1: [107.72494, -5399.87512, 124482.62283, -1184001.43735],
        5: [86.9826, -3777.63193, 73126.18742, -627750.2109],
        10: [77.05336, -3102.87046, 53808.95922, -437207.95248],
    },
    ("Pu", "ct", 10): {
        1: [113.55313, -6083.36832, 149338.80171, -1526314.04607],
        5: [92.25914, -4308.96509, 88952.60498, -822635.61452],
        10: [82.04412, -3572.15392, 66661.55525, -594480.75636],
    },
    ("Pu", "ct", 11): {
        1: [119.30918, -6795.87827, 176385.48623, -1916150.04735],
        5: [97.49704, -4872.75936, 106634.98395, -1048771.40811],
        10: [86.99233, -4064.53353, 80363.72344, -758363.31305],
    },
    ("Pu", "ct", 12): {
        1: [125.02625, -7551.21194, 207034.52585, -2395128.84718],
        5: [102.69376, -5470.05013, 126718.33786, -1330933.97283],
        10: [91.91742, -4587.60676, 95941.566, -963097.61258],
    },
    ("Pu", "ct", 13): {
        1: [130.69153, -8344.90827, 241289.83502, -2972016.0606],
        5: [107.85086, -6094.06537, 148454.81738, -1645244.29296],
        10: [96.80805, -5138.12463, 113002.71392, -1191056.41582],
    },
    ("Pz", "ct", 1): {
        1: [57.87306, -1313.24935, 14775.02371, -78358.24797],
        5: [42.7087, -734.41195, 6454.64125, -28937.31933],
        10: [35.85429, -526.84027, 3983.96951, -15920.94625],
    },
    ("Pz", "ct", 2): {
        1: [99.67824, -2613.84092, 40993.05587, -305147.36372],
        5: [80.4541, -1713.45775, 20003.59383, -103238.05351],
        10: [71.38091, -1360.4893, 13849.68389, -65924.89458],
    },
    ("Pz", "ct", 3): {
        1: [147.04766, -4302.65849, 76399.57662, -654722.95743],
        5: [124.31475, -3117.31076, 45747.60653, -333990.36514],
        10: [113.28451, -2602.36468, 33768.3374, -218667.20728],
    },
    ("Pz", "ct", 4): {
        1: [201.50063, -6529.00109, 126019.67281, -1152742.92376],
        5: [175.33669, -5005.47262, 82687.06828, -668850.24801],
        10: [162.46157, -4321.36561, 64895.94027, -484820.70263],
    },
    ("Pz", "ct", 5): {
        1: [263.49838, -9414.23365, 199911.22911, -2050627.43595],
        5: [233.96429, -7498.69865, 138918.19824, -1281419.45266],
        10: [219.28211, -6623.26279, 113391.17331, -984275.64905],
    },
    ("Pz", "ct", 6): {
        1: [333.16774, -12964.03197, 292429.19375, -3124233.7203],
        5: [300.35662, -10641.6075, 213487.5301, -2087656.19639],
        10: [283.87909, -9552.52154, 178886.94806, -1659172.29283],
    },
    ("Pz", "ct", 7): {
        1: [410.80988, -17389.61597, 426111.58155, -5024404.79002],
        5: [374.64662, -14582.16547, 320873.92282, -3487114.33763],
        10: [356.39236, -13252.9612, 274213.05721, -2844919.51622],
    },
    ("Pz", "ct", 8): {
        1: [496.25187, -22597.79997, 583070.6042, -7121417.14277],
        5: [456.78115, -19304.04819, 453029.9483, -5160603.09663],
        10: [436.74764, -17725.71396, 394191.45055, -4318872.91473],
    },
    ("Pz", "ct", 9): {
        1: [589.53214, -28727.53669, 777603.17579, -9810348.62225],
        5: [546.79242, -24918.2995, 619890.02811, -7363624.17845],
        10: [525.00618, -23066.56317, 546373.05188, -6262599.04969],
    },
    ("Pz", "ct", 10): {
        1: [691.01526, -36091.44845, 1049711.80069, -14435803.74725],
        5: [644.94277, -31668.56261, 851248.59315, -11074173.35551],
        10: [621.35061, -29493.29838, 756798.3661, -9512568.96832],
    },
    ("Pz", "ct", 11): {
        1: [800.21313, -44364.05117, 1346935.16836, -19074159.04888],
        5: [750.84835, -39331.17227, 1110308.99581, -14938461.25547],
        10: [725.52318, -36855.10571, 998040.54937, -13032425.67798],
    },
    ("Pz", "ct", 12): {
        1: [917.59781, -54064.48864, 1745940.74067, -26580567.62572],
        5: [864.93594, -48337.71744, 1457105.79707, -21127541.85087],
        10: [837.82359, -45501.11662, 1318981.21613, -18596556.02677],
    },
    ("Pz", "ct", 13): {
        1: [1042.61602, -64751.42935, 2170526.54238, -33863270.75851],
        5: [986.68107, -58346.55627, 1834613.48016, -27358171.74258],
        10: [957.83512, -55163.46716, 1673215.87467, -24322979.95071],
    },
    ("Pu", "ctt", 1): {
        1: [72.43198, -2031.23067, 27494.26774, -164543.30512],
        5: [55.59415, -1222.20012, 13443.13654, -71684.75121],
        10: [47.81991, -915.29619, 8831.56983, -42803.99912],
    },
    ("Pu", "ctt", 2): {
        1: [78.5872, -2460.87151, 37154.334, -243593.39597],
        5: [60.97992, -1527.88391, 18831.74305, -108243.93544],
        10: [52.78346, -1168.0995, 12706.65728, -66631.43888],
    },
    ("Pu", "ctt", 3): {
        1: [84.67165, -2925.83841, 47973.32259, -324422.32044],
        5: [66.36261, -1869.20764, 25207.0344, -148418.36118],
        10: [57.78032, -1455.92691, 17522.80885, -96534.4054],
    },
    ("Pu", "ctt", 4): {
        1: [90.71548, -3443.03514, 62174.79257, -467064.09797],
        5: [71.72293, -2251.24864, 33595.06849, -220403.04039],
        10: [62.77445, -1778.38625, 23715.6499, -145954.6005],
    },
    ("Pu", "ctt", 5): {
        1: [96.67917, -3996.03494, 78107.66115, -626647.97267],
        5: [77.04718, -2663.51694, 42976.41425, -297395.2658],
        10: [67.75824, -2130.4943, 30781.70168, -199985.0841],
    },
    ("Pu", "ctt", 6): {
        1: [102.55646, -4578.6262, 95843.1953, -815823.10008],
        5: [82.33471, -3109.35965, 53987.26297, -397783.77633],
        10: [72.72851, -2515.86546, 39264.53256, -273143.07028],
    },
    ("Pu", "ctt", 7): {
        1: [108.38921, -5208.19928, 117267.76416, -1087261.24569],
        5: [87.60449, -3596.77841, 67652.56542, -551842.16856],
        10: [77.68532, -2937.78455, 49828.09019, -386816.45076],
    },
    ("Pu", "ctt", 8): {
        1: [114.14748, -5865.43034, 140175.45428, -1375438.13175],
        5: [92.82932, -4111.44108, 82480.98233, -716957.8177],
        10: [82.61194, -3384.18857, 61149.98891, -502674.1651],
    },
    ("Pu", "ctt", 9): {
        1: [119.85944, -6562.79423, 166525.42089, -1749567.26408],
        5: [98.01824, -4660.05087, 99746.13377, -939758.23097],
        10: [87.5213, -3865.4198, 74755.11279, -670480.46204],
    },
    ("Pu", "ctt", 10): {
        1: [125.52142, -7293.56005, 195257.17003, -2170047.77306],
        5: [103.17601, -5237.90592, 118503.18084, -1182250.319],
        10: [92.40193, -4372.0547, 89359.13517, -845108.67347],
    },
    ("Pu", "ctt", 11): {
        1: [131.14034, -8057.30978, 226766.51504, -2660188.16558],
        5: [108.3026, -5846.25616, 139302.35327, -1468682.58163],
        10: [97.2676, -4910.85091, 105978.35276, -1062163.48445],
    },
    ("Pu", "ctt", 12): {
        1: [136.7227, -8861.09408, 261970.44529, -3250283.97786],
        5: [113.39994, -6485.65881, 162339.71662, -1808761.9352],
        10: [102.10599, -5477.05797, 124422.28759, -1323068.1304],
    },
    ("Pu", "ctt", 13): {
        1: [142.26231, -9693.62082, 299692.7503, -3907390.84193],
        5: [118.47587, -7160.24252, 188251.16296, -2221810.6885],
        10: [106.93568, -6076.86788, 145158.93958, -1634404.62279],
    },
    ("Pz", "ctt", 1): {
        1: [72.43198, -2031.23067, 27494.26774, -164543.30512],
        5: [55.59415, -1222.20012, 13443.13654, -71684.75121],
        10: [47.81991, -915.29619, 8831.56983, -42803.99912],
    },
    ("Pz", "ctt", 2): {
        1: [124.57781, -3781.77526, 66414.0551, -553118.89443],
        5: [103.22082, -2624.54577, 36835.31251, -240925.89419],
        10: [92.96515, -2136.7731, 25986.26469, -142497.29758],
    },
    ("Pz", "ctt", 3): {
        1: [181.6224, -5973.86533, 114155.31784, -1018592.30109],
        5: [156.40846, -4493.46903, 73261.80762, -575124.31783],
        10: [144.06643, -3835.88786, 56629.40987, -407658.59025],
    },
    ("Pz", "ctt", 4): {
        1: [245.26835, -8825.08169, 186494.00456, -1886000.00793],
        5: [216.46176, -6956.00534, 128026.82236, -1161323.05157],
        10: [202.15361, -6100.35436, 103236.75768, -873348.8791],
    },
    ("Pz", "ctt", 5): {
        1: [316.06899, -12339.56185, 277562.2443, -2939754.96],
        5: [283.79656, -10062.24915, 201191.20697, -1951145.98057],
        10: [267.61865, -8997.37847, 167746.59962, -1541584.34763],
    },
    ("Pz", "ctt", 6): {
        1: [394.28695, -16595.70237, 393218.44887, -4313377.89935],
        5: [358.65805, -13887.27335, 297246.89408, -3030204.48059],
        10: [340.67862, -12603.20846, 254376.23251, -2486083.09091],
    },
    ("Pz", "ctt", 7): {
        1: [480.46359, -21897.10427, 565128.08582, -6892293.51428],
        5: [441.39285, -18641.47805, 436940.53423, -4966446.39881],
        10: [421.58513, -17087.60832, 379306.56974, -4144253.77227],
    },
    ("Pz", "ctt", 8): {
        1: [574.18323, -27969.28756, 755815.12115, -9503874.44771],
        5: [531.7919, -24203.15676, 600571.65866, -7105482.04515],
        10: [510.20262, -22378.84028, 528808.83714, -6041380.37244],
    },
    ("Pz", "ctt", 9): {
        1: [675.99802, -35287.19625, 1025000.94459, -14061085.95072],
        5: [630.24994, -30909.49217, 829241.8834, -10755636.83147],
        10: [606.84893, -28766.58182, 737149.24304, -9251863.23731],
    },
    ("Pz", "ctt", 10): {
        1: [785.52855, -43533.15196, 1321409.40045, -18702907.29567],
        5: [736.41307, -38529.29943, 1085617.96925, -14564425.86968],
        10: [711.25605, -36085.20059, 975895.79875, -12722904.43955],
    },
    ("Pz", "ctt", 11): {
        1: [902.85357, -52895.98471, 1669608.54095, -24250405.80617],
        5: [850.45746, -47277.01647, 1395419.93309, -19343069.54672],
        10: [823.52649, -44500.16483, 1264589.99366, -17070913.5799],
    },
    ("Pz", "ctt", 12): {
        1: [1028.42555, -63858.55314, 2141259.81328, -33415237.36744],
        5: [972.68579, -57490.6317, 1808268.64014, -26985027.78549],
        10: [943.95566, -54328.32607, 1648442.93368, -23988629.39937],
    },
    ("Pz", "ctt", 13): {
        1: [1161.56118, -75813.63728, 2633988.31569, -42088318.29743],
        5: [1102.53922, -68747.38161, 2251673.80175, -34543193.61516],
        10: [1072.0703, -65224.17253, 2066852.27021, -30993491.84766],
    },
}

CV_TAU_MIN = {
    ("Za", "n", 1): 25,
    ("Za", "n", 2): 25,
    ("Za", "n", 3): 30,
    ("Za", "n", 4): 30,
    ("Za", "n", 5): 35,
    ("Za", "n", 6): 35,
    ("Za", "n", 7): 35,
    ("Za", "n", 8): 40,
    ("Za", "n", 9): 40,
    ("Za", "n", 10): 45,
    ("Za", "n", 11): 45,
    ("Za", "n", 12): 45,
    ("Za", "n", 13): 50,
    ("Zt", "n", 1): 25,
    ("Zt", "n", 2): 25,
    ("Zt", "n", 3): 30,
    ("Zt", "n", 4): 30,
    ("Zt", "n", 5): 35,
    ("Zt", "n", 6): 35,
    ("Zt", "n", 7): 35,
    ("Zt", "n", 8): 40,
    ("Zt", "n", 9): 40,
    ("Zt", "n", 10): 45,
    ("Zt", "n", 11): 45,
    ("Zt", "n", 12): 45,
    ("Zt", "n", 13): 50,
    ("Za", "c", 1): 25,
    ("Za", "c", 2): 25,
    ("Za", "c", 3): 30,
    ("Za", "c", 4): 30,
    ("Za", "c", 5): 35,
    ("Za", "c", 6): 35,
    ("Za", "c", 7): 35,
    ("Za", "c", 8): 40,
    ("Za", "c", 9): 40,
    ("Za", "c", 10): 45,
    ("Za", "c", 11): 45,
    ("Za", "c", 12): 45,
    ("Za", "c", 13): 50,
    ("Zt", "c", 1): 25,
    ("Zt", "c", 2): 25,
    ("Zt", "c", 3): 30,
    ("Zt", "c", 4): 30,
    ("Zt", "c", 5): 35,
    ("Zt", "c", 6): 35,
    ("Zt", "c", 7): 35,
    ("Zt", "c", 8): 40,
    ("Zt", "c", 9): 40,
    ("Zt", "c", 10): 45,
    ("Zt", "c", 11): 45,
    ("Zt", "c", 12): 45,
    ("Zt", "c", 13): 50,
    ("Za", "ct", 1): 25,
    ("Za", "ct", 2): 30,
    ("Za", "ct", 3): 30,
    ("Za", "ct", 4): 30,
    ("Za", "ct", 5): 35,
    ("Za", "ct", 6): 35,
    ("Za", "ct", 7): 40,
    ("Za", "ct", 8): 40,
    ("Za", "ct", 9): 40,
    ("Za", "ct", 10): 45,
    ("Za", "ct", 11): 45,
    ("Za", "ct", 12): 50,
    ("Za", "ct", 13): 50,
    ("Zt", "ct", 1): 25,
    ("Zt", "ct", 2): 30,
    ("Zt", "ct", 3): 30,
    ("Zt", "ct", 4): 30,
    ("Zt", "ct", 5): 35,
    ("Zt", "ct", 6): 35,
    ("Zt", "ct", 7): 40,
    ("Zt", "ct", 8): 40,
    ("Zt", "ct", 9): 40,
    ("Zt", "ct", 10): 45,
    ("Zt", "ct", 11): 45,
    ("Zt", "ct", 12): 50,
    ("Zt", "ct", 13): 50,
    ("Za", "ctt", 1): 25,
    ("Za", "ctt", 2): 30,
    ("Za", "ctt", 3): 30,
    ("Za", "ctt", 4): 35,
    ("Za", "ctt", 5): 35,
    ("Za", "ctt", 6): 35,
    ("Za", "ctt", 7): 40,
    ("Za", "ctt", 8): 40,
    ("Za", "ctt", 9): 45,
    ("Za", "ctt", 10): 45,
    ("Za", "ctt", 11): 45,
    ("Za", "ctt", 12): 50,
    ("Za", "ctt", 13): 50,
    ("Zt", "ctt", 1): 25,
    ("Zt", "ctt", 2): 30,
    ("Zt", "ctt", 3): 30,
    ("Zt", "ctt", 4): 35,
    ("Zt", "ctt", 5): 35,
    ("Zt", "ctt", 6): 35,
    ("Zt", "ctt", 7): 40,
    ("Zt", "ctt", 8): 40,
    ("Zt", "ctt", 9): 45,
    ("Zt", "ctt", 10): 45,
    ("Zt", "ctt", 11): 45,
    ("Zt", "ctt", 12): 50,
    ("Zt", "ctt", 13): 50,
    ("Pu", "n", 1): 25,
    ("Pu", "n", 2): 25,
    ("Pu", "n", 3): 30,
    ("Pu", "n", 4): 30,
    ("Pu", "n", 5): 35,
    ("Pu", "n", 6): 35,
    ("Pu", "n", 7): 35,
    ("Pu", "n", 8): 40,
    ("Pu", "n", 9): 40,
    ("Pu", "n", 10): 45,
    ("Pu", "n", 11): 45,
    ("Pu", "n", 12): 45,
    ("Pu", "n", 13): 50,
    ("Pz", "n", 1): 25,
    ("Pz", "n", 2): 25,
    ("Pz", "n", 3): 30,
    ("Pz", "n", 4): 30,
    ("Pz", "n", 5): 35,
    ("Pz", "n", 6): 35,
    ("Pz", "n", 7): 35,
    ("Pz", "n", 8): 40,
    ("Pz", "n", 9): 40,
    ("Pz", "n", 10): 45,
    ("Pz", "n", 11): 45,
    ("Pz", "n", 12): 45,
    ("Pz", "n", 13): 50,
    ("Pu", "c", 1): 25,
    ("Pu", "c", 2): 25,
    ("Pu", "c", 3): 30,
    ("Pu", "c", 4): 30,
    ("Pu", "c", 5): 35,
    ("Pu", "c", 6): 35,
    ("Pu", "c", 7): 35,
    ("Pu", "c", 8): 40,
    ("Pu", "c", 9): 40,
    ("Pu", "c", 10): 45,
    ("Pu", "c", 11): 45,
    ("Pu", "c", 12): 45,
    ("Pu", "c", 13): 50,
    ("Pz", "c", 1): 25,
    ("Pz", "c", 2): 25,
    ("Pz", "c", 3): 30,
    ("Pz", "c", 4): 30,
    ("Pz", "c", 5): 35,
    ("Pz", "c", 6): 35,
    ("Pz", "c", 7): 35,
    ("Pz", "c", 8): 40,
    ("Pz", "c", 9): 40,
    ("Pz", "c", 10): 45,
    ("Pz", "c", 11): 45,
    ("Pz", "c", 12): 45,
    ("Pz", "c", 13): 50,
    ("Pu", "ct", 1): 25,
    ("Pu", "ct", 2): 30,
    ("Pu", "ct", 3): 30,
    ("Pu", "ct", 4): 30,
    ("Pu", "ct", 5): 35,
    ("Pu", "ct", 6): 35,
    ("Pu", "ct", 7): 40,
    ("Pu", "ct", 8): 40,
    ("Pu", "ct", 9): 40,
    ("Pu", "ct", 10): 45,
    ("Pu", "ct", 11): 45,
    ("Pu", "ct", 12): 50,
    ("Pu", "ct", 13): 50,
    ("Pz", "ct", 1): 25,
    ("Pz", "ct", 2): 30,
    ("Pz", "ct", 3): 30,
    ("Pz", "ct", 4): 30,
    ("Pz", "ct", 5): 35,
    ("Pz", "ct", 6): 35,
    ("Pz", "ct", 7): 40,
    ("Pz", "ct", 8): 40,
    ("Pz", "ct", 9): 40,
    ("Pz", "ct", 10): 45,
    ("Pz", "ct", 11): 45,
    ("Pz", "ct", 12): 50,
    ("Pz", "ct", 13): 50,
    ("Pu", "ctt", 1): 25,
    ("Pu", "ctt", 2): 30,
    ("Pu", "ctt", 3): 30,
    ("Pu", "ctt", 4): 35,
    ("Pu", "ctt", 5): 35,
    ("Pu", "ctt", 6): 35,
    ("Pu", "ctt", 7): 40,
    ("Pu", "ctt", 8): 40,
    ("Pu", "ctt", 9): 45,
    ("Pu", "ctt", 10): 45,
    ("Pu", "ctt", 11): 45,
    ("Pu", "ctt", 12): 50,
    ("Pu", "ctt", 13): 50,
    ("Pz", "ctt", 1): 25,
    ("Pz", "ctt", 2): 30,
    ("Pz", "ctt", 3): 30,
    ("Pz", "ctt", 4): 35,
    ("Pz", "ctt", 5): 35,
    ("Pz", "ctt", 6): 35,
    ("Pz", "ctt", 7): 40,
    ("Pz", "ctt", 8): 40,
    ("Pz", "ctt", 9): 45,
    ("Pz", "ctt", 10): 45,
    ("Pz", "ctt", 11): 45,
    ("Pz", "ctt", 12): 50,
    ("Pz", "ctt", 13): 50,
}

PVAL_LARGE_P = {
    ("Pz", "c", 1): [2.60001, 0.4424, 0.01681, 0.00021],
    ("Pz", "c", 2): [3.91014, 0.22886, 0.0036, 2e-05],
    ("Pz", "c", 3): [5.29142, 0.15873, 0.00135, 0.0],
    ("Pz", "c", 4): [6.67619, 0.12251, 0.00065, 0.0],
    ("Pz", "c", 5): [8.11703, 0.10119, 0.00037, 0.0],
    ("Pz", "c", 6): [9.48038, 0.085, 0.00023, 0.0],
    ("Pz", "c", 7): [10.94488, 0.07457, 0.00015, 0.0],
    ("Pz", "c", 8): [12.34873, 0.06586, 0.00011, 0.0],
    ("Pz", "c", 9): [13.82803, 0.05955, 8e-05, 0.0],
    ("Pz", "c", 10): [15.24309, 0.05395, 6e-05, 0.0],
    ("Pz", "c", 11): [16.67574, 0.04942, 4e-05, 0.0],
    ("Pz", "c", 12): [18.12166, 0.04566, 3e-05, 0.0],
    ("Pz", "c", 13): [19.59107, 0.04255, 3e-05, 0.0],
    ("Pz", "ct", 1): [3.34748, 0.28382, 0.00617, 5e-05],
    ("Pz", "ct", 2): [4.82719, 0.16609, 0.0016, 1e-05],
    ("Pz", "ct", 3): [6.30975, 0.12402, 0.0007, 0.0],
    ("Pz", "ct", 4): [7.77624, 0.10044, 0.00038, 0.0],
    ("Pz", "ct", 5): [9.27226, 0.08568, 0.00023, 0.0],
    ("Pz", "ct", 6): [10.67983, 0.07385, 0.00015, 0.0],
    ("Pz", "ct", 7): [12.09005, 0.06502, 0.0001, 0.0],
    ("Pz", "ct", 8): [13.53755, 0.05845, 8e-05, 0.0],
    ("Pz", "ct", 9): [15.01789, 0.05334, 6e-05, 0.0],
    ("Pz", "ct", 10): [16.4478, 0.04882, 4e-05, 0.0],
    ("Pz", "ct", 11): [17.84, 0.04486, 3e-05, 0.0],
    ("Pz", "ct", 12): [19.30601, 0.04181, 3e-05, 0.0],
    ("Pz", "ct", 13): [20.67043, 0.03878, 2e-05, 0.0],
    ("Pz", "ctt", 1): [3.85968, 0.22416, 0.00349, 2e-05],
    ("Pz", "ctt", 2): [5.5296, 0.13797, 0.00098, 0.0],
    ("Pz", "ctt", 3): [7.1063, 0.10536, 0.00045, 0.0],
    ("Pz", "ctt", 4): [8.6813, 0.08769, 0.00026, 0.0],
    ("Pz", "ctt", 5): [10.22783, 0.07569, 0.00017, 0.0],
    ("Pz", "ctt", 6): [11.71719, 0.06646, 0.00011, 0.0],
    ("Pz", "ctt", 7): [13.21317, 0.05951, 8e-05, 0.0],
    ("Pz", "ctt", 8): [14.6611, 0.05367, 6e-05, 0.0],
    ("Pz", "ctt", 9): [16.19398, 0.04945, 5e-05, 0.0],
    ("Pz", "ctt", 10): [17.60558, 0.04529, 3e-05, 0.0],
    ("Pz", "ctt", 11): [19.14325, 0.04235, 3e-05, 0.0],
    ("Pz", "ctt", 12): [20.6701, 0.03973, 2e-05, 0.0],
    ("Pz", "ctt", 13): [22.01247, 0.03686, 2e-05, 0.0],
    ("Pz", "n", 1): [2.16876, 0.78365, 0.04915, 0.00087],
    ("Pz", "n", 2): [3.3247, 0.29826, 0.00676, 6e-05],
    ("Pz", "n", 3): [4.63801, 0.18806, 0.00212, 1e-05],
    ("Pz", "n", 4): [6.00043, 0.13855, 0.00092, 0.0],
    ("Pz", "n", 5): [7.37952, 0.11018, 0.00048, 0.0],
    ("Pz", "n", 6): [8.74804, 0.09128, 0.00028, 0.0],
    ("Pz", "n", 7): [10.14341, 0.07838, 0.00018, 0.0],
    ("Pz", "n", 8): [11.57965, 0.06914, 0.00012, 0.0],
    ("Pz", "n", 9): [13.01344, 0.06183, 9e-05, 0.0],
    ("Pz", "n", 10): [14.47188, 0.0561, 7e-05, 0.0],
    ("Pz", "n", 11): [15.72943, 0.05016, 5e-05, 0.0],
    ("Pz", "n", 12): [17.16561, 0.04623, 4e-05, 0.0],
    ("Pz", "n", 13): [18.68866, 0.04327, 3e-05, 0.0],
    ("Zt", "c", 1): [1.7735, 0.99563, -0.11623, -0.01648],
    ("Zt", "c", 2): [2.28614, 0.82696, -0.19037, -0.02531],
    ("Zt", "c", 3): [2.7309, 0.71143, -0.22628, -0.02756],
    ("Zt", "c", 4): [3.16982, 0.6842, -0.22242, -0.02493],
    ("Zt", "c", 5): [3.62852, 0.72671, -0.19741, -0.02069],
    ("Zt", "c", 6): [4.10967, 0.81079, -0.16534, -0.01657],
    ("Zt", "c", 7): [4.5882, 0.90244, -0.13572, -0.0132],
    ("Zt", "c", 8): [5.04236, 0.98143, -0.11275, -0.01076],
    ("Zt", "c", 9): [5.47698, 1.05403, -0.09312, -0.0088],
    ("Zt", "c", 10): [5.91164, 1.12835, -0.07539, -0.00719],
    ("Zt", "c", 11): [6.35502, 1.21313, -0.05664, -0.00564],
    ("Zt", "c", 12): [6.75481, 1.27101, -0.04489, -0.00469],
    ("Zt", "c", 13): [7.15084, 1.33079, -0.03331, -0.0038],
    ("Zt", "ct", 1): [2.67793, 0.90911, -0.21181, -0.03099],
    ("Zt", "ct", 2): [3.02495, 0.83171, -0.2087, -0.0268],
    ("Zt", "ct", 3): [3.38586, 0.79416, -0.19914, -0.02309],
    ("Zt", "ct", 4): [3.77263, 0.80284, -0.18075, -0.01937],
    ("Zt", "ct", 5): [4.20041, 0.85677, -0.15539, -0.01577],
    ("Zt", "ct", 6): [4.62818, 0.91426, -0.13437, -0.01314],
    ("Zt", "ct", 7): [5.06185, 0.98352, -0.11319, -0.01082],
    ("Zt", "ct", 8): [5.5125, 1.0694, -0.0905, -0.00865],
    ("Zt", "ct", 9): [5.94052, 1.14214, -0.07278, -0.00702],
    ("Zt", "ct", 10): [6.35607, 1.20839, -0.05813, -0.00577],
    ("Zt", "ct", 11): [6.78453, 1.28544, -0.04231, -0.00453],
    ("Zt", "ct", 12): [7.17826, 1.34299, -0.03135, -0.0037],
    ("Zt", "ct", 13): [7.53794, 1.38331, -0.02426, -0.00315],
    ("Zt", "ctt", 1): [3.35051, 0.93012, -0.20245, -0.02724],
    ("Zt", "ctt", 2): [3.67509, 0.9123, -0.17999, -0.02192],
    ("Zt", "ctt", 3): [4.03813, 0.93056, -0.156, -0.01762],
    ("Zt", "ctt", 4): [4.42616, 0.97184, -0.13211, -0.01408],
    ("Zt", "ctt", 5): [4.81407, 1.01111, -0.11463, -0.01172],
    ("Zt", "ctt", 6): [5.2261, 1.07062, -0.0956, -0.00959],
    ("Zt", "ctt", 7): [5.61881, 1.1193, -0.0814, -0.00806],
    ("Zt", "ctt", 8): [6.00595, 1.16676, -0.06925, -0.00685],
    ("Zt", "ctt", 9): [6.42692, 1.23858, -0.05321, -0.00548],
    ("Zt", "ctt", 10): [6.83747, 1.3066, -0.03907, -0.00436],
    ("Zt", "ctt", 11): [7.20228, 1.35005, -0.03046, -0.00365],
    ("Zt", "ctt", 12): [7.60799, 1.41617, -0.01869, -0.00283],
    ("Zt", "ctt", 13): [7.97465, 1.46309, -0.01082, -0.00228],
    ("Zt", "n", 1): [0.46981, 0.98962, -0.04231, 0.00071],
    ("Zt", "n", 2): [1.57702, 0.94323, -0.14169, -0.02172],
    ("Zt", "n", 3): [2.27828, 0.84081, -0.20473, -0.0296],
    ("Zt", "n", 4): [2.82849, 0.81191, -0.20185, -0.02589],
    ("Zt", "n", 5): [3.32584, 0.82445, -0.1854, -0.0218],
    ("Zt", "n", 6): [3.80563, 0.87143, -0.16082, -0.01768],
    ("Zt", "n", 7): [4.28734, 0.94586, -0.13267, -0.01395],
    ("Zt", "n", 8): [4.7606, 1.02365, -0.10826, -0.01113],
    ("Zt", "n", 9): [5.21757, 1.09915, -0.08697, -0.00888],
    ("Zt", "n", 10): [5.64931, 1.16313, -0.07063, -0.00726],
    ("Zt", "n", 11): [6.09855, 1.24308, -0.05245, -0.00567],
    ("Zt", "n", 12): [6.49722, 1.29446, -0.0415, -0.00471],
    ("Zt", "n", 13): [6.88598, 1.34332, -0.03186, -0.00391],
    ("Za", "c", 1): [1.691, 0.49315, 0.02664, 0.00056],
    ("Za", "c", 2): [2.18902, 0.37008, 0.01335, 0.0002],
    ("Za", "c", 3): [2.62992, 0.3061, 0.00809, 0.0001],
    ("Za", "c", 4): [3.03968, 0.26994, 0.00569, 6e-05],
    ("Za", "c", 5): [3.4124, 0.24503, 0.00431, 4e-05],
    ("Za", "c", 6): [3.74242, 0.22506, 0.00337, 2e-05],
    ("Za", "c", 7): [4.05606, 0.2104, 0.00277, 2e-05],
    ("Za", "c", 8): [4.34336, 0.198, 0.00232, 1e-05],
    ("Za", "c", 9): [4.61378, 0.18761, 0.00198, 1e-05],
    ("Za", "c", 10): [4.874, 0.17912, 0.00172, 1e-05],
    ("Za", "c", 11): [5.10763, 0.1708, 0.0015, 1e-05],
    ("Za", "c", 12): [5.33569, 0.16378, 0.00133, 1e-05],
    ("Za", "c", 13): [5.55398, 0.15752, 0.00118, 0.0],
    ("Za", "ct", 1): [2.59723, 0.39234, 0.0134, 0.00019],
    ("Za", "ct", 2): [2.90588, 0.32659, 0.00872, 0.0001],
    ("Za", "ct", 3): [3.21369, 0.28247, 0.00607, 6e-05],
    ("Za", "ct", 4): [3.52014, 0.25188, 0.00449, 4e-05],
    ("Za", "ct", 5): [3.81276, 0.22924, 0.00347, 2e-05],
    ("Za", "ct", 6): [4.09603, 0.21231, 0.00281, 2e-05],
    ("Za", "ct", 7): [4.37132, 0.19929, 0.00234, 1e-05],
    ("Za", "ct", 8): [4.62507, 0.18776, 0.00198, 1e-05],
    ("Za", "ct", 9): [4.87416, 0.17854, 0.00171, 1e-05],
    ("Za", "ct", 10): [5.11358, 0.1707, 0.0015, 1e-05],
    ("Za", "ct", 11): [5.33092, 0.16317, 0.00131, 1e-05],
    ("Za", "ct", 12): [5.54839, 0.15696, 0.00117, 0.0],
    ("Za", "ct", 13): [5.75913, 0.15153, 0.00106, 0.0],
    ("Za", "ctt", 1): [3.21801, 0.3429, 0.00894, 0.0001],
    ("Za", "ctt", 2): [3.46431, 0.29735, 0.00641, 6e-05],
    ("Za", "ctt", 3): [3.69878, 0.26168, 0.00471, 4e-05],
    ("Za", "ctt", 4): [3.94223, 0.23577, 0.00361, 3e-05],
    ("Za", "ctt", 5): [4.18364, 0.21616, 0.00288, 2e-05],
    ("Za", "ctt", 6): [4.42511, 0.20083, 0.00236, 1e-05],
    ("Za", "ctt", 7): [4.6758, 0.18972, 0.00201, 1e-05],
    ("Za", "ctt", 8): [4.91022, 0.17972, 0.00172, 1e-05],
    ("Za", "ctt", 9): [5.1324, 0.17093, 0.00149, 1e-05],
    ("Za", "ctt", 10): [5.35974, 0.16416, 0.00133, 1e-05],
    ("Za", "ctt", 11): [5.57662, 0.15801, 0.00119, 0.0],
    ("Za", "ctt", 12): [5.77426, 0.15196, 0.00106, 0.0],
    ("Za", "ctt", 13): [5.97557, 0.147, 0.00096, 0.0],
    ("Za", "n", 1): [0.55303, 0.65526, 0.06859, 0.00237],
    ("Za", "n", 2): [1.52382, 0.41591, 0.02077, 0.00042],
    ("Za", "n", 3): [2.16575, 0.33532, 0.01111, 0.00016],
    ("Za", "n", 4): [2.67119, 0.29092, 0.00729, 8e-05],
    ("Za", "n", 5): [3.09875, 0.26153, 0.00532, 5e-05],
    ("Za", "n", 6): [3.46981, 0.23897, 0.00407, 3e-05],
    ("Za", "n", 7): [3.79216, 0.21999, 0.0032, 2e-05],
    ("Za", "n", 8): [4.09891, 0.20601, 0.00264, 2e-05],
    ("Za", "n", 9): [4.38151, 0.19425, 0.00222, 1e-05],
    ("Za", "n", 10): [4.64131, 0.18377, 0.00189, 1e-05],
    ("Za", "n", 11): [4.89326, 0.17528, 0.00164, 1e-05],
    ("Za", "n", 12): [5.12444, 0.16734, 0.00143, 1e-05],
    ("Za", "n", 13): [5.34665, 0.16049, 0.00127, 0.0],
    ("Pu", "c", 1): [2.60001, 0.4424, 0.01681, 0.00021],
    ("Pu", "c", 2): [2.68634, 0.33117, 0.0096, 0.0001],
    ("Pu", "c", 3): [2.80162, 0.26268, 0.00599, 5e-05],
    ("Pu", "c", 4): [2.99092, 0.22759, 0.00433, 3e-05],
    ("Pu", "c", 5): [3.14425, 0.19977, 0.00322, 2e-05],
    ("Pu", "c", 6): [3.31252, 0.18079, 0.00254, 1e-05],
    ("Pu", "c", 7): [3.47287, 0.16591, 0.00206, 1e-05],
    ("Pu", "c", 8): [3.64037, 0.15495, 0.00173, 1e-05],
    ("Pu", "c", 9): [3.77281, 0.14413, 0.00145, 1e-05],
    ("Pu", "c", 10): [3.91062, 0.13558, 0.00124, 1e-05],
    ("Pu", "c", 11): [4.06193, 0.12934, 0.00109, 0.0],
    ("Pu", "c", 12): [4.19313, 0.1231, 0.00096, 0.0],
    ("Pu", "c", 13): [4.33881, 0.11856, 0.00087, 0.0],
    ("Pu", "ct", 1): [3.34748, 0.28382, 0.00617, 5e-05],
    ("Pu", "ct", 2): [3.40558, 0.2435, 0.00453, 3e-05],
    ("Pu", "ct", 3): [3.47145, 0.21234, 0.00341, 2e-05],
    ("Pu", "ct", 4): [3.57082, 0.18982, 0.00266, 2e-05],
    ("Pu", "ct", 5): [3.69775, 0.17423, 0.00219, 1e-05],
    ("Pu", "ct", 6): [3.7894, 0.15873, 0.00177, 1e-05],
    ("Pu", "ct", 7): [3.91884, 0.1484, 0.0015, 1e-05],
    ("Pu", "ct", 8): [4.05433, 0.13998, 0.0013, 1e-05],
    ("Pu", "ct", 9): [4.17302, 0.13223, 0.00113, 0.0],
    ("Pu", "ct", 10): [4.28906, 0.12525, 0.00098, 0.0],
    ("Pu", "ct", 11): [4.41216, 0.11968, 0.00087, 0.0],
    ("Pu", "ct", 12): [4.53118, 0.11474, 0.00078, 0.0],
    ("Pu", "ct", 13): [4.64504, 0.11031, 0.0007, 0.0],
    ("Pu", "ctt", 1): [3.85968, 0.22416, 0.00349, 2e-05],
    ("Pu", "ctt", 2): [3.91972, 0.20162, 0.0028, 2e-05],
    ("Pu", "ctt", 3): [3.97918, 0.1827, 0.00227, 1e-05],
    ("Pu", "ctt", 4): [4.05146, 0.16712, 0.00187, 1e-05],
    ("Pu", "ctt", 5): [4.12433, 0.15404, 0.00156, 1e-05],
    ("Pu", "ctt", 6): [4.23093, 0.14465, 0.00134, 1e-05],
    ("Pu", "ctt", 7): [4.33144, 0.13626, 0.00117, 0.0],
    ("Pu", "ctt", 8): [4.4308, 0.12882, 0.00102, 0.0],
    ("Pu", "ctt", 9): [4.5243, 0.12212, 0.00089, 0.0],
    ("Pu", "ctt", 10): [4.62967, 0.11674, 0.0008, 0.0],
    ("Pu", "ctt", 11): [4.73818, 0.11213, 0.00072, 0.0],
    ("Pu", "ctt", 12): [4.85901, 0.10858, 0.00066, 0.0],
    ("Pu", "ctt", 13): [4.95855, 0.10462, 0.0006, 0.0],
    ("Pu", "n", 1): [2.16876, 0.78365, 0.04915, 0.00087],
    ("Pu", "n", 2): [2.30141, 0.45201, 0.01852, 0.00024],
    ("Pu", "n", 3): [2.52273, 0.332, 0.01012, 0.00011],
    ("Pu", "n", 4): [2.67678, 0.26123, 0.00616, 5e-05],
    ("Pu", "n", 5): [2.8568, 0.22076, 0.00423, 3e-05],
    ("Pu", "n", 6): [3.02567, 0.19328, 0.0031, 2e-05],
    ("Pu", "n", 7): [3.21966, 0.17646, 0.00247, 1e-05],
    ("Pu", "n", 8): [3.39207, 0.16247, 0.00201, 1e-05],
    ("Pu", "n", 9): [3.56443, 0.15185, 0.00169, 1e-05],
    ("Pu", "n", 10): [3.73629, 0.14344, 0.00145, 1e-05],
    ("Pu", "n", 11): [3.87581, 0.13476, 0.00124, 1e-05],
    ("Pu", "n", 12): [4.02504, 0.12813, 0.00108, 0.0],
    ("Pu", "n", 13): [4.17035, 0.12253, 0.00095, 0.0],
}

PVAL_SMALL_P = {
    ("Pz", "c", 1): [0.45193, 0.09204, 0.00056],
    ("Pz", "c", 2): [1.60992, 0.07326, 0.00025],
    ("Pz", "c", 3): [2.73637, 0.06117, 0.00013],
    ("Pz", "c", 4): [3.86247, 0.05259, 8e-05],
    ("Pz", "c", 5): [4.98988, 0.04609, 5e-05],
    ("Pz", "c", 6): [6.12249, 0.04108, 3e-05],
    ("Pz", "c", 7): [7.29898, 0.03731, 3e-05],
    ("Pz", "c", 8): [8.47726, 0.03414, 2e-05],
    ("Pz", "c", 9): [9.69336, 0.03163, 1e-05],
    ("Pz", "c", 10): [10.73233, 0.02881, 1e-05],
    ("Pz", "c", 11): [12.17754, 0.02772, 1e-05],
    ("Pz", "c", 12): [13.25437, 0.02564, 1e-05],
    ("Pz", "c", 13): [14.36937, 0.02394, 1e-05],
    ("Pz", "ct", 1): [1.1245, 0.07914, 0.00033],
    ("Pz", "ct", 2): [2.43729, 0.06301, 0.00015],
    ("Pz", "ct", 3): [3.65957, 0.05362, 8e-05],
    ("Pz", "ct", 4): [4.86466, 0.04704, 5e-05],
    ("Pz", "ct", 5): [6.03527, 0.04176, 4e-05],
    ("Pz", "ct", 6): [7.22436, 0.03778, 3e-05],
    ("Pz", "ct", 7): [8.47405, 0.03482, 2e-05],
    ("Pz", "ct", 8): [9.69001, 0.03214, 2e-05],
    ("Pz", "ct", 9): [10.82288, 0.02954, 1e-05],
    ("Pz", "ct", 10): [12.0289, 0.02756, 1e-05],
    ("Pz", "ct", 11): [13.05972, 0.02535, 1e-05],
    ("Pz", "ct", 12): [14.40882, 0.02422, 1e-05],
    ("Pz", "ct", 13): [15.60999, 0.02287, 1e-05],
    ("Pz", "ctt", 1): [1.56669, 0.07093, 0.00023],
    ("Pz", "ctt", 2): [3.0293, 0.05647, 0.0001],
    ("Pz", "ctt", 3): [4.33499, 0.04811, 6e-05],
    ("Pz", "ctt", 4): [5.61336, 0.04253, 4e-05],
    ("Pz", "ctt", 5): [6.82644, 0.03799, 3e-05],
    ("Pz", "ctt", 6): [8.07941, 0.0347, 2e-05],
    ("Pz", "ctt", 7): [9.32983, 0.03202, 2e-05],
    ("Pz", "ctt", 8): [10.57117, 0.02971, 1e-05],
    ("Pz", "ctt", 9): [11.70119, 0.02738, 1e-05],
    ("Pz", "ctt", 10): [13.00781, 0.02592, 1e-05],
    ("Pz", "ctt", 11): [14.09769, 0.02406, 1e-05],
    ("Pz", "ctt", 12): [15.1912, 0.02246, 0.0],
    ("Pz", "ctt", 13): [16.65065, 0.02175, 0.0],
    ("Pz", "n", 1): [-0.05747, 0.10528, 0.0009],
    ("Pz", "n", 2): [1.07587, 0.08183, 0.00036],
    ("Pz", "n", 3): [2.1909, 0.0669, 0.00018],
    ("Pz", "n", 4): [3.28615, 0.05624, 0.0001],
    ("Pz", "n", 5): [4.4174, 0.04898, 6e-05],
    ("Pz", "n", 6): [5.55883, 0.04344, 4e-05],
    ("Pz", "n", 7): [6.78555, 0.03961, 3e-05],
    ("Pz", "n", 8): [7.96394, 0.03604, 2e-05],
    ("Pz", "n", 9): [9.12275, 0.03299, 2e-05],
    ("Pz", "n", 10): [10.27289, 0.03036, 1e-05],
    ("Pz", "n", 11): [11.43023, 0.02817, 1e-05],
    ("Pz", "n", 12): [12.64695, 0.02642, 1e-05],
    ("Pz", "n", 13): [13.70024, 0.02446, 1e-05],
    ("Zt", "c", 1): [2.15274, 1.43372, 0.03774],
    ("Zt", "c", 2): [2.86542, 1.46933, 0.03567],
    ("Zt", "c", 3): [3.47725, 1.48967, 0.0327],
    ("Zt", "c", 4): [4.02426, 1.50826, 0.0309],
    ("Zt", "c", 5): [4.55062, 1.53636, 0.03075],
    ("Zt", "c", 6): [4.97087, 1.53467, 0.02808],
    ("Zt", "c", 7): [5.4352, 1.5619, 0.02865],
    ("Zt", "c", 8): [5.87116, 1.58509, 0.02893],
    ("Zt", "c", 9): [6.27076, 1.60193, 0.02887],
    ("Zt", "c", 10): [6.65584, 1.61836, 0.02884],
    ("Zt", "c", 11): [7.04665, 1.63999, 0.02927],
    ("Zt", "c", 12): [7.34635, 1.6372, 0.02794],
    ("Zt", "c", 13): [7.71357, 1.65823, 0.0285],
    ("Zt", "ct", 1): [3.18907, 1.5673, 0.04462],
    ("Zt", "ct", 2): [3.63675, 1.52843, 0.03541],
    ("Zt", "ct", 3): [4.12061, 1.53019, 0.0323],
    ("Zt", "ct", 4): [4.62182, 1.55498, 0.03222],
    ("Zt", "ct", 5): [5.07545, 1.56917, 0.03116],
    ("Zt", "ct", 6): [5.52368, 1.59158, 0.03132],
    ("Zt", "ct", 7): [5.94315, 1.60992, 0.03123],
    ("Zt", "ct", 8): [6.32313, 1.61964, 0.03049],
    ("Zt", "ct", 9): [6.6689, 1.62231, 0.02922],
    ("Zt", "ct", 10): [7.08013, 1.65123, 0.03027],
    ("Zt", "ct", 11): [7.38367, 1.64793, 0.02875],
    ("Zt", "ct", 12): [7.65112, 1.63814, 0.02695],
    ("Zt", "ct", 13): [7.97788, 1.65001, 0.02691],
    ("Zt", "ctt", 1): [3.94459, 1.6281, 0.04496],
    ("Zt", "ctt", 2): [4.3414, 1.59843, 0.03852],
    ("Zt", "ctt", 3): [4.74258, 1.58427, 0.03432],
    ("Zt", "ctt", 4): [5.15283, 1.58514, 0.03211],
    ("Zt", "ctt", 5): [5.60329, 1.61109, 0.0327],
    ("Zt", "ctt", 6): [5.98547, 1.61779, 0.03164],
    ("Zt", "ctt", 7): [6.3174, 1.61114, 0.02944],
    ("Zt", "ctt", 8): [6.68154, 1.62229, 0.02906],
    ("Zt", "ctt", 9): [7.02859, 1.63055, 0.02843],
    ("Zt", "ctt", 10): [7.30663, 1.62041, 0.02645],
    ("Zt", "ctt", 11): [7.58787, 1.61583, 0.0251],
    ("Zt", "ctt", 12): [7.95609, 1.6411, 0.02614],
    ("Zt", "ctt", 13): [8.31543, 1.66521, 0.02706],
    ("Zt", "n", 1): [0.61974, 1.22244, 0.02913],
    ("Zt", "n", 2): [1.87437, 1.3594, 0.03136],
    ("Zt", "n", 3): [2.71208, 1.41759, 0.02969],
    ("Zt", "n", 4): [3.39311, 1.46254, 0.02983],
    ("Zt", "n", 5): [4.01535, 1.51235, 0.03159],
    ("Zt", "n", 6): [4.4961, 1.51783, 0.02887],
    ("Zt", "n", 7): [4.99955, 1.54882, 0.02942],
    ("Zt", "n", 8): [5.4691, 1.5776, 0.03016],
    ("Zt", "n", 9): [5.9098, 1.60018, 0.03021],
    ("Zt", "n", 10): [6.32025, 1.61987, 0.03034],
    ("Zt", "n", 11): [6.62021, 1.60615, 0.02768],
    ("Zt", "n", 12): [6.99808, 1.62546, 0.0281],
    ("Zt", "n", 13): [7.27338, 1.61388, 0.02601],
    ("Za", "c", 1): [0.49201, 0.18132, 0.00212],
    ("Za", "c", 2): [0.95833, 0.15241, 0.00126],
    ("Za", "c", 3): [1.33525, 0.13514, 0.00087],
    ("Za", "c", 4): [1.67767, 0.12445, 0.00067],
    ("Za", "c", 5): [1.9852, 0.11646, 0.00054],
    ("Za", "c", 6): [2.24864, 0.10941, 0.00044],
    ("Za", "c", 7): [2.51042, 0.10437, 0.00038],
    ("Za", "c", 8): [2.76101, 0.10031, 0.00033],
    ("Za", "c", 9): [2.98927, 0.09654, 0.0003],
    ("Za", "c", 10): [3.19957, 0.09304, 0.00026],
    ("Za", "c", 11): [3.41772, 0.09042, 0.00024],
    ("Za", "c", 12): [3.60051, 0.08742, 0.00022],
    ("Za", "c", 13): [3.79258, 0.08511, 0.0002],
    ("Za", "ct", 1): [1.13314, 0.15538, 0.00126],
    ("Za", "ct", 2): [1.43203, 0.13675, 0.00087],
    ("Za", "ct", 3): [1.73177, 0.12512, 0.00067],
    ("Za", "ct", 4): [2.0149, 0.11671, 0.00054],
    ("Za", "ct", 5): [2.28321, 0.1102, 0.00045],
    ("Za", "ct", 6): [2.53576, 0.10494, 0.00039],
    ("Za", "ct", 7): [2.77622, 0.10061, 0.00034],
    ("Za", "ct", 8): [2.99579, 0.09661, 0.0003],
    ("Za", "ct", 9): [3.20694, 0.09317, 0.00026],
    ("Za", "ct", 10): [3.4265, 0.09062, 0.00024],
    ("Za", "ct", 11): [3.60963, 0.08758, 0.00022],
    ("Za", "ct", 12): [3.78959, 0.08496, 0.0002],
    ("Za", "ct", 13): [3.96483, 0.08266, 0.00018],
    ("Za", "ctt", 1): [1.56913, 0.13958, 0.00089],
    ("Za", "ctt", 2): [1.82924, 0.1271, 0.00068],
    ("Za", "ctt", 3): [2.07647, 0.11758, 0.00054],
    ("Za", "ctt", 4): [2.32635, 0.11069, 0.00045],
    ("Za", "ctt", 5): [2.57097, 0.1054, 0.00039],
    ("Za", "ctt", 6): [2.79814, 0.1008, 0.00034],
    ("Za", "ctt", 7): [3.00644, 0.09651, 0.00029],
    ("Za", "ctt", 8): [3.21701, 0.09318, 0.00026],
    ("Za", "ctt", 9): [3.41558, 0.09008, 0.00024],
    ("Za", "ctt", 10): [3.58908, 0.08684, 0.00021],
    ("Za", "ctt", 11): [3.77336, 0.08441, 0.00019],
    ("Za", "ctt", 12): [3.96019, 0.08242, 0.00018],
    ("Za", "ctt", 13): [4.14791, 0.08074, 0.00017],
    ("Za", "n", 1): [-0.22028, 0.20529, 0.00361],
    ("Za", "n", 2): [0.50165, 0.16096, 0.00161],
    ("Za", "n", 3): [0.98829, 0.14107, 0.00104],
    ("Za", "n", 4): [1.38049, 0.12867, 0.00077],
    ("Za", "n", 5): [1.72829, 0.12029, 0.00061],
    ("Za", "n", 6): [2.00943, 0.11223, 0.00049],
    ("Za", "n", 7): [2.28843, 0.10673, 0.00042],
    ("Za", "n", 8): [2.54902, 0.10226, 0.00036],
    ("Za", "n", 9): [2.793, 0.09831, 0.00032],
    ("Za", "n", 10): [3.02258, 0.0949, 0.00028],
    ("Za", "n", 11): [3.21856, 0.09124, 0.00025],
    ("Za", "n", 12): [3.42417, 0.08859, 0.00023],
    ("Za", "n", 13): [3.60585, 0.08572, 0.0002],
    ("Pu", "c", 1): [0.45193, 0.09204, 0.00056],
    ("Pu", "c", 2): [0.71358, 0.08321, 0.00041],
    ("Pu", "c", 3): [0.9431, 0.07705, 0.00032],
    ("Pu", "c", 4): [1.14746, 0.07232, 0.00026],
    ("Pu", "c", 5): [1.34246, 0.06882, 0.00022],
    ("Pu", "c", 6): [1.52562, 0.06598, 0.00019],
    ("Pu", "c", 7): [1.67958, 0.06308, 0.00017],
    ("Pu", "c", 8): [1.83559, 0.0609, 0.00015],
    ("Pu", "c", 9): [1.9852, 0.05905, 0.00014],
    ("Pu", "c", 10): [2.12475, 0.0573, 0.00012],
    ("Pu", "c", 11): [2.2574, 0.05572, 0.00011],
    ("Pu", "c", 12): [2.39833, 0.05456, 0.00011],
    ("Pu", "c", 13): [2.53614, 0.05353, 0.0001],
    ("Pu", "ct", 1): [1.1245, 0.07914, 0.00033],
    ("Pu", "ct", 2): [1.29376, 0.07418, 0.00027],
    ("Pu", "ct", 3): [1.45577, 0.07015, 0.00023],
    ("Pu", "ct", 4): [1.60122, 0.06648, 0.00019],
    ("Pu", "ct", 5): [1.7565, 0.06387, 0.00017],
    ("Pu", "ct", 6): [1.89874, 0.06143, 0.00015],
    ("Pu", "ct", 7): [2.05631, 0.05983, 0.00014],
    ("Pu", "ct", 8): [2.19271, 0.05805, 0.00013],
    ("Pu", "ct", 9): [2.30814, 0.05612, 0.00011],
    ("Pu", "ct", 10): [2.44309, 0.05489, 0.00011],
    ("Pu", "ct", 11): [2.57674, 0.05382, 0.0001],
    ("Pu", "ct", 12): [2.69108, 0.05254, 9e-05],
    ("Pu", "ct", 13): [2.81595, 0.05163, 9e-05],
    ("Pu", "ctt", 1): [1.56669, 0.07093, 0.00023],
    ("Pu", "ctt", 2): [1.71033, 0.0677, 0.0002],
    ("Pu", "ctt", 3): [1.8431, 0.0647, 0.00017],
    ("Pu", "ctt", 4): [1.97417, 0.06217, 0.00015],
    ("Pu", "ctt", 5): [2.09526, 0.05981, 0.00014],
    ("Pu", "ctt", 6): [2.23859, 0.05831, 0.00013],
    ("Pu", "ctt", 7): [2.361, 0.05661, 0.00012],
    ("Pu", "ctt", 8): [2.47068, 0.05488, 0.00011],
    ("Pu", "ctt", 9): [2.59076, 0.0536, 0.0001],
    ("Pu", "ctt", 10): [2.70495, 0.05235, 9e-05],
    ("Pu", "ctt", 11): [2.82676, 0.05142, 9e-05],
    ("Pu", "ctt", 12): [2.95925, 0.0508, 8e-05],
    ("Pu", "ctt", 13): [3.06002, 0.04974, 8e-05],
    ("Pu", "n", 1): [-0.05747, 0.10528, 0.0009],
    ("Pu", "n", 2): [0.34074, 0.09007, 0.00054],
    ("Pu", "n", 3): [0.64861, 0.0822, 0.0004],
    ("Pu", "n", 4): [0.909, 0.07687, 0.00032],
    ("Pu", "n", 5): [1.12007, 0.07207, 0.00026],
    ("Pu", "n", 6): [1.30491, 0.06806, 0.00022],
    ("Pu", "n", 7): [1.48994, 0.06532, 0.00019],
    ("Pu", "n", 8): [1.66235, 0.06292, 0.00017],
    ("Pu", "n", 9): [1.82352, 0.06082, 0.00015],
    ("Pu", "n", 10): [1.98767, 0.05928, 0.00014],
    ("Pu", "n", 11): [2.11845, 0.05729, 0.00012],
    ("Pu", "n", 12): [2.25422, 0.05575, 0.00011],
    ("Pu", "n", 13): [2.4007, 0.05469, 0.00011],
}

PVAL_TAU_MAX = {
    ("Pz", "c", 1): -23.11139,
    ("Pz", "c", 2): inf,
    ("Pz", "c", 3): inf,
    ("Pz", "c", 4): inf,
    ("Pz", "c", 5): inf,
    ("Pz", "c", 6): inf,
    ("Pz", "c", 7): inf,
    ("Pz", "c", 8): inf,
    ("Pz", "c", 9): inf,
    ("Pz", "c", 10): inf,
    ("Pz", "c", 11): inf,
    ("Pz", "c", 12): inf,
    ("Pz", "c", 13): inf,
    ("Pz", "ct", 1): inf,
    ("Pz", "ct", 2): inf,
    ("Pz", "ct", 3): inf,
    ("Pz", "ct", 4): inf,
    ("Pz", "ct", 5): inf,
    ("Pz", "ct", 6): inf,
    ("Pz", "ct", 7): inf,
    ("Pz", "ct", 8): inf,
    ("Pz", "ct", 9): inf,
    ("Pz", "ct", 10): inf,
    ("Pz", "ct", 11): inf,
    ("Pz", "ct", 12): inf,
    ("Pz", "ct", 13): inf,
    ("Pz", "ctt", 1): inf,
    ("Pz", "ctt", 2): inf,
    ("Pz", "ctt", 3): inf,
    ("Pz", "ctt", 4): inf,
    ("Pz", "ctt", 5): inf,
    ("Pz", "ctt", 6): inf,
    ("Pz", "ctt", 7): inf,
    ("Pz", "ctt", 8): inf,
    ("Pz", "ctt", 9): inf,
    ("Pz", "ctt", 10): inf,
    ("Pz", "ctt", 11): inf,
    ("Pz", "ctt", 12): inf,
    ("Pz", "ctt", 13): inf,
    ("Pz", "n", 1): -11.42329,
    ("Pz", "n", 2): inf,
    ("Pz", "n", 3): inf,
    ("Pz", "n", 4): inf,
    ("Pz", "n", 5): inf,
    ("Pz", "n", 6): inf,
    ("Pz", "n", 7): inf,
    ("Pz", "n", 8): inf,
    ("Pz", "n", 9): inf,
    ("Pz", "n", 10): inf,
    ("Pz", "n", 11): inf,
    ("Pz", "n", 12): inf,
    ("Pz", "n", 13): inf,
    ("Zt", "c", 1): 2.71521,
    ("Zt", "c", 2): 1.63732,
    ("Zt", "c", 3): 1.27502,
    ("Zt", "c", 4): 1.26783,
    ("Zt", "c", 5): 1.49105,
    ("Zt", "c", 6): 1.90583,
    ("Zt", "c", 7): 2.44943,
    ("Zt", "c", 8): 3.03395,
    ("Zt", "c", 9): 3.70877,
    ("Zt", "c", 10): 4.53686,
    ("Zt", "c", 11): 5.7562,
    ("Zt", "c", 12): 6.83575,
    ("Zt", "c", 13): 8.27005,
    ("Zt", "ct", 1): 1.59077,
    ("Zt", "ct", 2): 1.53734,
    ("Zt", "ct", 3): 1.56693,
    ("Zt", "ct", 4): 1.73625,
    ("Zt", "ct", 5): 2.09127,
    ("Zt", "ct", 6): 2.49136,
    ("Zt", "ct", 7): 3.02879,
    ("Zt", "ct", 8): 3.81851,
    ("Zt", "ct", 9): 4.6786,
    ("Zt", "ct", 10): 5.6471,
    ("Zt", "ct", 11): 7.09703,
    ("Zt", "ct", 12): 8.53513,
    ("Zt", "ct", 13): 9.79738,
    ("Zt", "ctt", 1): 1.70817,
    ("Zt", "ctt", 2): 1.88508,
    ("Zt", "ctt", 3): 2.1786,
    ("Zt", "ctt", 4): 2.5985,
    ("Zt", "ctt", 5): 3.01577,
    ("Zt", "ctt", 6): 3.62409,
    ("Zt", "ctt", 7): 4.22507,
    ("Zt", "ctt", 8): 4.885,
    ("Zt", "ctt", 9): 6.02507,
    ("Zt", "ctt", 10): 7.44599,
    ("Zt", "ctt", 11): 8.66452,
    ("Zt", "ctt", 12): 10.90002,
    ("Zt", "ctt", 13): 13.13352,
    ("Zt", "n", 1): inf,
    ("Zt", "n", 2): 2.20769,
    ("Zt", "n", 3): 1.53952,
    ("Zt", "n", 4): 1.54941,
    ("Zt", "n", 5): 1.70851,
    ("Zt", "n", 6): 2.02986,
    ("Zt", "n", 7): 2.5438,
    ("Zt", "n", 8): 3.17392,
    ("Zt", "n", 9): 3.94083,
    ("Zt", "n", 10): 4.75193,
    ("Zt", "n", 11): 6.00294,
    ("Zt", "n", 12): 7.0764,
    ("Zt", "n", 13): 8.32728,
    ("Za", "c", 1): inf,
    ("Za", "c", 2): inf,
    ("Za", "c", 3): inf,
    ("Za", "c", 4): inf,
    ("Za", "c", 5): inf,
    ("Za", "c", 6): inf,
    ("Za", "c", 7): inf,
    ("Za", "c", 8): inf,
    ("Za", "c", 9): inf,
    ("Za", "c", 10): inf,
    ("Za", "c", 11): inf,
    ("Za", "c", 12): inf,
    ("Za", "c", 13): inf,
    ("Za", "ct", 1): inf,
    ("Za", "ct", 2): inf,
    ("Za", "ct", 3): inf,
    ("Za", "ct", 4): inf,
    ("Za", "ct", 5): inf,
    ("Za", "ct", 6): inf,
    ("Za", "ct", 7): inf,
    ("Za", "ct", 8): inf,
    ("Za", "ct", 9): inf,
    ("Za", "ct", 10): inf,
    ("Za", "ct", 11): inf,
    ("Za", "ct", 12): inf,
    ("Za", "ct", 13): inf,
    ("Za", "ctt", 1): inf,
    ("Za", "ctt", 2): inf,
    ("Za", "ctt", 3): inf,
    ("Za", "ctt", 4): inf,
    ("Za", "ctt", 5): inf,
    ("Za", "ctt", 6): inf,
    ("Za", "ctt", 7): inf,
    ("Za", "ctt", 8): inf,
    ("Za", "ctt", 9): inf,
    ("Za", "ctt", 10): inf,
    ("Za", "ctt", 11): inf,
    ("Za", "ctt", 12): inf,
    ("Za", "ctt", 13): inf,
    ("Za", "n", 1): -8.70872,
    ("Za", "n", 2): inf,
    ("Za", "n", 3): inf,
    ("Za", "n", 4): inf,
    ("Za", "n", 5): inf,
    ("Za", "n", 6): inf,
    ("Za", "n", 7): inf,
    ("Za", "n", 8): inf,
    ("Za", "n", 9): inf,
    ("Za", "n", 10): inf,
    ("Za", "n", 11): inf,
    ("Za", "n", 12): inf,
    ("Za", "n", 13): inf,
    ("Pu", "c", 1): -23.11139,
    ("Pu", "c", 2): inf,
    ("Pu", "c", 3): inf,
    ("Pu", "c", 4): inf,
    ("Pu", "c", 5): inf,
    ("Pu", "c", 6): inf,
    ("Pu", "c", 7): inf,
    ("Pu", "c", 8): inf,
    ("Pu", "c", 9): inf,
    ("Pu", "c", 10): inf,
    ("Pu", "c", 11): inf,
    ("Pu", "c", 12): inf,
    ("Pu", "c", 13): inf,
    ("Pu", "ct", 1): inf,
    ("Pu", "ct", 2): inf,
    ("Pu", "ct", 3): inf,
    ("Pu", "ct", 4): inf,
    ("Pu", "ct", 5): inf,
    ("Pu", "ct", 6): inf,
    ("Pu", "ct", 7): inf,
    ("Pu", "ct", 8): inf,
    ("Pu", "ct", 9): inf,
    ("Pu", "ct", 10): inf,
    ("Pu", "ct", 11): inf,
    ("Pu", "ct", 12): inf,
    ("Pu", "ct", 13): inf,
    ("Pu", "ctt", 1): inf,
    ("Pu", "ctt", 2): inf,
    ("Pu", "ctt", 3): inf,
    ("Pu", "ctt", 4): inf,
    ("Pu", "ctt", 5): inf,
    ("Pu", "ctt", 6): inf,
    ("Pu", "ctt", 7): inf,
    ("Pu", "ctt", 8): inf,
    ("Pu", "ctt", 9): inf,
    ("Pu", "ctt", 10): inf,
    ("Pu", "ctt", 11): inf,
    ("Pu", "ctt", 12): inf,
    ("Pu", "ctt", 13): inf,
    ("Pu", "n", 1): -11.42329,
    ("Pu", "n", 2): -19.90302,
    ("Pu", "n", 3): inf,
    ("Pu", "n", 4): inf,
    ("Pu", "n", 5): inf,
    ("Pu", "n", 6): inf,
    ("Pu", "n", 7): inf,
    ("Pu", "n", 8): inf,
    ("Pu", "n", 9): inf,
    ("Pu", "n", 10): inf,
    ("Pu", "n", 11): inf,
    ("Pu", "n", 12): inf,
    ("Pu", "n", 13): inf,
}

PVAL_TAU_STAR = {
    ("Pz", "c", 1): -0.85495,
    ("Pz", "c", 2): -5.9284,
    ("Pz", "c", 3): -17.61559,
    ("Pz", "c", 4): -36.82599,
    ("Pz", "c", 5): -63.67738,
    ("Pz", "c", 6): -98.27482,
    ("Pz", "c", 7): -140.65869,
    ("Pz", "c", 8): -190.71133,
    ("Pz", "c", 9): -248.84712,
    ("Pz", "c", 10): -314.29919,
    ("Pz", "c", 11): -387.45671,
    ("Pz", "c", 12): -468.13713,
    ("Pz", "c", 13): -556.5918,
    ("Pz", "ct", 1): -3.09193,
    ("Pz", "ct", 2): -13.6393,
    ("Pz", "ct", 3): -32.52448,
    ("Pz", "ct", 4): -59.3465,
    ("Pz", "ct", 5): -93.99928,
    ("Pz", "ct", 6): -136.39465,
    ("Pz", "ct", 7): -186.61794,
    ("Pz", "ct", 8): -244.58018,
    ("Pz", "ct", 9): -310.21765,
    ("Pz", "ct", 10): -383.26063,
    ("Pz", "ct", 11): -464.22382,
    ("Pz", "ct", 12): -552.46764,
    ("Pz", "ct", 13): -648.3492,
    ("Pz", "ctt", 1): -5.81024,
    ("Pz", "ctt", 2): -22.10953,
    ("Pz", "ctt", 3): -47.93286,
    ("Pz", "ctt", 4): -82.06292,
    ("Pz", "ctt", 5): -124.474,
    ("Pz", "ctt", 6): -174.5215,
    ("Pz", "ctt", 7): -232.31935,
    ("Pz", "ctt", 8): -298.00516,
    ("Pz", "ctt", 9): -371.23283,
    ("Pz", "ctt", 10): -452.18681,
    ("Pz", "ctt", 11): -540.40106,
    ("Pz", "ctt", 12): -636.60242,
    ("Pz", "ctt", 13): -739.82309,
    ("Pz", "n", 1): -0.22313,
    ("Pz", "n", 2): -2.85212,
    ("Pz", "n", 3): -11.02783,
    ("Pz", "n", 4): -26.3634,
    ("Pz", "n", 5): -49.34333,
    ("Pz", "n", 6): -80.11079,
    ("Pz", "n", 7): -118.45555,
    ("Pz", "n", 8): -164.84755,
    ("Pz", "n", 9): -218.87307,
    ("Pz", "n", 10): -280.71706,
    ("Pz", "n", 11): -350.06555,
    ("Pz", "n", 12): -427.10826,
    ("Pz", "n", 13): -512.0003,
    ("Zt", "c", 1): 1.37009,
    ("Zt", "c", 2): 0.92517,
    ("Zt", "c", 3): 0.41622,
    ("Zt", "c", 4): -0.0896,
    ("Zt", "c", 5): -0.54361,
    ("Zt", "c", 6): -0.95427,
    ("Zt", "c", 7): -1.31103,
    ("Zt", "c", 8): -1.62936,
    ("Zt", "c", 9): -1.91606,
    ("Zt", "c", 10): -2.18022,
    ("Zt", "c", 11): -2.42676,
    ("Zt", "c", 12): -2.65963,
    ("Zt", "c", 13): -2.8771,
    ("Zt", "ct", 1): 0.38064,
    ("Zt", "ct", 2): 0.04876,
    ("Zt", "ct", 3): -0.30388,
    ("Zt", "ct", 4): -0.66219,
    ("Zt", "ct", 5): -1.00917,
    ("Zt", "ct", 6): -1.33868,
    ("Zt", "ct", 7): -1.64339,
    ("Zt", "ct", 8): -1.92677,
    ("Zt", "ct", 9): -2.18956,
    ("Zt", "ct", 10): -2.43395,
    ("Zt", "ct", 11): -2.66267,
    ("Zt", "ct", 12): -2.87762,
    ("Zt", "ct", 13): -3.08526,
    ("Zt", "ctt", 1): -0.24467,
    ("Zt", "ctt", 2): -0.52846,
    ("Zt", "ctt", 3): -0.82107,
    ("Zt", "ctt", 4): -1.11986,
    ("Zt", "ctt", 5): -1.41019,
    ("Zt", "ctt", 6): -1.69362,
    ("Zt", "ctt", 7): -1.96028,
    ("Zt", "ctt", 8): -2.21115,
    ("Zt", "ctt", 9): -2.44749,
    ("Zt", "ctt", 10): -2.67456,
    ("Zt", "ctt", 11): -3.21931,
    ("Zt", "ctt", 12): -3.41925,
    ("Zt", "ctt", 13): -3.70924,
    ("Zt", "n", 1): 2.82237,
    ("Zt", "n", 2): 1.96721,
    ("Zt", "n", 3): 1.10338,
    ("Zt", "n", 4): 0.37623,
    ("Zt", "n", 5): -0.17761,
    ("Zt", "n", 6): -0.62839,
    ("Zt", "n", 7): -1.01461,
    ("Zt", "n", 8): -1.35604,
    ("Zt", "n", 9): -1.66634,
    ("Zt", "n", 10): -1.94483,
    ("Zt", "n", 11): -2.20537,
    ("Zt", "n", 12): -2.45134,
    ("Zt", "n", 13): -3.06399,
    ("Za", "c", 1): 2.41302,
    ("Za", "c", 2): 1.89033,
    ("Za", "c", 3): 1.00252,
    ("Za", "c", 4): -0.25591,
    ("Za", "c", 5): -1.80419,
    ("Za", "c", 6): -3.59506,
    ("Za", "c", 7): -5.48384,
    ("Za", "c", 8): -7.45561,
    ("Za", "c", 9): -9.49432,
    ("Za", "c", 10): -11.62568,
    ("Za", "c", 11): -13.82878,
    ("Za", "c", 12): -16.11756,
    ("Za", "c", 13): -18.42803,
    ("Za", "ct", 1): 0.9615,
    ("Za", "ct", 2): 0.13513,
    ("Za", "ct", 3): -0.94433,
    ("Za", "ct", 4): -2.29727,
    ("Za", "ct", 5): -3.88018,
    ("Za", "ct", 6): -5.6607,
    ("Za", "ct", 7): -7.54212,
    ("Za", "ct", 8): -9.57132,
    ("Za", "ct", 9): -11.68489,
    ("Za", "ct", 10): -13.87383,
    ("Za", "ct", 11): -16.15171,
    ("Za", "ct", 12): -18.44444,
    ("Za", "ct", 13): -20.82704,
    ("Za", "ctt", 1): -0.76047,
    ("Za", "ctt", 2): -1.77978,
    ("Za", "ctt", 3): -3.00881,
    ("Za", "ctt", 4): -4.4564,
    ("Za", "ctt", 5): -6.09573,
    ("Za", "ctt", 6): -7.89769,
    ("Za", "ctt", 7): -9.84013,
    ("Za", "ctt", 8): -11.86907,
    ("Za", "ctt", 9): -14.03943,
    ("Za", "ctt", 10): -16.24953,
    ("Za", "ctt", 11): -18.55493,
    ("Za", "ctt", 12): -20.89991,
    ("Za", "ctt", 13): -23.32032,
    ("Za", "n", 1): 3.10481,
    ("Za", "n", 2): 2.86677,
    ("Za", "n", 3): 2.10244,
    ("Za", "n", 4): 0.94316,
    ("Za", "n", 5): -0.53959,
    ("Za", "n", 6): -2.18728,
    ("Za", "n", 7): -3.97034,
    ("Za", "n", 8): -5.85993,
    ("Za", "n", 9): -7.83425,
    ("Za", "n", 10): -9.86676,
    ("Za", "n", 11): -12.05137,
    ("Za", "n", 12): -14.29852,
    ("Za", "n", 13): -16.56746,
    ("Pu", "c", 1): -0.85495,
    ("Pu", "c", 2): -1.0942,
    ("Pu", "c", 3): -1.45092,
    ("Pu", "c", 4): -1.95591,
    ("Pu", "c", 5): -2.6491,
    ("Pu", "c", 6): -3.52581,
    ("Pu", "c", 7): -4.56918,
    ("Pu", "c", 8): -5.74068,
    ("Pu", "c", 9): -7.07286,
    ("Pu", "c", 10): -8.48929,
    ("Pu", "c", 11): -10.01146,
    ("Pu", "c", 12): -11.64436,
    ("Pu", "c", 13): -13.30203,
    ("Pu", "ct", 1): -3.09193,
    ("Pu", "ct", 2): -3.61362,
    ("Pu", "ct", 3): -4.2337,
    ("Pu", "ct", 4): -5.01097,
    ("Pu", "ct", 5): -5.92567,
    ("Pu", "ct", 6): -6.98054,
    ("Pu", "ct", 7): -8.16903,
    ("Pu", "ct", 8): -9.47533,
    ("Pu", "ct", 9): -10.86739,
    ("Pu", "ct", 10): -12.36814,
    ("Pu", "ct", 11): -13.98676,
    ("Pu", "ct", 12): -15.6557,
    ("Pu", "ct", 13): -17.43059,
    ("Pu", "ctt", 1): -5.81024,
    ("Pu", "ctt", 2): -6.53839,
    ("Pu", "ctt", 3): -7.36381,
    ("Pu", "ctt", 4): -8.32683,
    ("Pu", "ctt", 5): -9.4033,
    ("Pu", "ctt", 6): -10.61023,
    ("Pu", "ctt", 7): -11.90731,
    ("Pu", "ctt", 8): -13.32873,
    ("Pu", "ctt", 9): -14.82754,
    ("Pu", "ctt", 10): -16.43441,
    ("Pu", "ctt", 11): -18.10393,
    ("Pu", "ctt", 12): -19.8697,
    ("Pu", "ctt", 13): -21.68684,
    ("Pu", "n", 1): -0.22313,
    ("Pu", "n", 2): -0.3217,
    ("Pu", "n", 3): -0.49807,
    ("Pu", "n", 4): -0.8223,
    ("Pu", "n", 5): -1.35801,
    ("Pu", "n", 6): -2.12993,
    ("Pu", "n", 7): -3.07027,
    ("Pu", "n", 8): -4.18972,
    ("Pu", "n", 9): -5.43235,
    ("Pu", "n", 10): -6.78939,
    ("Pu", "n", 11): -8.23145,
    ("Pu", "n", 12): -9.77526,
    ("Pu", "n", 13): -11.37389,
}

PVAL_TAU_MIN = {
    ("Pz", "c", 1): -82.90566,
    ("Pz", "c", 2): -149.41501,
    ("Pz", "c", 3): -233.56497,
    ("Pz", "c", 4): -334.9419,
    ("Pz", "c", 5): -455.75311,
    ("Pz", "c", 6): -593.27745,
    ("Pz", "c", 7): -742.47839,
    ("Pz", "c", 8): -911.28194,
    ("Pz", "c", 9): -1088.974,
    ("Pz", "c", 10): -1322.67916,
    ("Pz", "c", 11): -1482.44098,
    ("Pz", "c", 12): -1744.1182,
    ("Pz", "c", 13): -2019.13003,
    ("Pz", "ct", 1): -121.13234,
    ("Pz", "ct", 2): -213.31086,
    ("Pz", "ct", 3): -315.5379,
    ("Pz", "ct", 4): -432.86156,
    ("Pz", "ct", 5): -570.99033,
    ("Pz", "ct", 6): -720.79502,
    ("Pz", "ct", 7): -878.01545,
    ("Pz", "ct", 8): -1055.57593,
    ("Pz", "ct", 9): -1268.18358,
    ("Pz", "ct", 10): -1484.84383,
    ("Pz", "ct", 11): -1764.35538,
    ("Pz", "ct", 12): -1980.09675,
    ("Pz", "ct", 13): -2251.28527,
    ("Pz", "ctt", 1): -154.99229,
    ("Pz", "ctt", 2): -270.96369,
    ("Pz", "ctt", 3): -397.37611,
    ("Pz", "ctt", 4): -535.36021,
    ("Pz", "ctt", 5): -695.67732,
    ("Pz", "ctt", 6): -863.18326,
    ("Pz", "ctt", 7): -1042.96764,
    ("Pz", "ctt", 8): -1240.94173,
    ("Pz", "ctt", 9): -1480.47069,
    ("Pz", "ctt", 10): -1689.83934,
    ("Pz", "ctt", 11): -1975.93776,
    ("Pz", "ctt", 12): -2283.69077,
    ("Pz", "ctt", 13): -2502.93084,
    ("Pz", "n", 1): -58.58649,
    ("Pz", "n", 2): -114.29489,
    ("Pz", "n", 3): -188.88045,
    ("Pz", "n", 4): -284.60886,
    ("Pz", "n", 5): -394.55984,
    ("Pz", "n", 6): -521.5213,
    ("Pz", "n", 7): -653.412,
    ("Pz", "n", 8): -813.57603,
    ("Pz", "n", 9): -991.2906,
    ("Pz", "n", 10): -1191.15793,
    ("Pz", "n", 11): -1403.73901,
    ("Pz", "n", 12): -1626.07115,
    ("Pz", "n", 13): -1910.49859,
    ("Zt", "c", 1): -18.99305,
    ("Zt", "c", 2): -20.59579,
    ("Zt", "c", 3): -22.77709,
    ("Zt", "c", 4): -24.40619,
    ("Zt", "c", 5): -24.98266,
    ("Zt", "c", 6): -27.32994,
    ("Zt", "c", 7): -27.25716,
    ("Zt", "c", 8): -27.39682,
    ("Zt", "c", 9): -27.74712,
    ("Zt", "c", 10): -28.05294,
    ("Zt", "c", 11): -28.01621,
    ("Zt", "c", 12): -29.29988,
    ("Zt", "c", 13): -29.08711,
    ("Zt", "ct", 1): -17.56297,
    ("Zt", "ct", 2): -21.58202,
    ("Zt", "ct", 3): -23.69061,
    ("Zt", "ct", 4): -24.13081,
    ("Zt", "ct", 5): -25.17794,
    ("Zt", "ct", 6): -25.40574,
    ("Zt", "ct", 7): -25.77781,
    ("Zt", "ct", 8): -26.56223,
    ("Zt", "ct", 9): -27.75908,
    ("Zt", "ct", 10): -27.27071,
    ("Zt", "ct", 11): -28.658,
    ("Zt", "ct", 12): -30.39636,
    ("Zt", "ct", 13): -30.65643,
    ("Zt", "ctt", 1): -18.1078,
    ("Zt", "ctt", 2): -20.74805,
    ("Zt", "ctt", 3): -23.07877,
    ("Zt", "ctt", 4): -24.68154,
    ("Zt", "ctt", 5): -24.63423,
    ("Zt", "ctt", 6): -25.56252,
    ("Zt", "ctt", 7): -27.36503,
    ("Zt", "ctt", 8): -27.91123,
    ("Zt", "ctt", 9): -28.68145,
    ("Zt", "ctt", 10): -30.63173,
    ("Zt", "ctt", 11): -32.18754,
    ("Zt", "ctt", 12): -31.39536,
    ("Zt", "ctt", 13): -30.76757,
    ("Zt", "n", 1): -20.98275,
    ("Zt", "n", 2): -21.6771,
    ("Zt", "n", 3): -23.87376,
    ("Zt", "n", 4): -24.5162,
    ("Zt", "n", 5): -23.9388,
    ("Zt", "n", 6): -26.29092,
    ("Zt", "n", 7): -26.32648,
    ("Zt", "n", 8): -26.15116,
    ("Zt", "n", 9): -26.48761,
    ("Zt", "n", 10): -26.69846,
    ("Zt", "n", 11): -29.01731,
    ("Zt", "n", 12): -28.92572,
    ("Zt", "n", 13): -31.02788,
    ("Za", "c", 1): -42.7698,
    ("Za", "c", 2): -60.56532,
    ("Za", "c", 3): -77.97882,
    ("Za", "c", 4): -93.21808,
    ("Za", "c", 5): -107.94646,
    ("Za", "c", 6): -123.23299,
    ("Za", "c", 7): -136.84941,
    ("Za", "c", 8): -149.75361,
    ("Za", "c", 9): -162.8911,
    ("Za", "c", 10): -176.55355,
    ("Za", "c", 11): -188.64662,
    ("Za", "c", 12): -202.58674,
    ("Za", "c", 13): -215.16493,
    ("Za", "ct", 1): -61.66949,
    ("Za", "ct", 2): -78.32051,
    ("Za", "ct", 3): -93.69855,
    ("Za", "ct", 4): -108.34856,
    ("Za", "ct", 5): -122.554,
    ("Za", "ct", 6): -136.13098,
    ("Za", "ct", 7): -149.19996,
    ("Za", "ct", 8): -162.74946,
    ("Za", "ct", 9): -176.17069,
    ("Za", "ct", 10): -187.9011,
    ("Za", "ct", 11): -202.23935,
    ("Za", "ct", 12): -215.95244,
    ("Za", "ct", 13): -229.12287,
    ("Za", "ctt", 1): -78.23024,
    ("Za", "ctt", 2): -93.10385,
    ("Za", "ctt", 3): -108.3135,
    ("Za", "ctt", 4): -122.70004,
    ("Za", "ctt", 5): -136.0247,
    ("Za", "ctt", 6): -149.29068,
    ("Za", "ctt", 7): -163.63176,
    ("Za", "ctt", 8): -176.5101,
    ("Za", "ctt", 9): -190.07313,
    ("Za", "ctt", 10): -205.42299,
    ("Za", "ctt", 11): -218.69053,
    ("Za", "ctt", 12): -230.68278,
    ("Za", "ctt", 13): -241.94294,
    ("Za", "n", 1): -28.39653,
    ("Za", "n", 2): -50.02287,
    ("Za", "n", 3): -68.08717,
    ("Za", "n", 4): -84.01911,
    ("Za", "n", 5): -98.18648,
    ("Za", "n", 6): -114.23523,
    ("Za", "n", 7): -128.25059,
    ("Za", "n", 8): -141.27654,
    ("Za", "n", 9): -154.72282,
    ("Za", "n", 10): -167.61524,
    ("Za", "n", 11): -182.42171,
    ("Za", "n", 12): -194.6711,
    ("Za", "n", 13): -209.08598,
    ("Pu", "c", 1): -82.90566,
    ("Pu", "c", 2): -101.98951,
    ("Pu", "c", 3): -120.07035,
    ("Pu", "c", 4): -137.54205,
    ("Pu", "c", 5): -153.78932,
    ("Pu", "c", 6): -169.22026,
    ("Pu", "c", 7): -186.48503,
    ("Pu", "c", 8): -201.64432,
    ("Pu", "c", 9): -216.10173,
    ("Pu", "c", 10): -231.20021,
    ("Pu", "c", 11): -246.11454,
    ("Pu", "c", 12): -258.97989,
    ("Pu", "c", 13): -271.38134,
    ("Pu", "ct", 1): -121.13234,
    ("Pu", "ct", 2): -137.1377,
    ("Pu", "ct", 3): -153.07019,
    ("Pu", "ct", 4): -170.46382,
    ("Pu", "ct", 5): -185.69559,
    ("Pu", "ct", 6): -201.87774,
    ("Pu", "ct", 7): -214.66366,
    ("Pu", "ct", 8): -229.38481,
    ("Pu", "ct", 9): -246.09632,
    ("Pu", "ct", 10): -258.92014,
    ("Pu", "ct", 11): -271.00378,
    ("Pu", "ct", 12): -285.79541,
    ("Pu", "ct", 13): -297.53719,
    ("Pu", "ctt", 1): -154.99229,
    ("Pu", "ctt", 2): -169.48055,
    ("Pu", "ctt", 3): -185.16082,
    ("Pu", "ctt", 4): -200.56588,
    ("Pu", "ctt", 5): -216.66315,
    ("Pu", "ctt", 6): -229.28361,
    ("Pu", "ctt", 7): -243.89672,
    ("Pu", "ctt", 8): -260.01949,
    ("Pu", "ctt", 9): -273.70207,
    ("Pu", "ctt", 10): -288.39675,
    ("Pu", "ctt", 11): -300.52165,
    ("Pu", "ctt", 12): -310.00217,
    ("Pu", "ctt", 13): -324.32704,
    ("Pu", "n", 1): -58.58649,
    ("Pu", "n", 2): -82.70634,
    ("Pu", "n", 3): -101.90023,
    ("Pu", "n", 4): -119.01021,
    ("Pu", "n", 5): -137.18282,
    ("Pu", "n", 6): -155.60654,
    ("Pu", "n", 7): -170.835,
    ("Pu", "n", 8): -186.22292,
    ("Pu", "n", 9): -201.44526,
    ("Pu", "n", 10): -214.46891,
    ("Pu", "n", 11): -230.89563,
    ("Pu", "n", 12): -245.72855,
    ("Pu", "n", 13): -257.9681,
}
