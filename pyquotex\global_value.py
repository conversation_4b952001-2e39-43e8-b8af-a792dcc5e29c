SSID = None
check_websocket_if_connect = None
ssl_Mutual_exclusion = False
ssl_Mutual_exclusion_write = False
started_listen_instruments = True
check_rejected_connection = False
check_accepted_connection = False
check_websocket_if_error = False
websocket_error_reason = None
balance_id = None

def reset_websocket_error():
    """إعادة تعيين حالة أخطاء WebSocket"""
    global check_websocket_if_error, websocket_error_reason
    check_websocket_if_error = False
    websocket_error_reason = None
