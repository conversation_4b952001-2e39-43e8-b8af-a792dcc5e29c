"""
CANFX Strategy V2 - Advanced Binary Options Trading Strategy
============================================================

A comprehensive 4-layer trading strategy combining:
1. Technical Analysis (TA)
2. Quantitative Analysis (QA) 
3. Behavioral Analysis (BA)
4. Artificial Intelligence (AI/ML)

Target: 80%+ win rate for binary options trading
"""

from .decision_maker import StrategyDecisionMaker
from .technical_layer import TechnicalAnalysisLayer
from .quantitative_layer import QuantitativeAnalysisLayer
from .behavioral_layer import BehavioralAnalysisLayer
from .ai_layer import AIAnalysisLayer

__version__ = "2.0.0"
__author__ = "CANFX Strategy Team"

__all__ = [
    'StrategyDecisionMaker',
    'TechnicalAnalysisLayer',
    'QuantitativeAnalysisLayer', 
    'BehavioralAnalysisLayer',
    'AIAnalysisLayer'
]
