"""
Constants for Quotex API
"""

# Asset codes mapping
codes = {
    # Major Forex Pairs
    "EURUSD": "EURUSD",
    "GBPUSD": "GBPUSD", 
    "USDJPY": "USDJPY",
    "AUDUSD": "AUDUSD",
    "USDCAD": "USDCAD",
    "NZDUSD": "NZDUSD",
    "USDCHF": "USDCHF",
    
    # Minor Forex Pairs
    "EURJPY": "EURJPY",
    "GBPJPY": "GBPJPY",
    "AUDJPY": "AUDJPY",
    "EURGBP": "EURGBP",
    "EURAUD": "EURAUD",
    "GBPAUD": "GBPAUD",
    "AUDCAD": "AUDCAD",
    "CADJPY": "CADJPY",
    "CHFJPY": "CHFJPY",
    "EURNZD": "EURNZD",
    "GBPNZD": "GBPNZD",
    "AUDNZD": "AUDNZD",
    "NZDJPY": "NZDJPY",
    
    # Exotic Pairs
    "USDTRY": "USDTRY",
    "USDZAR": "USDZAR",
    "USDMXN": "USDMXN",
    "USDBRL": "USDBRL",
    "USDRUB": "USDRUB",
    "USDPLN": "USDPLN",
    "USDSEK": "USDSEK",
    "USDNOK": "USDNOK",
    "USDDKK": "USDDKK",
    
    # Cryptocurrencies
    "BTCUSD": "BTCUSD",
    "ETHUSD": "ETHUSD",
    "LTCUSD": "LTCUSD",
    "XRPUSD": "XRPUSD",
    "BCHUSD": "BCHUSD",
    "ADAUSD": "ADAUSD",
    "DOTUSD": "DOTUSD",
    "LINKUSD": "LINKUSD",
    
    # Commodities
    "XAUUSD": "XAUUSD",  # Gold
    "XAGUSD": "XAGUSD",  # Silver
    "XPTUSD": "XPTUSD",  # Platinum
    "XPDUSD": "XPDUSD",  # Palladium
    "USOIL": "USOIL",    # Oil
    "UKOIL": "UKOIL",    # Brent Oil
    "NATGAS": "NATGAS",  # Natural Gas
    
    # Stock Indices
    "SPX500": "SPX500",  # S&P 500
    "NAS100": "NAS100",  # NASDAQ 100
    "US30": "US30",      # Dow Jones
    "GER30": "GER30",    # DAX
    "UK100": "UK100",    # FTSE 100
    "FRA40": "FRA40",    # CAC 40
    "JPN225": "JPN225",  # Nikkei 225
    "AUS200": "AUS200",  # ASX 200
}

# Trading directions
DIRECTION_CALL = "call"
DIRECTION_PUT = "put"

# Account types
ACCOUNT_DEMO = 1
ACCOUNT_REAL = 0

# Time modes
TIME_MODE_TIMER = "TIMER"
TIME_MODE_TIME = "TIME"

# Expiry times (in minutes)
EXPIRY_TIMES = [1, 5, 15, 30, 60, 120, 300, 600]

# Minimum and maximum trade amounts
MIN_TRADE_AMOUNT = 1
MAX_TRADE_AMOUNT = 1000

# API endpoints
API_BASE_URL = "qxbroker.com"
WS_BASE_URL = "wss://ws2.qxbroker.com/socket.io/"

# Status codes
STATUS_SUCCESS = "SUCCESS"
STATUS_ERROR = "ERROR"
STATUS_PENDING = "PENDING"

# Trade results
TRADE_WIN = "win"
TRADE_LOSS = "loss"
TRADE_PENDING = "pending"

# Market status
MARKET_OPEN = True
MARKET_CLOSED = False

# Default settings
DEFAULT_ASSET = "EURUSD"
DEFAULT_PERIOD = 60
DEFAULT_AMOUNT = 10
DEFAULT_EXPIRY = 5

# Language codes
LANG_EN = "en"
LANG_PT = "pt"
LANG_ES = "es"
LANG_RU = "ru"
LANG_AR = "ar"

# User agent
DEFAULT_USER_AGENT = "Quotex/1.0"

# Timeframes (in seconds)
TIMEFRAMES = {
    "1M": 60,
    "5M": 300,
    "15M": 900,
    "30M": 1800,
    "1H": 3600,
    "2H": 7200,
    "4H": 14400,
    "1D": 86400
}

# Candle periods
CANDLE_PERIODS = [5, 10, 15, 30, 60, 120, 300, 600, 900, 1800, 3600, 7200, 14400, 86400]

# Error codes
ERROR_INVALID_CREDENTIALS = "INVALID_CREDENTIALS"
ERROR_CONNECTION_FAILED = "CONNECTION_FAILED"
ERROR_INSUFFICIENT_BALANCE = "INSUFFICIENT_BALANCE"
ERROR_INVALID_ASSET = "INVALID_ASSET"
ERROR_MARKET_CLOSED = "MARKET_CLOSED"
ERROR_INVALID_AMOUNT = "INVALID_AMOUNT"
ERROR_TRADE_FAILED = "TRADE_FAILED"

# Success messages
MSG_CONNECTION_SUCCESS = "Connected successfully"
MSG_TRADE_SUCCESS = "Trade placed successfully"
MSG_LOGOUT_SUCCESS = "Logged out successfully"

# WebSocket events
WS_EVENT_CONNECT = "connect"
WS_EVENT_DISCONNECT = "disconnect"
WS_EVENT_CANDLE = "candle"
WS_EVENT_TRADE = "trade"
WS_EVENT_BALANCE = "balance"
WS_EVENT_ASSET = "asset"

# Asset categories
ASSET_FOREX = "forex"
ASSET_CRYPTO = "crypto"
ASSET_COMMODITY = "commodity"
ASSET_INDEX = "index"
ASSET_STOCK = "stock"

# Risk levels
RISK_LOW = "LOW"
RISK_MEDIUM = "MEDIUM"
RISK_HIGH = "HIGH"

# Trading sessions
SESSION_ASIAN = "ASIAN"
SESSION_EUROPEAN = "EUROPEAN"
SESSION_AMERICAN = "AMERICAN"

# Volatility levels
VOLATILITY_LOW = "LOW"
VOLATILITY_MEDIUM = "MEDIUM"
VOLATILITY_HIGH = "HIGH"

# Signal strength
SIGNAL_WEAK = "WEAK"
SIGNAL_MODERATE = "MODERATE"
SIGNAL_STRONG = "STRONG"

# Trend directions
TREND_UP = "UP"
TREND_DOWN = "DOWN"
TREND_SIDEWAYS = "SIDEWAYS"

# Support/Resistance levels
LEVEL_SUPPORT = "SUPPORT"
LEVEL_RESISTANCE = "RESISTANCE"

# Chart patterns
PATTERN_TRIANGLE = "TRIANGLE"
PATTERN_WEDGE = "WEDGE"
PATTERN_FLAG = "FLAG"
PATTERN_PENNANT = "PENNANT"
PATTERN_HEAD_SHOULDERS = "HEAD_SHOULDERS"
PATTERN_DOUBLE_TOP = "DOUBLE_TOP"
PATTERN_DOUBLE_BOTTOM = "DOUBLE_BOTTOM"

# Indicator signals
SIGNAL_BUY = "BUY"
SIGNAL_SELL = "SELL"
SIGNAL_HOLD = "HOLD"

# Order types
ORDER_MARKET = "MARKET"
ORDER_LIMIT = "LIMIT"
ORDER_STOP = "STOP"
ORDER_STOP_LIMIT = "STOP_LIMIT"

# Position sizes
POSITION_MICRO = "MICRO"
POSITION_MINI = "MINI"
POSITION_STANDARD = "STANDARD"

# Trading strategies
STRATEGY_SCALPING = "SCALPING"
STRATEGY_DAY_TRADING = "DAY_TRADING"
STRATEGY_SWING_TRADING = "SWING_TRADING"
STRATEGY_POSITION_TRADING = "POSITION_TRADING"

# News impact levels
NEWS_LOW_IMPACT = "LOW"
NEWS_MEDIUM_IMPACT = "MEDIUM"
NEWS_HIGH_IMPACT = "HIGH"

# Economic calendar events
EVENT_NFP = "NFP"
EVENT_CPI = "CPI"
EVENT_GDP = "GDP"
EVENT_FOMC = "FOMC"
EVENT_ECB = "ECB"
EVENT_BOE = "BOE"
EVENT_BOJ = "BOJ"

# Market sentiment
SENTIMENT_BULLISH = "BULLISH"
SENTIMENT_BEARISH = "BEARISH"
SENTIMENT_NEUTRAL = "NEUTRAL"

# Volume indicators
VOLUME_LOW = "LOW"
VOLUME_AVERAGE = "AVERAGE"
VOLUME_HIGH = "HIGH"

# Price action patterns
PA_DOJI = "DOJI"
PA_HAMMER = "HAMMER"
PA_SHOOTING_STAR = "SHOOTING_STAR"
PA_ENGULFING = "ENGULFING"
PA_HARAMI = "HARAMI"

# Fibonacci levels
FIBO_0 = 0.0
FIBO_236 = 0.236
FIBO_382 = 0.382
FIBO_500 = 0.500
FIBO_618 = 0.618
FIBO_786 = 0.786
FIBO_1000 = 1.000

# Moving average types
MA_SMA = "SMA"
MA_EMA = "EMA"
MA_WMA = "WMA"
MA_SMMA = "SMMA"

# Oscillator levels
OSC_OVERSOLD = 30
OSC_OVERBOUGHT = 70
OSC_NEUTRAL = 50

# Bollinger Bands settings
BB_PERIOD = 20
BB_DEVIATION = 2

# RSI settings
RSI_PERIOD = 14
RSI_OVERSOLD = 30
RSI_OVERBOUGHT = 70

# MACD settings
MACD_FAST = 12
MACD_SLOW = 26
MACD_SIGNAL = 9

# Stochastic settings
STOCH_K = 14
STOCH_D = 3
STOCH_SLOWING = 3
