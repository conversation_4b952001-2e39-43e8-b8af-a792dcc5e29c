Metadata-Version: 2.1
Name: featuretools
Version: 1.31.0
Summary: a framework for automated feature engineering
Author-email: "Alteryx, Inc." <<EMAIL>>
Maintainer-email: "Alteryx, Inc." <<EMAIL>>
License: BSD 3-clause
Project-URL: Documentation, https://featuretools.alteryx.com
Project-URL: Source Code, https://github.com/alteryx/featuretools/
Project-URL: Changes, https://featuretools.alteryx.com/en/latest/release_notes.html
Project-URL: Issue Tracker, https://github.com/alteryx/featuretools/issues
Project-URL: Twitter, https://twitter.com/alteryxoss
Project-URL: Chat, https://join.slack.com/t/alteryx-oss/shared_invite/zt-182tyvuxv-NzIn6eiCEf8TBziuKp0bNA
Keywords: feature engineering,data science,machine learning
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Science/Research
Classifier: Intended Audience :: Developers
Classifier: Topic :: Software Development
Classifier: Topic :: Scientific/Engineering
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Operating System :: Microsoft :: Windows
Classifier: Operating System :: POSIX
Classifier: Operating System :: Unix
Classifier: Operating System :: MacOS
Requires-Python: <4,>=3.9
Description-Content-Type: text/markdown
License-File: LICENSE
License-File: featuretools/primitives/data/free_email_provider_domains_license
Requires-Dist: cloudpickle >=1.5.0
Requires-Dist: holidays >=0.17
Requires-Dist: numpy >=1.25.0
Requires-Dist: packaging >=20.0
Requires-Dist: pandas >=2.0.0
Requires-Dist: psutil >=5.7.0
Requires-Dist: scipy >=1.10.0
Requires-Dist: tqdm >=4.66.3
Requires-Dist: woodwork >=0.28.0
Provides-Extra: autonormalize
Requires-Dist: autonormalize >=2.0.1 ; extra == 'autonormalize'
Provides-Extra: complete
Requires-Dist: featuretools[dask,nlp,premium] ; extra == 'complete'
Provides-Extra: dask
Requires-Dist: dask[dataframe] >=2023.2.0 ; extra == 'dask'
Requires-Dist: distributed >=2023.2.0 ; extra == 'dask'
Provides-Extra: dev
Requires-Dist: ruff >=0.1.6 ; extra == 'dev'
Requires-Dist: black[jupyter] >=23.1.0 ; extra == 'dev'
Requires-Dist: pre-commit >=2.20.0 ; extra == 'dev'
Requires-Dist: featuretools[dask,docs,test] ; extra == 'dev'
Provides-Extra: docs
Requires-Dist: ipython ==8.4.0 ; extra == 'docs'
Requires-Dist: jupyter ==1.0.0 ; extra == 'docs'
Requires-Dist: jupyter-client >=8.0.2 ; extra == 'docs'
Requires-Dist: matplotlib ==3.7.2 ; extra == 'docs'
Requires-Dist: Sphinx ==5.1.1 ; extra == 'docs'
Requires-Dist: nbsphinx ==0.8.9 ; extra == 'docs'
Requires-Dist: nbconvert ==6.5.0 ; extra == 'docs'
Requires-Dist: pydata-sphinx-theme ==0.9.0 ; extra == 'docs'
Requires-Dist: sphinx-inline-tabs ==2022.1.2b11 ; extra == 'docs'
Requires-Dist: sphinx-copybutton ==0.5.0 ; extra == 'docs'
Requires-Dist: myst-parser ==0.18.0 ; extra == 'docs'
Requires-Dist: autonormalize >=2.0.1 ; extra == 'docs'
Requires-Dist: click >=7.0.0 ; extra == 'docs'
Requires-Dist: featuretools[dask,test] ; extra == 'docs'
Provides-Extra: nlp
Requires-Dist: nlp-primitives >=2.12.0 ; extra == 'nlp'
Provides-Extra: premium
Requires-Dist: premium-primitives >=0.0.3 ; extra == 'premium'
Provides-Extra: sklearn
Requires-Dist: featuretools-sklearn-transformer >=1.0.0 ; extra == 'sklearn'
Provides-Extra: sql
Requires-Dist: featuretools-sql >=0.0.1 ; extra == 'sql'
Requires-Dist: psycopg2-binary >=2.9.3 ; extra == 'sql'
Provides-Extra: test
Requires-Dist: boto3 >=1.34.32 ; extra == 'test'
Requires-Dist: composeml >=0.8.0 ; extra == 'test'
Requires-Dist: graphviz >=0.8.4 ; extra == 'test'
Requires-Dist: moto[all] >=5.0.0 ; extra == 'test'
Requires-Dist: pip >=23.3.0 ; extra == 'test'
Requires-Dist: pyarrow >=14.0.1 ; extra == 'test'
Requires-Dist: pympler >=0.8 ; extra == 'test'
Requires-Dist: pytest >=7.1.2 ; extra == 'test'
Requires-Dist: pytest-cov >=3.0.0 ; extra == 'test'
Requires-Dist: pytest-xdist >=2.5.0 ; extra == 'test'
Requires-Dist: smart-open >=5.0.0 ; extra == 'test'
Requires-Dist: urllib3 >=1.26.18 ; extra == 'test'
Requires-Dist: pytest-timeout >=2.1.0 ; extra == 'test'
Provides-Extra: tsfresh
Requires-Dist: featuretools-tsfresh-primitives >=1.0.0 ; extra == 'tsfresh'

<p align="center">
<img width=50% src="https://www.featuretools.com/wp-content/uploads/2017/12/FeatureLabs-Logo-Tangerine-800.png" alt="Featuretools" />
</p>
<p align="center">
<i>"One of the holy grails of machine learning is to automate more and more of the feature engineering process."</i> ― Pedro Domingos, <a href="https://bit.ly/things_to_know_ml">A Few Useful Things to Know about Machine Learning</a>
</p>

<p align="center">
    <a href="https://github.com/alteryx/featuretools/actions/workflows/tests_with_latest_deps.yaml" alt="Tests" target="_blank">
        <img src="https://github.com/alteryx/featuretools/actions/workflows/tests_with_latest_deps.yaml/badge.svg?branch=main" alt="Tests" />
    </a>
    <a href="https://codecov.io/gh/alteryx/featuretools">
        <img src="https://codecov.io/gh/alteryx/featuretools/branch/main/graph/badge.svg"/>
    </a>
    <a href='https://featuretools.alteryx.com/en/stable/?badge=stable'>
        <img src='https://readthedocs.com/projects/feature-labs-inc-featuretools/badge/?version=stable' alt='Documentation Status' />
    </a>
    <a href="https://badge.fury.io/py/featuretools" target="_blank">
        <img src="https://badge.fury.io/py/featuretools.svg?maxAge=2592000" alt="PyPI Version" />
    </a>
    <a href="https://anaconda.org/conda-forge/featuretools" target="_blank">
        <img src="https://anaconda.org/conda-forge/featuretools/badges/version.svg" alt="Anaconda Version" />
    </a>
    <a href="https://stackoverflow.com/questions/tagged/featuretools" target="_blank">
        <img src="http://img.shields.io/badge/questions-on_stackoverflow-blue.svg" alt="StackOverflow" />
    </a>
    <a href="https://pepy.tech/project/featuretools" target="_blank">
        <img src="https://static.pepy.tech/badge/featuretools/month" alt="PyPI Downloads" />
    </a>
</p>
<hr>

[Featuretools](https://www.featuretools.com) is a python library for automated feature engineering. See the [documentation](https://docs.featuretools.com) for more information.

## Installation
Install with pip

```
python -m pip install featuretools
```

or from the Conda-forge channel on [conda](https://anaconda.org/conda-forge/featuretools):

```
conda install -c conda-forge featuretools
```

### Add-ons

You can install add-ons individually or all at once by running:

```
python -m pip install "featuretools[complete]"
```

**Premium Primitives** - Use Premium Primitives from the premium-primitives repo

```
python -m pip install "featuretools[premium]"
```

**NLP Primitives** - Use Natural Language Primitives from the nlp-primitives repo

```
python -m pip install "featuretools[nlp]"
```

**Dask Support** - Use Dask to run DFS with njobs > 1

```
python -m pip install "featuretools[dask]"
```

## Example
Below is an example of using Deep Feature Synthesis (DFS) to perform automated feature engineering. In this example, we apply DFS to a multi-table dataset consisting of timestamped customer transactions.

```python
>> import featuretools as ft
>> es = ft.demo.load_mock_customer(return_entityset=True)
>> es.plot()
```

<img src="https://github.com/alteryx/featuretools/blob/main/docs/source/_static/images/entity_set.png?raw=true" width="350">

Featuretools can automatically create a single table of features for any "target dataframe"
```python
>> feature_matrix, features_defs = ft.dfs(entityset=es, target_dataframe_name="customers")
>> feature_matrix.head(5)
```

```
            zip_code  COUNT(transactions)  COUNT(sessions)  SUM(transactions.amount) MODE(sessions.device)  MIN(transactions.amount)  MAX(transactions.amount)  YEAR(join_date)  SKEW(transactions.amount)  DAY(join_date)                   ...                     SUM(sessions.MIN(transactions.amount))  MAX(sessions.SKEW(transactions.amount))  MAX(sessions.MIN(transactions.amount))  SUM(sessions.MEAN(transactions.amount))  STD(sessions.SUM(transactions.amount))  STD(sessions.MEAN(transactions.amount))  SKEW(sessions.MEAN(transactions.amount))  STD(sessions.MAX(transactions.amount))  NUM_UNIQUE(sessions.DAY(session_start))  MIN(sessions.SKEW(transactions.amount))
customer_id                                                                                                                                                                                                                                  ...
1              60091                  131               10                  10236.77               desktop                      5.60                    149.95             2008                   0.070041               1                   ...                                                     169.77                                 0.610052                                   41.95                               791.976505                              175.939423                                 9.299023                                 -0.377150                                5.857976                                        1                                -0.395358
2              02139                  122                8                   9118.81                mobile                      5.81                    149.15             2008                   0.028647              20                   ...                                                     114.85                                 0.492531                                   42.96                               596.243506                              230.333502                                10.925037                                  0.962350                                7.420480                                        1                                -0.470007
3              02139                   78                5                   5758.24               desktop                      6.78                    147.73             2008                   0.070814              10                   ...                                                      64.98                                 0.645728                                   21.77                               369.770121                              471.048551                                 9.819148                                 -0.244976                               12.537259                                        1                                -0.630425
4              60091                  111                8                   8205.28               desktop                      5.73                    149.56             2008                   0.087986              30                   ...                                                      83.53                                 0.516262                                   17.27                               584.673126                              322.883448                                13.065436                                 -0.548969                               12.738488                                        1                                -0.497169
5              02139                   58                4                   4571.37                tablet                      5.91                    148.17             2008                   0.085883              19                   ...                                                      73.09                                 0.830112                                   27.46                               313.448942                              198.522508                                 8.950528                                  0.098885                                5.599228                                        1                                -0.396571

[5 rows x 69 columns]
```
We now have a feature vector for each customer that can be used for machine learning. See the [documentation on Deep Feature Synthesis](https://featuretools.alteryx.com/en/stable/getting_started/afe.html) for more examples.

Featuretools contains many different types of built-in primitives for creating features. If the primitive you need is not included, Featuretools also allows you to [define your own custom primitives](https://featuretools.alteryx.com/en/stable/getting_started/primitives.html#defining-custom-primitives).

## Demos
**Predict Next Purchase**

[Repository](https://github.com/alteryx/open_source_demos/blob/main/predict-next-purchase/) | [Notebook](https://github.com/alteryx/open_source_demos/blob/main/predict-next-purchase/Tutorial.ipynb)

In this demonstration, we use a multi-table dataset of 3 million online grocery orders from Instacart to predict what a customer will buy next. We show how to generate features with automated feature engineering and build an accurate machine learning pipeline using Featuretools, which can be reused for multiple prediction problems. For more advanced users, we show how to scale that pipeline to a large dataset using Dask.

For more examples of how to use Featuretools, check out our [demos](https://www.featuretools.com/demos) page.

## Testing & Development

The Featuretools community welcomes pull requests. Instructions for testing and development are available [here.](https://featuretools.alteryx.com/en/stable/install.html#development)

## Support
The Featuretools community is happy to provide support to users of Featuretools. Project support can be found in four places depending on the type of question:

1. For usage questions, use [Stack Overflow](https://stackoverflow.com/questions/tagged/featuretools) with the `featuretools` tag.
2. For bugs, issues, or feature requests start a [Github issue](https://github.com/alteryx/featuretools/issues).
3. For discussion regarding development on the core library, use [Slack](https://join.slack.com/t/alteryx-oss/shared_invite/zt-182tyvuxv-NzIn6eiCEf8TBziuKp0bNA).
4. For everything else, the core developers can be reached by <NAME_EMAIL>

## Citing Featuretools

If you use Featuretools, please consider citing the following paper:

James Max Kanter, Kalyan Veeramachaneni. [Deep feature synthesis: Towards automating data science endeavors.](https://dai.lids.mit.edu/wp-content/uploads/2017/10/DSAA_DSM_2015.pdf) *IEEE DSAA 2015*.

BibTeX entry:

```bibtex
@inproceedings{kanter2015deep,
  author    = {James Max Kanter and Kalyan Veeramachaneni},
  title     = {Deep feature synthesis: Towards automating data science endeavors},
  booktitle = {2015 {IEEE} International Conference on Data Science and Advanced Analytics, DSAA 2015, Paris, France, October 19-21, 2015},
  pages     = {1--10},
  year      = {2015},
  organization={IEEE}
}
```

## Built at Alteryx

**Featuretools** is an open source project maintained by [Alteryx](https://www.alteryx.com). To see the other open source projects we’re working on visit [Alteryx Open Source](https://www.alteryx.com/open-source). If building impactful data science pipelines is important to you or your business, please get in touch.

<p align="center">
  <a href="https://www.alteryx.com/open-source">
    <img src="https://alteryx-oss-web-images.s3.amazonaws.com/OpenSource_Logo-01.png" alt="Alteryx Open Source" width="800"/>
  </a>
</p>
