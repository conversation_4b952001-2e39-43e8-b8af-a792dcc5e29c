"""
Auto Trader - التداول الآلي
نظام التداول الآلي المتقدم للخيارات الثنائية
"""

import asyncio
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging

from .trading_connector import TradingConnector
from ..strategy.decision_maker import StrategyDecisionMaker
from ..risk_management.risk_manager import RiskManager

logger = logging.getLogger(__name__)

class AutoTrader:
    """نظام التداول الآلي المتقدم"""
    
    def __init__(self, client=None):
        """تهيئة نظام التداول الآلي"""
        if client:
            # استخدام العميل الموجود
            self.client = client
            self.connector = None
        else:
            # إنشاء موصل جديد
            self.connector = TradingConnector()
            self.client = None

        self.strategy = StrategyDecisionMaker()
        self.risk_manager = RiskManager()

        # إعدادات التداول
        self.is_trading = False
        self.current_session = None
        self.active_trade = None
        self.session_config = {}

        # إحصائيات الجلسة
        self.session_stats = {
            'trades_executed': 0,
            'wins': 0,
            'losses': 0,
            'total_profit': 0,
            'start_time': None,
            'end_time': None
        }

        logger.info("🤖 Auto Trader initialized")
    
    async def connect_to_platform(self, email: str, password: str) -> Dict[str, Any]:
        """الاتصال بمنصة التداول"""
        try:
            logger.info("🔗 Connecting to trading platform...")
            
            connection_result = await self.connector.connect(email, password)
            
            if connection_result['status'] == 'SUCCESS':
                logger.info("✅ Connected to trading platform successfully")
                return connection_result
            else:
                logger.error(f"❌ Failed to connect: {connection_result.get('error', 'Unknown error')}")
                return connection_result
                
        except Exception as e:
            logger.error(f"Error connecting to platform: {e}")
            return {'status': 'ERROR', 'error': str(e)}
    
    async def setup_trading_session(self, account_type: str, pair: str, 
                                  trade_amount: float, max_trades: int) -> Dict[str, Any]:
        """إعداد جلسة التداول"""
        try:
            logger.info(f"⚙️ Setting up trading session...")
            
            # التبديل لنوع الحساب المطلوب
            account_result = await self.connector.switch_account_type(account_type)
            if account_result['status'] != 'SUCCESS':
                return account_result
            
            # الحصول على معلومات الحساب
            account_info = await self.connector.get_account_info()
            if account_info['status'] != 'SUCCESS':
                return account_info
            
            current_balance = account_info['balance']
            
            # بدء جلسة إدارة المخاطر
            risk_session = self.risk_manager.start_session(current_balance, account_type)
            if risk_session['status'] != 'SUCCESS':
                return risk_session
            
            # حفظ إعدادات الجلسة
            self.session_config = {
                'account_type': account_type,
                'pair': pair,
                'trade_amount': trade_amount,
                'max_trades': max_trades,
                'initial_balance': current_balance,
                'session_id': risk_session['session_id'],
                'start_time': datetime.now()
            }
            
            # إعادة تعيين الإحصائيات
            self.session_stats = {
                'trades_executed': 0,
                'wins': 0,
                'losses': 0,
                'total_profit': 0,
                'start_time': datetime.now(),
                'end_time': None
            }
            
            logger.info(f"✅ Trading session setup complete")
            logger.info(f"   Account: {account_type}")
            logger.info(f"   Balance: ${current_balance}")
            logger.info(f"   Pair: {pair}")
            logger.info(f"   Trade Amount: ${trade_amount}")
            logger.info(f"   Max Trades: {max_trades}")
            
            return {
                'status': 'SUCCESS',
                'session_config': self.session_config,
                'account_info': account_info,
                'risk_limits': risk_session['risk_limits']
            }
            
        except Exception as e:
            logger.error(f"Error setting up trading session: {e}")
            return {'status': 'ERROR', 'error': str(e)}
    
    async def start_auto_trading(self) -> Dict[str, Any]:
        """بدء التداول الآلي"""
        try:
            if not self.session_config:
                return {'status': 'ERROR', 'error': 'No trading session configured'}
            
            if not self.connector.is_connected:
                return {'status': 'ERROR', 'error': 'Not connected to trading platform'}
            
            logger.info("🚀 Starting automated trading...")
            self.is_trading = True
            
            # حلقة التداول الرئيسية
            while self.is_trading and self.session_stats['trades_executed'] < self.session_config['max_trades']:
                try:
                    # انتظار إشارة تداول
                    signal = await self._wait_for_trading_signal()
                    
                    if signal and signal.get('decision') == 'TRADE':
                        # تنفيذ الصفقة
                        trade_result = await self._execute_trade(signal)
                        
                        if trade_result['status'] == 'SUCCESS':
                            # انتظار انتهاء الصفقة
                            await self._monitor_trade(trade_result)
                        else:
                            logger.error(f"Failed to execute trade: {trade_result.get('error')}")
                    
                    # فحص حالة إدارة المخاطر
                    if not self._check_risk_conditions():
                        logger.warning("🛑 Risk conditions not met, stopping trading")
                        break
                    
                    # انتظار قصير قبل البحث عن إشارة جديدة
                    await asyncio.sleep(30)
                    
                except Exception as e:
                    logger.error(f"Error in trading loop: {e}")
                    await asyncio.sleep(60)  # انتظار أطول في حالة الخطأ
            
            # إنهاء الجلسة
            await self._end_trading_session()
            
            return {
                'status': 'SUCCESS',
                'session_completed': True,
                'final_stats': self.session_stats
            }
            
        except Exception as e:
            logger.error(f"Error in auto trading: {e}")
            self.is_trading = False
            return {'status': 'ERROR', 'error': str(e)}
    
    async def _wait_for_trading_signal(self) -> Optional[Dict[str, Any]]:
        """انتظار إشارة تداول قوية"""
        try:
            logger.info("🔍 Analyzing market for trading signals...")
            
            # تحميل البيانات الحالية للزوج
            pair = self.session_config['pair']
            
            # هنا نحتاج لتحميل البيانات من النظام الأساسي
            # سنستخدم البيانات المحفوظة مؤقتاً
            candles_data = await self._load_current_candles(pair)
            
            if not candles_data:
                logger.warning(f"No data available for {pair}")
                return None
            
            # تشغيل الاستراتيجية
            analysis_result = self.strategy.run_strategy_analysis(
                pair_name=pair,
                candles=candles_data
            )
            
            trading_decision = analysis_result.get('trading_decision', {})
            
            if trading_decision.get('decision') == 'TRADE':
                confidence = trading_decision.get('confidence', 0)
                logger.info(f"🎯 Trading signal detected! Confidence: {confidence:.1f}%")
                return trading_decision
            else:
                logger.debug(f"No trading signal. Reason: {trading_decision.get('reasoning', 'Unknown')}")
                return None
                
        except Exception as e:
            logger.error(f"Error waiting for trading signal: {e}")
            return None
    
    async def _load_current_candles(self, pair: str) -> Optional[List[Dict[str, Any]]]:
        """تحميل البيانات الحالية للزوج"""
        try:
            # تحميل من الملفات المحفوظة
            historical_file = f"data/historical/{pair}.json"
            
            if os.path.exists(historical_file):
                with open(historical_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data.get('candles', [])
            
            return None
            
        except Exception as e:
            logger.error(f"Error loading candles for {pair}: {e}")
            return None
    
    async def _execute_trade(self, signal: Dict[str, Any]) -> Dict[str, Any]:
        """تنفيذ الصفقة"""
        try:
            pair = self.session_config['pair']
            trade_amount = self.session_config['trade_amount']
            direction = signal.get('direction', 'CALL')
            expiry_minutes = signal.get('expiry_minutes', 5)
            confidence = signal.get('confidence', 0)
            
            # الحصول على الرصيد الحالي
            account_info = await self.connector.get_account_info()
            current_balance = account_info.get('balance', 0)
            
            # التحقق من صحة الصفقة مع إدارة المخاطر
            validation = self.risk_manager.validate_trade(
                trade_amount=trade_amount,
                confidence=confidence,
                agreement_layers=signal.get('layer_agreement', {}).get('agreeing_layers', 0),
                current_balance=current_balance
            )
            
            if not validation['allowed']:
                logger.warning(f"🚫 Trade not allowed: {', '.join(validation['reasons'])}")
                return {'status': 'ERROR', 'error': 'Trade validation failed', 'reasons': validation['reasons']}
            
            # تنفيذ الصفقة
            logger.info(f"📈 Executing trade: {pair} {direction} ${trade_amount} {expiry_minutes}min")

            if self.client:
                # استخدام العميل مباشرة (نفس طريقة التداول اليدوي)
                trade_result = await self._execute_trade_direct(pair, trade_amount, direction, expiry_minutes * 60)
            else:
                # استخدام الموصل
                trade_result = await self.connector.place_trade(
                    pair=pair,
                    direction=direction,
                    amount=trade_amount,
                    duration=expiry_minutes
                )
            
            if trade_result['status'] == 'SUCCESS':
                self.active_trade = {
                    'trade_id': trade_result['trade_id'],
                    'pair': pair,
                    'direction': direction,
                    'amount': trade_amount,
                    'expiry_minutes': expiry_minutes,
                    'start_time': datetime.now(),
                    'signal': signal
                }
                
                logger.info(f"✅ Trade executed successfully - ID: {trade_result['trade_id']}")
                
            return trade_result
            
        except Exception as e:
            logger.error(f"Error executing trade: {e}")
            return {'status': 'ERROR', 'error': str(e)}

    async def _execute_trade_direct(self, asset: str, amount: float, direction: str, duration: int = 60) -> Dict[str, Any]:
        """تنفيذ صفقة مباشرة باستخدام العميل (نفس طريقة التداول اليدوي)"""
        try:
            if not self.client:
                return {'status': 'ERROR', 'error': 'No client available'}

            # التحقق من أن الزوج متاح
            asset_name, asset_data = await self.client.get_available_asset(asset, force_open=True)

            if not asset_data or len(asset_data) < 3 or not asset_data[2]:
                return {'status': 'ERROR', 'error': f'Asset {asset} is not available'}

            # تنفيذ الصفقة (تقريب المبلغ لرقمين عشريين)
            rounded_amount = round(amount, 2)
            status, buy_info = await self.client.buy(
                rounded_amount, asset_name, direction, duration, time_mode="TIMER"
            )

            if status:
                trade_id = buy_info.get('id') if isinstance(buy_info, dict) else None
                if trade_id:
                    return {
                        'status': 'SUCCESS',
                        'trade_id': trade_id,
                        'amount': rounded_amount,
                        'asset': asset,
                        'direction': direction,
                        'duration': duration
                    }
                else:
                    return {'status': 'ERROR', 'error': 'Trade executed but no trade ID received'}
            else:
                error_msg = str(buy_info) if buy_info else 'Unknown error'
                return {'status': 'ERROR', 'error': error_msg}

        except Exception as e:
            logger.error(f"Error executing trade directly: {e}")
            return {'status': 'ERROR', 'error': str(e)}
    
    async def _monitor_trade(self, trade_result: Dict[str, Any]):
        """مراقبة الصفقة حتى انتهائها"""
        try:
            if not self.active_trade:
                return
            
            trade_id = self.active_trade['trade_id']
            expiry_minutes = self.active_trade['expiry_minutes']
            
            logger.info(f"⏳ Monitoring trade {trade_id} for {expiry_minutes} minutes...")
            
            # انتظار انتهاء الصفقة + وقت إضافي للتأكد
            wait_time = (expiry_minutes * 60) + 30  # إضافة 30 ثانية
            await asyncio.sleep(wait_time)
            
            # فحص نتيجة الصفقة باستخدام نفس الطريقة المستخدمة في التداول اليدوي
            try:
                logger.info(f"🔍 Checking trade result for ID: {trade_id}")

                if self.client:
                    # استخدام العميل مباشرة
                    win_result = await self.client.check_win(trade_id)
                    profit = self.client.get_profit()
                else:
                    # استخدام الموصل
                    win_result = await self.connector.client.check_win(trade_id)
                    profit = self.connector.client.get_profit()

                if win_result:
                    trade_outcome = 'WIN'
                    logger.info(f"✅ Trade {trade_id} WON - Profit: ${profit:.2f}")
                    await self._record_trade_result(trade_outcome, profit)
                else:
                    trade_outcome = 'LOSS'
                    logger.info(f"❌ Trade {trade_id} LOST - Loss: ${abs(profit):.2f}")
                    await self._record_trade_result(trade_outcome, profit)

                logger.info(f"📊 Trade {trade_id} completed: {trade_outcome}, Profit: ${profit}")

            except Exception as check_error:
                logger.warning(f"Could not check trade result: {check_error}")
                # تسجيل كخسارة في حالة عدم معرفة النتيجة
                await self._record_trade_result('LOSS', 0)
            
            self.active_trade = None
            
        except Exception as e:
            logger.error(f"Error monitoring trade: {e}")
            if self.active_trade:
                await self._record_trade_result('LOSS', 0)
                self.active_trade = None
    
    async def _record_trade_result(self, result: str, profit: float):
        """تسجيل نتيجة الصفقة"""
        try:
            trade_amount = self.active_trade['amount'] if self.active_trade else 0
            
            # تسجيل في إدارة المخاطر
            risk_result = self.risk_manager.record_trade_result(trade_amount, result, profit)
            
            # تحديث إحصائيات الجلسة
            self.session_stats['trades_executed'] += 1
            
            if result.upper() == 'WIN':
                self.session_stats['wins'] += 1
                self.session_stats['total_profit'] += profit
            else:
                self.session_stats['losses'] += 1
                self.session_stats['total_profit'] -= trade_amount
            
            # حفظ تفاصيل الصفقة
            await self._save_trade_record(result, profit)
            
            logger.info(f"📝 Trade result recorded: {result}")
            
        except Exception as e:
            logger.error(f"Error recording trade result: {e}")
    
    async def _save_trade_record(self, result: str, profit: float):
        """حفظ سجل الصفقة"""
        try:
            if not self.active_trade:
                return
            
            trade_record = {
                'trade_id': self.active_trade['trade_id'],
                'pair': self.active_trade['pair'],
                'direction': self.active_trade['direction'],
                'amount': self.active_trade['amount'],
                'expiry_minutes': self.active_trade['expiry_minutes'],
                'start_time': self.active_trade['start_time'].isoformat(),
                'end_time': datetime.now().isoformat(),
                'result': result,
                'profit': profit,
                'signal_details': self.active_trade['signal'],
                'session_id': self.session_config['session_id']
            }
            
            # حفظ في ملف الجلسة
            session_file = f"data/sessions/trades_{self.session_config['session_id']}.json"
            os.makedirs("data/sessions", exist_ok=True)
            
            trades_list = []
            if os.path.exists(session_file):
                with open(session_file, 'r', encoding='utf-8') as f:
                    trades_list = json.load(f)
            
            trades_list.append(trade_record)
            
            with open(session_file, 'w', encoding='utf-8') as f:
                json.dump(trades_list, f, indent=2, ensure_ascii=False)
            
        except Exception as e:
            logger.error(f"Error saving trade record: {e}")
    
    def _check_risk_conditions(self) -> bool:
        """فحص شروط إدارة المخاطر"""
        try:
            # فحص الحد الأقصى للصفقات
            if self.session_stats['trades_executed'] >= self.session_config['max_trades']:
                return False
            
            # فحص إدارة المخاطر
            # يمكن إضافة المزيد من الفحوصات هنا
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking risk conditions: {e}")
            return False
    
    async def _end_trading_session(self):
        """إنهاء جلسة التداول"""
        try:
            self.is_trading = False
            self.session_stats['end_time'] = datetime.now()
            
            # حفظ جلسة إدارة المخاطر
            session_file = self.risk_manager.save_session()
            
            # حساب الإحصائيات النهائية
            total_trades = self.session_stats['trades_executed']
            win_rate = (self.session_stats['wins'] / total_trades * 100) if total_trades > 0 else 0
            
            logger.info("🏁 Trading session ended")
            logger.info(f"   Total Trades: {total_trades}")
            logger.info(f"   Wins: {self.session_stats['wins']}")
            logger.info(f"   Losses: {self.session_stats['losses']}")
            logger.info(f"   Win Rate: {win_rate:.1f}%")
            logger.info(f"   Total Profit: ${self.session_stats['total_profit']:.2f}")
            
        except Exception as e:
            logger.error(f"Error ending trading session: {e}")
    
    async def stop_trading(self):
        """إيقاف التداول"""
        logger.info("🛑 Stopping automated trading...")
        self.is_trading = False
    
    def get_session_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الجلسة"""
        total_trades = self.session_stats['trades_executed']
        win_rate = (self.session_stats['wins'] / total_trades * 100) if total_trades > 0 else 0
        
        return {
            'session_config': self.session_config,
            'stats': self.session_stats,
            'win_rate': win_rate,
            'is_trading': self.is_trading,
            'active_trade': self.active_trade is not None
        }
