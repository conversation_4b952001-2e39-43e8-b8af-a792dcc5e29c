"""
Contains values used to approximate the critical value and
p-value from statistics that follow the <PERSON><PERSON><PERSON> distribution.

Most values are from <PERSON><PERSON><PERSON><PERSON> (1994) or (2010).  A small number of these
did not appear in the original paper and have been computed using an identical
simulation.
"""

from numpy import array, asarray, inf

from arch.typing import Float64Array

small_scaling = asarray([1, 1, 1e-2])
tau_small_p = {}
tau_large_p = {}

tau_small_p["n"] = [
    [0.6344, 1.2378, 3.2496],
    [1.9129, 1.3857, 3.5322],
    [2.7648, 1.4502, 3.4186],
    [3.4336, 1.4835, 3.1900],
    [4.0999, 1.5533, 3.5900],
    [4.5388, 1.5344, 2.9807],
]
tau_small_p["n"] = asarray(tau_small_p["n"]) * small_scaling

tau_small_p["c"] = [
    [2.1659, 1.4412, 3.8269],
    [2.9200, 1.5012, 3.9796],
    [3.4699, 1.4856, 3.1640],
    [3.9673, 1.4777, 2.6315],
    [4.5509, 1.5338, 2.9545],
    [5.1399, 1.6036, 3.4445],
]
tau_small_p["c"] = asarray(tau_small_p["c"]) * small_scaling

tau_small_p["ct"] = [
    [3.2512, 1.6047, 4.9588],
    [3.6646, 1.5419, 3.6448],
    [4.0983, 1.5173, 2.9898],
    [4.5844, 1.5338, 2.8796],
    [5.0722, 1.5634, 2.9472],
    [5.5300, 1.5914, 3.0392],
]
tau_small_p["ct"] = asarray(tau_small_p["ct"]) * small_scaling

tau_small_p["ctt"] = [
    [4.0003, 1.6580, 4.8288],
    [4.3534, 1.6016, 3.7947],
    [4.7343, 1.5768, 3.2396],
    [5.2140, 1.6077, 3.3449],
    [5.6481, 1.6274, 3.3455],
    [5.9296, 1.5929, 2.8223],
]
tau_small_p["ctt"] = asarray(tau_small_p["ctt"]) * small_scaling

large_scaling = asarray([1, 1e-1, 1e-1, 1e-2])
tau_large_p["n"] = [
    [0.4797, 9.3557, -0.6999, 3.3066],
    [1.5578, 8.5580, -2.0830, -3.3549],
    [2.2268, 6.8093, -3.2362, -5.4448],
    [2.7654, 6.4502, -3.0811, -4.4946],
    [3.2684, 6.8051, -2.6778, -3.4972],
    [3.7268, 7.1670, -2.3648, -2.8288],
]
tau_large_p["n"] = asarray(tau_large_p["n"]) * large_scaling

tau_large_p["c"] = [
    [1.7339, 9.3202, -1.2745, -1.0368],
    [2.1945, 6.4695, -2.9198, -4.2377],
    [2.5893, 4.5168, -3.6529, -5.0074],
    [3.0387, 4.5452, -3.3666, -4.1921],
    [3.5049, 5.2098, -2.9158, -3.3468],
    [3.9489, 5.8933, -2.5359, -2.721],
]
tau_large_p["c"] = asarray(tau_large_p["c"]) * large_scaling

tau_large_p["ct"] = [
    [2.5261, 6.1654, -3.7956, -6.0285],
    [2.8500, 5.2720, -3.6622, -5.1695],
    [3.2210, 5.2550, -3.2685, -4.1501],
    [3.6520, 5.9758, -2.7483, -3.2081],
    [4.0712, 6.6428, -2.3464, -2.5460],
    [4.4735, 7.1757, -2.0681, -2.1196],
]
tau_large_p["ct"] = asarray(tau_large_p["ct"]) * large_scaling

tau_large_p["ctt"] = [
    [3.0778, 4.9529, -4.1477, -5.9359],
    [3.4713, 5.9670, -3.2507, -4.2286],
    [3.8637, 6.7852, -2.6286, -3.1381],
    [4.2736, 7.6199, -2.1534, -2.4026],
    [4.6679, 8.2618, -1.8220, -1.9147],
    [5.0009, 8.3735, -1.6994, -1.6928],
]
tau_large_p["ctt"] = asarray(tau_large_p["ctt"]) * large_scaling

# These are the new estimates from MacKinnon 2010
# the first axis is N -1
# the second axis is 1 %, 5 %, 10 %
# the last axis is the coefficients

# noinspection PyDictCreation
tau_2010: dict[str, Float64Array] = {}

tau_2010["n"] = array(
    [
        [
            [-2.56574, -2.2358, -3.627, 0],  # N = 1
            [-1.94100, -0.2686, -3.365, 31.223],
            [-1.61682, 0.2656, -2.714, 25.364],
        ]
    ]
)

tau_2010["c"] = array(
    [
        [
            [-3.43035, -6.5393, -16.786, -79.433],  # N = 1, 1%
            [-2.86154, -2.8903, -4.234, -40.040],  # 5 %
            [-2.56677, -1.5384, -2.809, 0],
        ],  # 10 %
        [
            [-3.89644, -10.9519, -33.527, 0],  # N = 2
            [-3.33613, -6.1101, -6.823, 0],
            [-3.04445, -4.2412, -2.720, 0],
        ],
        [
            [-4.29374, -14.4354, -33.195, 47.433],  # N = 3
            [-3.74066, -8.5632, -10.852, 27.982],
            [-3.45218, -6.2143, -3.718, 0],
        ],
        [
            [-4.64332, -18.1031, -37.972, 0],  # N = 4
            [-4.09600, -11.2349, -11.175, 0],
            [-3.81020, -8.3931, -4.137, 0],
        ],
        [
            [-4.95756, -21.8883, -45.142, 0],  # N = 5
            [-4.41519, -14.0405, -12.575, 0],
            [-4.13157, -10.7417, -3.784, 0],
        ],
        [
            [-5.24568, -25.6688, -57.737, 88.639],  # N = 6
            [-4.70693, -16.9178, -17.492, 60.007],
            [-4.42501, -13.1875, -5.104, 27.877],
        ],
        [
            [-5.51233, -29.5760, -69.398, 164.295],  # N = 7
            [-4.97684, -19.9021, -22.045, 110.761],
            [-4.69648, -15.7315, -5.104, 27.877],
        ],
        [
            [-5.76202, -33.5258, -82.189, 256.289],  # N = 8
            [-5.22924, -23.0023, -24.646, 144.479],
            [-4.95007, -18.3959, -7.344, 94.872],
        ],
        [
            [-5.99742, -37.6572, -87.365, 248.316],  # N = 9
            [-5.46697, -26.2057, -26.627, 176.382],
            [-5.18897, -21.1377, -9.484, 172.704],
        ],
        [
            [-6.22103, -41.7154, -102.680, 389.33],  # N = 10
            [-5.69244, -29.4521, -30.994, 251.016],
            [-5.41533, -24.0006, -7.514, 163.049],
        ],
        [
            [-6.43377, -46.0084, -106.809, 352.752],  # N = 11
            [-5.90714, -32.8336, -30.275, 249.994],
            [-5.63086, -26.9693, -4.083, 151.427],
        ],
        [
            [-6.63790, -50.2095, -124.156, 579.622],  # N = 12
            [-6.11279, -36.2681, -32.505, 314.802],
            [-5.83724, -29.9864, -2.686, 184.116],
        ],
    ]
)

tau_2010["ct"] = array(
    [
        [
            [-3.95877, -9.0531, -28.428, -134.155],  # N = 1
            [-3.41049, -4.3904, -9.036, -45.374],
            [-3.12705, -2.5856, -3.925, -22.380],
        ],
        [
            [-4.32762, -15.4387, -35.679, 0],  # N = 2
            [-3.78057, -9.5106, -12.074, 0],
            [-3.49631, -7.0815, -7.538, 21.892],
        ],
        [
            [-4.66305, -18.7688, -49.793, 104.244],  # N = 3
            [-4.11890, -11.8922, -19.031, 77.332],
            [-3.83511, -9.0723, -8.504, 35.403],
        ],
        [
            [-4.96940, -22.4694, -52.599, 51.314],  # N = 4
            [-4.42871, -14.5876, -18.228, 39.647],
            [-4.14633, -11.2500, -9.873, 54.109],
        ],
        [
            [-5.25276, -26.2183, -59.631, 50.646],  # N = 5
            [-4.71537, -17.3569, -22.660, 91.359],
            [-4.43422, -13.6078, -10.238, 76.781],
        ],
        [
            [-5.51727, -29.9760, -75.222, 202.253],  # N = 6
            [-4.98228, -20.3050, -25.224, 132.03],
            [-4.70233, -16.1253, -9.836, 94.272],
        ],
        [
            [-5.76537, -33.9165, -84.312, 245.394],  # N = 7
            [-5.23299, -23.3328, -28.955, 182.342],
            [-4.95405, -18.7352, -10.168, 120.575],
        ],
        [
            [-6.00003, -37.8892, -96.428, 335.92],  # N = 8
            [-5.46971, -26.4771, -31.034, 220.165],
            [-5.19183, -21.4328, -10.726, 157.955],
        ],
        [
            [-6.22288, -41.9496, -109.881, 466.068],  # N = 9
            [-5.69447, -29.7152, -33.784, 273.002],
            [-5.41738, -24.2882, -8.584, 169.891],
        ],
        [
            [-6.43551, -46.1151, -120.814, 566.823],  # N = 10
            [-5.90887, -33.0251, -37.208, 346.189],
            [-5.63255, -27.2042, -6.792, 177.666],
        ],
        [
            [-6.63894, -50.4287, -128.997, 642.781],  # N = 11
            [-6.11404, -36.4610, -36.246, 348.554],
            [-5.83850, -30.1995, -5.163, 210.338],
        ],
        [
            [-6.83488, -54.7119, -139.800, 736.376],  # N = 12
            [-6.31127, -39.9676, -37.021, 406.051],
            [-6.03650, -33.2381, -6.606, 317.776],
        ],
    ]
)

tau_2010["ctt"] = array(
    [
        [
            [-4.37113, -11.5882, -35.819, -334.047],  # N = 1
            [-3.83239, -5.9057, -12.490, -118.284],
            [-3.55326, -3.6596, -5.293, -63.559],
        ],
        [
            [-4.69276, -20.2284, -64.919, 88.884],  # N =2
            [-4.15387, -13.3114, -28.402, 72.741],
            [-3.87346, -10.4637, -17.408, 66.313],
        ],
        [
            [-4.99071, -23.5873, -76.924, 184.782],  # N = 3
            [-4.45311, -15.7732, -32.316, 122.705],
            [-4.17280, -12.4909, -17.912, 83.285],
        ],
        [
            [-5.26780, -27.2836, -78.971, 137.871],  # N = 4
            [-4.73244, -18.4833, -31.875, 111.817],
            [-4.45268, -14.7199, -17.969, 101.92],
        ],
        [
            [-5.52826, -30.9051, -92.490, 248.096],  # N = 5
            [-4.99491, -21.2360, -37.685, 194.208],
            [-4.71587, -17.0820, -18.631, 136.672],
        ],
        [
            [-5.77379, -34.7010, -105.937, 393.991],  # N = 6
            [-5.24217, -24.2177, -39.153, 232.528],
            [-4.96397, -19.6064, -18.858, 174.919],
        ],
        [
            [-6.00609, -38.7383, -108.605, 365.208],  # N = 7
            [-5.47664, -27.3005, -39.498, 246.918],
            [-5.19921, -22.2617, -17.910, 208.494],
        ],
        [
            [-6.22758, -42.7154, -119.622, 421.395],  # N = 8
            [-5.69983, -30.4365, -44.300, 345.48],
            [-5.42320, -24.9686, -19.688, 274.462],
        ],
        [
            [-6.43933, -46.7581, -136.691, 651.38],  # N = 9
            [-5.91298, -33.7584, -42.686, 346.629],
            [-5.63704, -27.8965, -13.880, 236.975],
        ],
        [
            [-6.64235, -50.9783, -145.462, 752.228],  # N = 10
            [-6.11753, -37.056, -48.719, 473.905],
            [-5.84215, -30.8119, -14.938, 316.006],
        ],
        [
            [-6.83743, -55.2861, -152.651, 792.577],  # N = 11
            [-6.31396, -40.5507, -46.771, 487.185],
            [-6.03921, -33.8950, -9.122, 285.164],
        ],
        [
            [-7.02582, -59.6037, -166.368, 989.879],  # N = 12
            [-6.50353, -44.0797, -47.242, 543.889],
            [-6.22941, -36.9673, -10.868, 418.414],
        ],
    ]
)


# These are the cut-off values for the left-tail vs. the rest of the
# tau distribution, for getting the p-values
tau_star = {
    "n": [-1.04, -1.53, -2.68, -3.09, -3.07, -3.77],
    "c": [-1.61, -2.62, -3.13, -3.47, -3.78, -3.93],
    "ct": [-2.89, -3.19, -3.50, -3.65, -3.80, -4.36],
    "ctt": [-3.21, -3.51, -3.81, -3.83, -4.12, -4.63],
}

tau_min = {
    "n": [-19.04, -19.62, -21.21, -23.25, -21.63, -25.74],
    "c": [-18.83, -18.86, -23.48, -28.07, -25.96, -23.27],
    "ct": [-16.18, -21.15, -25.37, -26.63, -26.53, -26.18],
    "ctt": [-17.17, -21.1, -24.33, -24.03, -24.33, -28.22],
}

tau_max = {
    "n": [inf, 1.51, 0.86, 0.88, 1.05, 1.24],
    "c": [2.74, 0.92, 0.55, 0.61, 0.79, 1],
    "ct": [0.7, 0.63, 0.71, 0.93, 1.19, 1.42],
    "ctt": [0.54, 0.79, 1.08, 1.43, 3.49, 1.92],
}


# Z values from new simulations, 500 exercises, 250,000 per ex.
adf_z_min = {"n": -inf, "c": -inf, "ct": -inf, "ctt": -inf}

adf_z_star = {"n": -1.79146, "c": -5.04709, "ct": -9.22766, "ctt": -12.88512}

adf_z_max = {"n": inf, "c": inf, "ct": inf, "ctt": inf}

# The small p parameters are for np.log(np.abs(stat))
adf_z_small_p = {
    "n": [0.05872, -0.69633, 0.02471, -0.04283],
    "c": [1.94205, -1.47677, 0.21163, -0.06288],
    "ct": [4.05596, -2.34128, 0.41403, -0.08312],
    "ctt": [5.68974, -2.98948, 0.55752, -0.09629],
}

adf_z_large_p = {
    "n": [0.56681, 0.67544, 0.06881, 0.00235],
    "c": [1.70059, 0.49465, 0.02636, 0.00055],
    "ct": [2.60323, 0.39217, 0.01321, 0.00019],
    "ctt": [3.2269, 0.34323, 0.00887, 0.0001],
}

adf_z_cv_approx = {
    "n": [
        [-13.30499, 331.55385, -8807.11008, 88544.76648],
        [-7.82957, 222.75548, -5863.14998, 58024.48295],
        [-5.57486, 170.79065, -4422.48027, 43264.68244],
    ],
    "c": [
        [-20.6258, 119.28619, -442.99517, 1082.9222],
        [-14.09457, 58.46106, -164.08684, 479.02908],
        [-11.25118, 38.48786, -87.62853, 246.93531],
    ],
    "ct": [
        [-29.3568, 232.33249, -1171.21447, 3166.83631],
        [-21.71085, 130.15894, -504.95264, 1170.38109],
        [-18.24475, 93.25896, -305.14509, 581.02963],
    ],
    "ctt": [
        [-36.5955, 354.30552, -2214.58403, 7401.70512],
        [-28.11176, 212.46258, -1067.94737, 3169.14023],
        [-24.18572, 158.63387, -695.72916, 1833.08527],
    ],
}
