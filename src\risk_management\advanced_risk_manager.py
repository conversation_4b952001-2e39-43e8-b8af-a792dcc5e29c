"""
Advanced Risk Management System - نظام إدارة المخاطر المتقدم
=========================================================
نظام شامل لإدارة المخاطر في التداول الآلي
"""

import json
import os
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class AdvancedRiskManager:
    """نظام إدارة المخاطر المتقدم"""
    
    def __init__(self):
        """تهيئة نظام إدارة المخاطر"""
        self.name = "Advanced Risk Management System"
        self.version = "2.0.0"
        
        # إعدادات إدارة المخاطر
        self.risk_settings = {
            # إدارة رأس المال
            'max_risk_per_trade': 0.02,        # 2% من رأس المال لكل صفقة
            'max_daily_risk': 0.10,            # 10% من رأس المال يومياً
            'max_weekly_risk': 0.25,           # 25% من رأس المال أسبوعياً
            'max_monthly_risk': 0.50,          # 50% من رأس المال شهرياً
            
            # حدود الخسائر
            'max_consecutive_losses': 3,        # أقصى خسائر متتالية
            'daily_loss_limit': 0.05,          # 5% خسارة يومية
            'weekly_loss_limit': 0.15,         # 15% خسارة أسبوعية
            'monthly_loss_limit': 0.30,        # 30% خسارة شهرية
            
            # حدود الصفقات
            'max_trades_per_hour': 5,          # أقصى صفقات في الساعة
            'max_trades_per_day': 20,          # أقصى صفقات في اليوم
            'max_concurrent_trades': 1,        # أقصى صفقات متزامنة
            
            # إعدادات الثقة
            'min_confidence_for_trade': 80,    # أقل ثقة للتداول
            'confidence_scaling': True,        # تدرج المبلغ حسب الثقة
            
            # إعدادات التوقف
            'emergency_stop_enabled': True,    # إيقاف طارئ
            'cooling_period_minutes': 30,      # فترة تهدئة بعد الخسائر
            'max_drawdown': 0.20               # أقصى انخفاض مسموح
        }
        
        # إحصائيات المخاطر
        self.risk_stats = {
            'current_balance': 0,
            'starting_balance': 0,
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'consecutive_losses': 0,
            'max_consecutive_losses': 0,
            'total_profit': 0,
            'total_loss': 0,
            'current_drawdown': 0,
            'max_drawdown': 0,
            'daily_trades': 0,
            'weekly_trades': 0,
            'monthly_trades': 0,
            'last_trade_time': None,
            'last_reset_time': datetime.now(),
            'emergency_stop_active': False,
            'cooling_period_end': None
        }
        
        # سجل المخاطر
        self.risk_log = []
        self.max_log_size = 1000
        
        logger.info(f"✅ {self.name} v{self.version} initialized")
    
    def set_initial_balance(self, balance: float):
        """تعيين الرصيد الأولي"""
        try:
            self.risk_stats['current_balance'] = balance
            self.risk_stats['starting_balance'] = balance
            logger.info(f"Initial balance set to ${balance:.2f}")
        except Exception as e:
            logger.error(f"Error setting initial balance: {e}")
    
    def calculate_position_size(self, confidence: float, account_balance: float, 
                              signal_strength: str = "STANDARD") -> Dict[str, Any]:
        """
        حساب حجم المركز بناءً على إدارة المخاطر
        
        Args:
            confidence: مستوى الثقة في الإشارة (0-100)
            account_balance: رصيد الحساب الحالي
            signal_strength: قوة الإشارة (HIGH, STANDARD, LOW)
            
        Returns:
            معلومات حجم المركز والمخاطر
        """
        try:
            # تحديث الرصيد الحالي
            self.risk_stats['current_balance'] = account_balance
            
            # التحقق من حالة الإيقاف الطارئ
            if self._is_emergency_stop_active():
                return self._create_rejection_result("Emergency stop is active")
            
            # التحقق من فترة التهدئة
            if self._is_cooling_period_active():
                return self._create_rejection_result("Cooling period is active")
            
            # التحقق من حدود التداول
            limits_check = self._check_trading_limits()
            if not limits_check['allowed']:
                return self._create_rejection_result(limits_check['reason'])
            
            # التحقق من مستوى الثقة
            min_confidence = self.risk_settings['min_confidence_for_trade']
            if confidence < min_confidence:
                return self._create_rejection_result(f"Confidence {confidence}% below minimum {min_confidence}%")
            
            # حساب حجم المركز الأساسي
            base_risk = self.risk_settings['max_risk_per_trade']
            base_amount = account_balance * base_risk
            
            # تعديل حسب الثقة
            if self.risk_settings['confidence_scaling']:
                confidence_factor = self._calculate_confidence_factor(confidence)
                adjusted_amount = base_amount * confidence_factor
            else:
                adjusted_amount = base_amount
            
            # تعديل حسب قوة الإشارة
            signal_factor = self._get_signal_strength_factor(signal_strength)
            final_amount = adjusted_amount * signal_factor
            
            # تعديل حسب الأداء الحالي
            performance_factor = self._calculate_performance_factor()
            final_amount *= performance_factor
            
            # التأكد من الحدود
            final_amount = self._apply_amount_limits(final_amount, account_balance)
            
            # حساب المخاطر
            risk_analysis = self._analyze_position_risk(final_amount, account_balance)
            
            return {
                'status': 'APPROVED',
                'position_size': round(final_amount, 2),
                'risk_percentage': (final_amount / account_balance) * 100,
                'confidence_factor': confidence_factor if self.risk_settings['confidence_scaling'] else 1.0,
                'signal_factor': signal_factor,
                'performance_factor': performance_factor,
                'risk_analysis': risk_analysis,
                'max_loss': final_amount,  # في الخيارات الثنائية، الخسارة القصوى = المبلغ المستثمر
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return self._create_rejection_result(f"Calculation error: {str(e)}")
    
    def validate_trade(self, trade_amount: float, confidence: float, 
                      account_balance: float) -> Dict[str, Any]:
        """
        التحقق من صحة الصفقة قبل التنفيذ
        
        Args:
            trade_amount: مبلغ الصفقة
            confidence: مستوى الثقة
            account_balance: رصيد الحساب
            
        Returns:
            نتيجة التحقق
        """
        try:
            validations = []
            
            # التحقق من الرصيد الكافي
            if trade_amount > account_balance:
                validations.append({
                    'check': 'Balance',
                    'status': 'FAIL',
                    'message': f'Insufficient balance: ${trade_amount:.2f} > ${account_balance:.2f}'
                })
            else:
                validations.append({
                    'check': 'Balance',
                    'status': 'PASS',
                    'message': 'Sufficient balance available'
                })
            
            # التحقق من نسبة المخاطرة
            risk_percentage = (trade_amount / account_balance) * 100
            max_risk = self.risk_settings['max_risk_per_trade'] * 100
            
            if risk_percentage > max_risk:
                validations.append({
                    'check': 'Risk Percentage',
                    'status': 'FAIL',
                    'message': f'Risk too high: {risk_percentage:.1f}% > {max_risk:.1f}%'
                })
            else:
                validations.append({
                    'check': 'Risk Percentage',
                    'status': 'PASS',
                    'message': f'Risk acceptable: {risk_percentage:.1f}%'
                })
            
            # التحقق من مستوى الثقة
            min_confidence = self.risk_settings['min_confidence_for_trade']
            if confidence < min_confidence:
                validations.append({
                    'check': 'Confidence',
                    'status': 'FAIL',
                    'message': f'Confidence too low: {confidence:.1f}% < {min_confidence}%'
                })
            else:
                validations.append({
                    'check': 'Confidence',
                    'status': 'PASS',
                    'message': f'Confidence acceptable: {confidence:.1f}%'
                })
            
            # التحقق من حدود التداول اليومية
            daily_limit = self.risk_settings['max_trades_per_day']
            if self.risk_stats['daily_trades'] >= daily_limit:
                validations.append({
                    'check': 'Daily Limit',
                    'status': 'FAIL',
                    'message': f'Daily trade limit reached: {self.risk_stats["daily_trades"]}/{daily_limit}'
                })
            else:
                validations.append({
                    'check': 'Daily Limit',
                    'status': 'PASS',
                    'message': f'Daily trades: {self.risk_stats["daily_trades"]}/{daily_limit}'
                })
            
            # التحقق من الخسائر المتتالية
            max_consecutive = self.risk_settings['max_consecutive_losses']
            if self.risk_stats['consecutive_losses'] >= max_consecutive:
                validations.append({
                    'check': 'Consecutive Losses',
                    'status': 'FAIL',
                    'message': f'Too many consecutive losses: {self.risk_stats["consecutive_losses"]}'
                })
            else:
                validations.append({
                    'check': 'Consecutive Losses',
                    'status': 'PASS',
                    'message': f'Consecutive losses: {self.risk_stats["consecutive_losses"]}/{max_consecutive}'
                })
            
            # تحديد النتيجة النهائية
            failed_checks = [v for v in validations if v['status'] == 'FAIL']
            
            if failed_checks:
                return {
                    'status': 'REJECTED',
                    'validations': validations,
                    'failed_checks': len(failed_checks),
                    'rejection_reasons': [v['message'] for v in failed_checks]
                }
            else:
                return {
                    'status': 'APPROVED',
                    'validations': validations,
                    'risk_percentage': risk_percentage,
                    'message': 'All risk checks passed'
                }
                
        except Exception as e:
            logger.error(f"Error validating trade: {e}")
            return {
                'status': 'ERROR',
                'error': str(e)
            }
    
    def record_trade_result(self, trade_amount: float, outcome: str, 
                          profit_loss: float, confidence: float):
        """
        تسجيل نتيجة الصفقة وتحديث الإحصائيات
        
        Args:
            trade_amount: مبلغ الصفقة
            outcome: نتيجة الصفقة (WIN/LOSS)
            profit_loss: الربح أو الخسارة
            confidence: مستوى الثقة في الإشارة
        """
        try:
            # تحديث الإحصائيات الأساسية
            self.risk_stats['total_trades'] += 1
            self.risk_stats['daily_trades'] += 1
            self.risk_stats['last_trade_time'] = datetime.now()
            
            if outcome == 'WIN':
                self.risk_stats['winning_trades'] += 1
                self.risk_stats['total_profit'] += profit_loss
                self.risk_stats['consecutive_losses'] = 0  # إعادة تعيين الخسائر المتتالية
                self.risk_stats['current_balance'] += profit_loss
            else:
                self.risk_stats['losing_trades'] += 1
                self.risk_stats['total_loss'] += abs(profit_loss)
                self.risk_stats['consecutive_losses'] += 1
                self.risk_stats['current_balance'] -= abs(profit_loss)
                
                # تحديث أقصى خسائر متتالية
                if self.risk_stats['consecutive_losses'] > self.risk_stats['max_consecutive_losses']:
                    self.risk_stats['max_consecutive_losses'] = self.risk_stats['consecutive_losses']
            
            # حساب الانخفاض الحالي
            self._update_drawdown()
            
            # التحقق من تفعيل الإيقاف الطارئ
            self._check_emergency_stop()
            
            # التحقق من تفعيل فترة التهدئة
            self._check_cooling_period()
            
            # تسجيل في السجل
            self._log_trade_result(trade_amount, outcome, profit_loss, confidence)
            
            logger.info(f"Trade recorded: {outcome}, Amount: ${trade_amount:.2f}, P/L: ${profit_loss:.2f}")
            
        except Exception as e:
            logger.error(f"Error recording trade result: {e}")

    # ==================== دوال مساعدة ====================

    def _is_emergency_stop_active(self) -> bool:
        """التحقق من حالة الإيقاف الطارئ"""
        return self.risk_stats.get('emergency_stop_active', False)

    def _is_cooling_period_active(self) -> bool:
        """التحقق من فترة التهدئة"""
        cooling_end = self.risk_stats.get('cooling_period_end')
        if cooling_end:
            return datetime.now() < datetime.fromisoformat(cooling_end)
        return False

    def _check_trading_limits(self) -> Dict[str, Any]:
        """التحقق من حدود التداول"""
        try:
            # حد الصفقات اليومية
            if self.risk_stats['daily_trades'] >= self.risk_settings['max_trades_per_day']:
                return {'allowed': False, 'reason': 'Daily trade limit exceeded'}

            # حد الصفقات في الساعة
            hour_ago = datetime.now() - timedelta(hours=1)
            recent_trades = len([log for log in self.risk_log
                               if datetime.fromisoformat(log['timestamp']) > hour_ago])

            if recent_trades >= self.risk_settings['max_trades_per_hour']:
                return {'allowed': False, 'reason': 'Hourly trade limit exceeded'}

            return {'allowed': True, 'reason': 'All limits OK'}

        except Exception as e:
            logger.error(f"Error checking trading limits: {e}")
            return {'allowed': False, 'reason': f'Limit check error: {str(e)}'}

    def _calculate_confidence_factor(self, confidence: float) -> float:
        """حساب معامل الثقة لتعديل حجم المركز"""
        try:
            # تحويل الثقة إلى معامل (0.5 - 1.5)
            # ثقة 80% = معامل 1.0
            # ثقة 90% = معامل 1.25
            # ثقة 95% = معامل 1.5

            normalized_confidence = (confidence - 50) / 50  # تطبيع من 0 إلى 1
            factor = 0.5 + normalized_confidence  # من 0.5 إلى 1.5

            return max(0.5, min(factor, 1.5))

        except Exception:
            return 1.0

    def _get_signal_strength_factor(self, signal_strength: str) -> float:
        """الحصول على معامل قوة الإشارة"""
        factors = {
            'HIGH': 1.2,
            'STANDARD': 1.0,
            'LOW': 0.8
        }
        return factors.get(signal_strength, 1.0)

    def _calculate_performance_factor(self) -> float:
        """حساب معامل الأداء لتعديل حجم المركز"""
        try:
            total_trades = self.risk_stats['total_trades']

            if total_trades < 10:
                return 1.0  # أداء محايد للصفقات القليلة

            win_rate = self.risk_stats['winning_trades'] / total_trades

            # تعديل حجم المركز بناءً على الأداء
            if win_rate >= 0.8:
                return 1.2  # زيادة 20% للأداء الممتاز
            elif win_rate >= 0.7:
                return 1.1  # زيادة 10% للأداء الجيد
            elif win_rate >= 0.6:
                return 1.0  # حجم عادي للأداء المقبول
            elif win_rate >= 0.5:
                return 0.9  # تقليل 10% للأداء الضعيف
            else:
                return 0.8  # تقليل 20% للأداء السيء

        except Exception:
            return 1.0

    def _apply_amount_limits(self, amount: float, balance: float) -> float:
        """تطبيق حدود المبلغ"""
        try:
            # حد أدنى (1% من الرصيد أو $1)
            min_amount = max(balance * 0.01, 1.0)

            # حد أقصى (5% من الرصيد)
            max_amount = balance * 0.05

            return max(min_amount, min(amount, max_amount))

        except Exception:
            return amount

    def _analyze_position_risk(self, amount: float, balance: float) -> Dict[str, Any]:
        """تحليل مخاطر المركز"""
        try:
            risk_percentage = (amount / balance) * 100

            # تصنيف المخاطر
            if risk_percentage <= 1:
                risk_level = "LOW"
            elif risk_percentage <= 2:
                risk_level = "MODERATE"
            elif risk_percentage <= 3:
                risk_level = "HIGH"
            else:
                risk_level = "VERY_HIGH"

            # حساب المخاطر المتراكمة
            daily_risk = self._calculate_daily_risk(amount)
            weekly_risk = self._calculate_weekly_risk(amount)

            return {
                'risk_level': risk_level,
                'risk_percentage': round(risk_percentage, 2),
                'daily_risk': round(daily_risk, 2),
                'weekly_risk': round(weekly_risk, 2),
                'max_loss': amount,
                'risk_reward_ratio': self._estimate_risk_reward_ratio()
            }

        except Exception as e:
            logger.error(f"Error analyzing position risk: {e}")
            return {'risk_level': 'UNKNOWN', 'error': str(e)}

    def _calculate_daily_risk(self, new_amount: float) -> float:
        """حساب المخاطر اليومية"""
        try:
            today = datetime.now().date()
            daily_trades = [log for log in self.risk_log
                          if datetime.fromisoformat(log['timestamp']).date() == today]

            daily_amount = sum(log.get('amount', 0) for log in daily_trades)
            total_daily_risk = (daily_amount + new_amount) / self.risk_stats['current_balance'] * 100

            return total_daily_risk

        except Exception:
            return 0

    def _calculate_weekly_risk(self, new_amount: float) -> float:
        """حساب المخاطر الأسبوعية"""
        try:
            week_ago = datetime.now() - timedelta(days=7)
            weekly_trades = [log for log in self.risk_log
                           if datetime.fromisoformat(log['timestamp']) > week_ago]

            weekly_amount = sum(log.get('amount', 0) for log in weekly_trades)
            total_weekly_risk = (weekly_amount + new_amount) / self.risk_stats['current_balance'] * 100

            return total_weekly_risk

        except Exception:
            return 0

    def _estimate_risk_reward_ratio(self) -> float:
        """تقدير نسبة المخاطر إلى المكافآت"""
        try:
            # في الخيارات الثنائية، نسبة المكافأة عادة 80-90%
            # المخاطر = 100% من المبلغ المستثمر
            # المكافأة = 80-90% من المبلغ المستثمر

            avg_payout = 0.85  # متوسط العائد 85%
            return avg_payout / 1.0  # المخاطر = 100%

        except Exception:
            return 0.85

    def _update_drawdown(self):
        """تحديث الانخفاض الحالي"""
        try:
            starting_balance = self.risk_stats['starting_balance']
            current_balance = self.risk_stats['current_balance']

            if starting_balance > 0:
                drawdown = (starting_balance - current_balance) / starting_balance
                self.risk_stats['current_drawdown'] = max(0, drawdown)

                if drawdown > self.risk_stats['max_drawdown']:
                    self.risk_stats['max_drawdown'] = drawdown

        except Exception as e:
            logger.error(f"Error updating drawdown: {e}")

    def _check_emergency_stop(self):
        """التحقق من تفعيل الإيقاف الطارئ"""
        try:
            if not self.risk_settings['emergency_stop_enabled']:
                return

            # تفعيل الإيقاف الطارئ في الحالات التالية:

            # 1. تجاوز الانخفاض الأقصى
            max_drawdown = self.risk_settings['max_drawdown']
            if self.risk_stats['current_drawdown'] >= max_drawdown:
                self._activate_emergency_stop(f"Maximum drawdown exceeded: {self.risk_stats['current_drawdown']:.1%}")
                return

            # 2. تجاوز الخسائر المتتالية
            max_consecutive = self.risk_settings['max_consecutive_losses']
            if self.risk_stats['consecutive_losses'] >= max_consecutive:
                self._activate_emergency_stop(f"Maximum consecutive losses: {self.risk_stats['consecutive_losses']}")
                return

            # 3. خسارة يومية كبيرة
            daily_loss_limit = self.risk_settings['daily_loss_limit']
            daily_loss = self._calculate_daily_loss()
            if daily_loss >= daily_loss_limit:
                self._activate_emergency_stop(f"Daily loss limit exceeded: {daily_loss:.1%}")
                return

        except Exception as e:
            logger.error(f"Error checking emergency stop: {e}")

    def _check_cooling_period(self):
        """التحقق من تفعيل فترة التهدئة"""
        try:
            # تفعيل فترة التهدئة بعد خسائر متتالية
            if self.risk_stats['consecutive_losses'] >= 2:
                cooling_minutes = self.risk_settings['cooling_period_minutes']
                cooling_end = datetime.now() + timedelta(minutes=cooling_minutes)
                self.risk_stats['cooling_period_end'] = cooling_end.isoformat()

                logger.warning(f"Cooling period activated for {cooling_minutes} minutes")

        except Exception as e:
            logger.error(f"Error checking cooling period: {e}")

    def _activate_emergency_stop(self, reason: str):
        """تفعيل الإيقاف الطارئ"""
        try:
            self.risk_stats['emergency_stop_active'] = True
            logger.critical(f"EMERGENCY STOP ACTIVATED: {reason}")

            # حفظ سجل الإيقاف الطارئ
            emergency_log = {
                'timestamp': datetime.now().isoformat(),
                'reason': reason,
                'balance': self.risk_stats['current_balance'],
                'drawdown': self.risk_stats['current_drawdown'],
                'consecutive_losses': self.risk_stats['consecutive_losses']
            }

            self._save_emergency_log(emergency_log)

        except Exception as e:
            logger.error(f"Error activating emergency stop: {e}")

    def _calculate_daily_loss(self) -> float:
        """حساب الخسارة اليومية"""
        try:
            today = datetime.now().date()
            daily_trades = [log for log in self.risk_log
                          if datetime.fromisoformat(log['timestamp']).date() == today
                          and log.get('outcome') == 'LOSS']

            daily_loss = sum(log.get('profit_loss', 0) for log in daily_trades)
            return abs(daily_loss) / self.risk_stats['current_balance']

        except Exception:
            return 0

    def _log_trade_result(self, amount: float, outcome: str, profit_loss: float, confidence: float):
        """تسجيل نتيجة الصفقة في السجل"""
        try:
            log_entry = {
                'timestamp': datetime.now().isoformat(),
                'amount': amount,
                'outcome': outcome,
                'profit_loss': profit_loss,
                'confidence': confidence,
                'balance_after': self.risk_stats['current_balance'],
                'consecutive_losses': self.risk_stats['consecutive_losses'],
                'drawdown': self.risk_stats['current_drawdown']
            }

            self.risk_log.append(log_entry)

            # الحفاظ على حجم السجل
            if len(self.risk_log) > self.max_log_size:
                self.risk_log = self.risk_log[-self.max_log_size:]

        except Exception as e:
            logger.error(f"Error logging trade result: {e}")

    def _save_emergency_log(self, emergency_log: Dict[str, Any]):
        """حفظ سجل الإيقاف الطارئ"""
        try:
            emergency_dir = "data/emergency_logs"
            if not os.path.exists(emergency_dir):
                os.makedirs(emergency_dir)

            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            file_path = os.path.join(emergency_dir, f"emergency_stop_{timestamp}.json")

            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(emergency_log, f, indent=2, ensure_ascii=False)

        except Exception as e:
            logger.error(f"Error saving emergency log: {e}")

    def _create_rejection_result(self, reason: str) -> Dict[str, Any]:
        """إنشاء نتيجة رفض"""
        return {
            'status': 'REJECTED',
            'reason': reason,
            'position_size': 0,
            'risk_percentage': 0,
            'timestamp': datetime.now().isoformat()
        }

    def get_risk_summary(self) -> Dict[str, Any]:
        """الحصول على ملخص المخاطر"""
        try:
            win_rate = 0
            if self.risk_stats['total_trades'] > 0:
                win_rate = self.risk_stats['winning_trades'] / self.risk_stats['total_trades']

            return {
                'risk_manager': self.name,
                'version': self.version,
                'current_balance': self.risk_stats['current_balance'],
                'starting_balance': self.risk_stats['starting_balance'],
                'total_profit_loss': self.risk_stats['total_profit'] - self.risk_stats['total_loss'],
                'win_rate': win_rate,
                'total_trades': self.risk_stats['total_trades'],
                'consecutive_losses': self.risk_stats['consecutive_losses'],
                'current_drawdown': self.risk_stats['current_drawdown'],
                'max_drawdown': self.risk_stats['max_drawdown'],
                'emergency_stop_active': self.risk_stats['emergency_stop_active'],
                'cooling_period_active': self._is_cooling_period_active(),
                'risk_settings': self.risk_settings.copy(),
                'last_update': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error getting risk summary: {e}")
            return {'error': str(e)}

    def reset_daily_stats(self):
        """إعادة تعيين الإحصائيات اليومية"""
        try:
            self.risk_stats['daily_trades'] = 0
            logger.info("Daily stats reset")
        except Exception as e:
            logger.error(f"Error resetting daily stats: {e}")

    def deactivate_emergency_stop(self, reason: str = "Manual override"):
        """إلغاء تفعيل الإيقاف الطارئ"""
        try:
            self.risk_stats['emergency_stop_active'] = False
            logger.warning(f"Emergency stop deactivated: {reason}")
        except Exception as e:
            logger.error(f"Error deactivating emergency stop: {e}")
