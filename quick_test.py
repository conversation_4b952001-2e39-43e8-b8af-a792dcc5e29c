#!/usr/bin/env python3
"""
اختبار سريع للاستراتيجية V2
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("🔍 Quick test of Strategy V2...")

try:
    from src.strategy_v2.decision_maker import StrategyDecisionMaker
    print("✅ StrategyDecisionMaker imported successfully")
    
    # إنشاء مثيل
    strategy = StrategyDecisionMaker()
    print("✅ Strategy instance created")
    
    # اختبار تحليل واحد
    result = strategy.analyze_market("EURUSD")
    print(f"📊 Analysis result: {result.get('signal', 'N/A')} with {result.get('confidence', 0):.1f}% confidence")
    
    print("✅ Quick test completed successfully!")
    
except Exception as e:
    print(f"❌ Test failed: {e}")
    import traceback
    traceback.print_exc()
