from numpy import asarray

kpss_critical_values = {}
c = (
    (100.000, 0.0000),
    (99.500, 0.0219),
    (99.000, 0.0249),
    (98.000, 0.0289),
    (97.000, 0.0318),
    (96.000, 0.0344),
    (95.000, 0.0366),
    (93.500, 0.0397),
    (91.500, 0.0435),
    (89.500, 0.0470),
    (86.000, 0.0527),
    (74.000, 0.0720),
    (70.500, 0.0778),
    (67.000, 0.0839),
    (64.000, 0.0894),
    (61.500, 0.0941),
    (58.500, 0.1001),
    (56.000, 0.1053),
    (53.000, 0.1119),
    (50.000, 0.1190),
    (47.000, 0.1266),
    (44.500, 0.1333),
    (42.000, 0.1406),
    (39.500, 0.1484),
    (37.000, 0.1568),
    (34.500, 0.1659),
    (32.000, 0.1758),
    (30.000, 0.1845),
    (28.000, 0.1939),
    (26.000, 0.2041),
    (24.000, 0.2153),
    (22.500, 0.2244),
    (21.000, 0.2343),
    (19.500, 0.2451),
    (18.000, 0.2568),
    (16.500, 0.2698),
    (15.000, 0.2842),
    (14.000, 0.2947),
    (12.000, 0.3186),
    (10.000, 0.3475),
    (9.500, 0.3557),
    (9.000, 0.3644),
    (8.500, 0.3736),
    (8.000, 0.3834),
    (7.500, 0.3940),
    (7.000, 0.4053),
    (6.500, 0.4176),
    (6.000, 0.4309),
    (5.500, 0.4454),
    (5.000, 0.4614),
    (4.500, 0.4793),
    (4.000, 0.4993),
    (3.500, 0.5222),
    (3.000, 0.5488),
    (2.500, 0.5804),
    (2.000, 0.6195),
    (1.500, 0.6704),
    (1.000, 0.7428),
    (0.900, 0.7618),
    (0.800, 0.7830),
    (0.700, 0.8073),
    (0.600, 0.8353),
    (0.500, 0.8683),
    (0.400, 0.9090),
    (0.300, 0.9618),
    (0.200, 1.0369),
    (0.100, 1.1650),
    (0.100, 1.1650),
    (0.090, 1.1845),
    (0.080, 1.2068),
    (0.070, 1.2318),
    (0.060, 1.2608),
    (0.050, 1.2948),
    (0.040, 1.3372),
    (0.030, 1.3912),
    (0.020, 1.4671),
    (0.010, 1.6000),
)
kpss_critical_values["c"] = asarray(c)

ct = (
    (100.000, 0.0000),
    (99.500, 0.0156),
    (99.000, 0.0173),
    (98.500, 0.0185),
    (97.500, 0.0203),
    (96.000, 0.0224),
    (94.500, 0.0240),
    (93.000, 0.0254),
    (91.000, 0.0272),
    (88.500, 0.0291),
    (86.000, 0.0309),
    (81.000, 0.0343),
    (68.500, 0.0424),
    (62.500, 0.0464),
    (56.500, 0.0507),
    (53.500, 0.0529),
    (51.000, 0.0548),
    (48.000, 0.0573),
    (45.500, 0.0594),
    (43.000, 0.0616),
    (40.000, 0.0645),
    (37.500, 0.0670),
    (35.500, 0.0691),
    (33.000, 0.0720),
    (31.000, 0.0744),
    (29.000, 0.0770),
    (27.500, 0.0791),
    (25.500, 0.0820),
    (24.000, 0.0844),
    (22.500, 0.0869),
    (21.000, 0.0897),
    (18.500, 0.0946),
    (16.000, 0.1004),
    (14.000, 0.1057),
    (12.000, 0.1119),
    (10.000, 0.1193),
    (9.500, 0.1214),
    (9.000, 0.1236),
    (8.500, 0.1259),
    (8.000, 0.1284),
    (7.500, 0.1310),
    (7.000, 0.1339),
    (6.500, 0.1370),
    (6.000, 0.1403),
    (5.500, 0.1439),
    (5.000, 0.1479),
    (4.500, 0.1523),
    (4.000, 0.1573),
    (3.500, 0.1630),
    (3.000, 0.1696),
    (2.500, 0.1774),
    (2.000, 0.1871),
    (1.500, 0.1996),
    (1.000, 0.2175),
    (0.900, 0.2222),
    (0.800, 0.2275),
    (0.700, 0.2335),
    (0.600, 0.2404),
    (0.500, 0.2486),
    (0.400, 0.2588),
    (0.300, 0.2719),
    (0.200, 0.2904),
    (0.100, 0.3223),
    (0.100, 0.3223),
    (0.090, 0.3272),
    (0.080, 0.3326),
    (0.070, 0.3388),
    (0.060, 0.3460),
    (0.050, 0.3546),
    (0.040, 0.3653),
    (0.030, 0.3790),
    (0.020, 0.3985),
    (0.010, 0.4313),
)
kpss_critical_values["ct"] = asarray(ct)
