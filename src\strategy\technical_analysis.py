"""
Technical Analysis Layer - الطبقة الأولى
تحليل فني متقدم باستخدام المؤشرات التقليدية والأنماط
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import logging
import json
import os

logger = logging.getLogger(__name__)

class TechnicalAnalysisLayer:
    """الطبقة الأولى: التحليل الفني"""
    
    def __init__(self):
        """تهيئة طبقة التحليل الفني"""
        self.name = "Technical Analysis Layer"
        self.weight = 0.25  # وزن هذه الطبقة في القرار النهائي
        
        # إعدادات المؤشرات
        self.sma_periods = [10, 20]
        self.ema_periods = [5, 10, 21]
        self.rsi_periods = [5, 14]
        self.bb_period = 20
        self.bb_std = 2
        
        # Technical Analysis initialized
    
    def analyze_candles(self, candles: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        تحليل الشموع وتحديد الأنماط
        
        Args:
            candles: قائمة الشموع (آخر 100 شمعة)
            
        Returns:
            نتائج تحليل الشموع
        """
        if len(candles) < 3:
            return {"error": "Not enough candles for pattern analysis"}
        
        try:
            # تحويل البيانات إلى DataFrame
            df = pd.DataFrame(candles)
            df['open'] = pd.to_numeric(df['open'])
            df['high'] = pd.to_numeric(df['high'])
            df['low'] = pd.to_numeric(df['low'])
            df['close'] = pd.to_numeric(df['close'])
            
            patterns = {
                'doji': self._detect_doji(df),
                'engulfing': self._detect_engulfing(df),
                'pin_bar': self._detect_pin_bar(df),
                'hammer': self._detect_hammer(df),
                'shooting_star': self._detect_shooting_star(df)
            }
            
            return {
                'patterns': patterns,
                'pattern_strength': self._calculate_pattern_strength(patterns),
                'trend_direction': self._determine_trend_direction(df)
            }
            
        except Exception as e:
            logger.error(f"Error in candle analysis: {e}")
            return {"error": str(e)}
    
    def analyze_moving_averages(self, candles: List[Dict[str, Any]]) -> Dict[str, Any]:
        """تحليل المتوسطات المتحركة"""
        try:
            df = pd.DataFrame(candles)
            df['close'] = pd.to_numeric(df['close'])
            
            # حساب المتوسطات المتحركة
            sma_signals = {}
            ema_signals = {}
            
            for period in self.sma_periods:
                if len(df) >= period:
                    df[f'SMA_{period}'] = df['close'].rolling(window=period).mean()
                    sma_signals[f'SMA_{period}'] = self._analyze_ma_signal(df['close'], df[f'SMA_{period}'])
            
            for period in self.ema_periods:
                if len(df) >= period:
                    df[f'EMA_{period}'] = df['close'].ewm(span=period).mean()
                    ema_signals[f'EMA_{period}'] = self._analyze_ma_signal(df['close'], df[f'EMA_{period}'])
            
            # تحليل تقاطع المتوسطات
            crossover_signals = self._analyze_ma_crossovers(df)
            
            return {
                'sma_signals': sma_signals,
                'ema_signals': ema_signals,
                'crossover_signals': crossover_signals,
                'ma_trend_strength': self._calculate_ma_trend_strength(df)
            }
            
        except Exception as e:
            logger.error(f"Error in moving averages analysis: {e}")
            return {"error": str(e)}
    
    def analyze_oscillators(self, candles: List[Dict[str, Any]]) -> Dict[str, Any]:
        """تحليل المذبذبات (RSI, MACD)"""
        try:
            df = pd.DataFrame(candles)
            df['close'] = pd.to_numeric(df['close'])
            
            oscillator_signals = {}
            
            # تحليل RSI
            for period in self.rsi_periods:
                if len(df) >= period + 1:
                    rsi = self._calculate_rsi(df['close'], period)
                    oscillator_signals[f'RSI_{period}'] = self._analyze_rsi_signal(rsi)
            
            # تحليل MACD
            if len(df) >= 26:
                macd_data = self._calculate_macd(df['close'])
                oscillator_signals['MACD'] = self._analyze_macd_signal(macd_data)
            
            return {
                'oscillator_signals': oscillator_signals,
                'momentum_strength': self._calculate_momentum_strength(oscillator_signals)
            }
            
        except Exception as e:
            logger.error(f"Error in oscillators analysis: {e}")
            return {"error": str(e)}
    
    def analyze_bollinger_bands(self, candles: List[Dict[str, Any]]) -> Dict[str, Any]:
        """تحليل بولينجر باندز"""
        try:
            df = pd.DataFrame(candles)
            df['close'] = pd.to_numeric(df['close'])
            
            if len(df) < self.bb_period:
                return {"error": "Not enough data for Bollinger Bands"}
            
            # حساب بولينجر باندز
            sma = df['close'].rolling(window=self.bb_period).mean()
            std = df['close'].rolling(window=self.bb_period).std()
            
            upper_band = sma + (std * self.bb_std)
            lower_band = sma - (std * self.bb_std)
            
            current_price = df['close'].iloc[-1]
            current_upper = upper_band.iloc[-1]
            current_lower = lower_band.iloc[-1]
            current_sma = sma.iloc[-1]
            
            # تحليل الإشارات
            bb_signal = self._analyze_bb_signal(current_price, current_upper, current_lower, current_sma)
            
            return {
                'bb_signal': bb_signal,
                'price_position': self._calculate_bb_position(current_price, current_upper, current_lower),
                'volatility_level': self._calculate_volatility_level(std.iloc[-1], std.mean())
            }
            
        except Exception as e:
            logger.error(f"Error in Bollinger Bands analysis: {e}")
            return {"error": str(e)}
    
    def generate_technical_signal(self, candles: List[Dict[str, Any]]) -> Dict[str, Any]:
        """توليد الإشارة الفنية النهائية"""
        try:
            # تسجيل بداية التحليل الجديد
            analysis_time = datetime.now().strftime('%H:%M:%S.%f')[:-3]
            logger.debug(f"🔧 Technical analysis starting at {analysis_time} with {len(candles)} candles")

            # تحليل جميع المكونات (تحليل جديد كامل)
            candle_analysis = self.analyze_candles(candles)
            ma_analysis = self.analyze_moving_averages(candles)
            oscillator_analysis = self.analyze_oscillators(candles)
            bb_analysis = self.analyze_bollinger_bands(candles)
            
            # حساب النقاط لكل نوع تحليل
            scores = {
                'candle_score': self._calculate_candle_score(candle_analysis),
                'ma_score': self._calculate_ma_score(ma_analysis),
                'oscillator_score': self._calculate_oscillator_score(oscillator_analysis),
                'bb_score': self._calculate_bb_score(bb_analysis)
            }
            
            # حساب النتيجة الإجمالية
            total_score = sum(scores.values()) / len(scores)

            # تحديد الاتجاه والقوة
            direction = "CALL" if total_score > 0 else "PUT"
            strength = abs(total_score)
            confidence = min(strength * 100, 100)
            
            # تحديد زمن الصفقة المقترح
            suggested_expiry = self._suggest_expiry_time(strength, confidence, bb_analysis)

            return {
                'layer': self.name,
                'weight': self.weight,
                'signal': direction,
                'strength': strength,
                'confidence': confidence,
                'suggested_expiry_minutes': suggested_expiry,
                'scores': scores,
                'total_score': total_score,
                'analysis_details': {
                    'candles': candle_analysis,
                    'moving_averages': ma_analysis,
                    'oscillators': oscillator_analysis,
                    'bollinger_bands': bb_analysis
                },
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error generating technical signal: {e}")
            return {
                'layer': self.name,
                'error': str(e),
                'signal': 'NEUTRAL',
                'strength': 0,
                'confidence': 0
            }
    
    # Helper methods
    def _detect_doji(self, df: pd.DataFrame) -> Dict[str, Any]:
        """كشف نمط الدوجي"""
        last_candle = df.iloc[-1]
        body_size = abs(last_candle['close'] - last_candle['open'])
        candle_range = last_candle['high'] - last_candle['low']
        
        if candle_range == 0:
            return {'detected': False, 'strength': 0}
        
        body_ratio = body_size / candle_range
        is_doji = body_ratio < 0.1
        
        return {
            'detected': is_doji,
            'strength': 1 - body_ratio if is_doji else 0,
            'type': 'reversal_signal'
        }
    
    def _detect_engulfing(self, df: pd.DataFrame) -> Dict[str, Any]:
        """كشف نمط الابتلاع"""
        if len(df) < 2:
            return {'detected': False, 'strength': 0}
        
        prev_candle = df.iloc[-2]
        curr_candle = df.iloc[-1]
        
        # ابتلاع صاعد
        bullish_engulfing = (
            prev_candle['close'] < prev_candle['open'] and  # الشمعة السابقة هابطة
            curr_candle['close'] > curr_candle['open'] and  # الشمعة الحالية صاعدة
            curr_candle['open'] < prev_candle['close'] and  # فتح أقل من إغلاق السابقة
            curr_candle['close'] > prev_candle['open']      # إغلاق أعلى من فتح السابقة
        )
        
        # ابتلاع هابط
        bearish_engulfing = (
            prev_candle['close'] > prev_candle['open'] and  # الشمعة السابقة صاعدة
            curr_candle['close'] < curr_candle['open'] and  # الشمعة الحالية هابطة
            curr_candle['open'] > prev_candle['close'] and  # فتح أعلى من إغلاق السابقة
            curr_candle['close'] < prev_candle['open']      # إغلاق أقل من فتح السابقة
        )
        
        if bullish_engulfing:
            return {'detected': True, 'strength': 0.8, 'type': 'bullish_reversal'}
        elif bearish_engulfing:
            return {'detected': True, 'strength': 0.8, 'type': 'bearish_reversal'}
        else:
            return {'detected': False, 'strength': 0}

    def _detect_pin_bar(self, df: pd.DataFrame) -> Dict[str, Any]:
        """كشف نمط البين بار"""
        last_candle = df.iloc[-1]

        body_size = abs(last_candle['close'] - last_candle['open'])
        upper_shadow = last_candle['high'] - max(last_candle['open'], last_candle['close'])
        lower_shadow = min(last_candle['open'], last_candle['close']) - last_candle['low']
        total_range = last_candle['high'] - last_candle['low']

        if total_range == 0:
            return {'detected': False, 'strength': 0}

        # بين بار صاعد (ذيل سفلي طويل)
        bullish_pin = (
            lower_shadow > body_size * 2 and
            lower_shadow > upper_shadow * 2 and
            lower_shadow > total_range * 0.6
        )

        # بين بار هابط (ذيل علوي طويل)
        bearish_pin = (
            upper_shadow > body_size * 2 and
            upper_shadow > lower_shadow * 2 and
            upper_shadow > total_range * 0.6
        )

        if bullish_pin:
            return {'detected': True, 'strength': 0.7, 'type': 'bullish_reversal'}
        elif bearish_pin:
            return {'detected': True, 'strength': 0.7, 'type': 'bearish_reversal'}
        else:
            return {'detected': False, 'strength': 0}

    def _detect_hammer(self, df: pd.DataFrame) -> Dict[str, Any]:
        """كشف نمط المطرقة"""
        last_candle = df.iloc[-1]

        body_size = abs(last_candle['close'] - last_candle['open'])
        lower_shadow = min(last_candle['open'], last_candle['close']) - last_candle['low']
        upper_shadow = last_candle['high'] - max(last_candle['open'], last_candle['close'])

        # شروط المطرقة
        is_hammer = (
            lower_shadow >= body_size * 2 and
            upper_shadow <= body_size * 0.1 and
            body_size > 0
        )

        if is_hammer:
            return {'detected': True, 'strength': 0.6, 'type': 'bullish_reversal'}
        else:
            return {'detected': False, 'strength': 0}

    def _detect_shooting_star(self, df: pd.DataFrame) -> Dict[str, Any]:
        """كشف نمط النجمة الساقطة"""
        last_candle = df.iloc[-1]

        body_size = abs(last_candle['close'] - last_candle['open'])
        upper_shadow = last_candle['high'] - max(last_candle['open'], last_candle['close'])
        lower_shadow = min(last_candle['open'], last_candle['close']) - last_candle['low']

        # شروط النجمة الساقطة
        is_shooting_star = (
            upper_shadow >= body_size * 2 and
            lower_shadow <= body_size * 0.1 and
            body_size > 0
        )

        if is_shooting_star:
            return {'detected': True, 'strength': 0.6, 'type': 'bearish_reversal'}
        else:
            return {'detected': False, 'strength': 0}

    def _calculate_pattern_strength(self, patterns: Dict[str, Any]) -> float:
        """حساب قوة الأنماط الإجمالية"""
        total_strength = 0
        pattern_count = 0

        for pattern_name, pattern_data in patterns.items():
            if pattern_data.get('detected', False):
                total_strength += pattern_data.get('strength', 0)
                pattern_count += 1

        return total_strength / max(pattern_count, 1)

    def _determine_trend_direction(self, df: pd.DataFrame) -> str:
        """تحديد اتجاه الترند"""
        if len(df) < 10:
            return "NEUTRAL"

        # مقارنة آخر 5 شموع مع 5 شموع قبلها
        recent_avg = df['close'].tail(5).mean()
        previous_avg = df['close'].iloc[-10:-5].mean()

        if recent_avg > previous_avg * 1.001:
            return "UPTREND"
        elif recent_avg < previous_avg * 0.999:
            return "DOWNTREND"
        else:
            return "SIDEWAYS"

    def _analyze_ma_signal(self, price_series: pd.Series, ma_series: pd.Series) -> Dict[str, Any]:
        """تحليل إشارة المتوسط المتحرك"""
        if len(price_series) < 2 or len(ma_series) < 2:
            return {'signal': 'NEUTRAL', 'strength': 0}

        current_price = price_series.iloc[-1]
        current_ma = ma_series.iloc[-1]
        prev_price = price_series.iloc[-2]
        prev_ma = ma_series.iloc[-2]

        # تحديد الإشارة
        if current_price > current_ma and prev_price <= prev_ma:
            return {'signal': 'BULLISH_CROSS', 'strength': 0.7}
        elif current_price < current_ma and prev_price >= prev_ma:
            return {'signal': 'BEARISH_CROSS', 'strength': 0.7}
        elif current_price > current_ma:
            return {'signal': 'ABOVE_MA', 'strength': 0.3}
        elif current_price < current_ma:
            return {'signal': 'BELOW_MA', 'strength': 0.3}
        else:
            return {'signal': 'NEUTRAL', 'strength': 0}

    def _analyze_ma_crossovers(self, df: pd.DataFrame) -> Dict[str, Any]:
        """تحليل تقاطعات المتوسطات المتحركة"""
        crossovers = {}

        # تقاطع EMA5 مع EMA21
        if 'EMA_5' in df.columns and 'EMA_21' in df.columns:
            ema5 = df['EMA_5']
            ema21 = df['EMA_21']

            if len(ema5) >= 2 and len(ema21) >= 2:
                current_diff = ema5.iloc[-1] - ema21.iloc[-1]
                prev_diff = ema5.iloc[-2] - ema21.iloc[-2]

                if current_diff > 0 and prev_diff <= 0:
                    crossovers['EMA5_EMA21'] = {'signal': 'GOLDEN_CROSS', 'strength': 0.8}
                elif current_diff < 0 and prev_diff >= 0:
                    crossovers['EMA5_EMA21'] = {'signal': 'DEATH_CROSS', 'strength': 0.8}
                else:
                    crossovers['EMA5_EMA21'] = {'signal': 'NO_CROSS', 'strength': 0}

        return crossovers

    def _calculate_ma_trend_strength(self, df: pd.DataFrame) -> float:
        """حساب قوة ترند المتوسطات المتحركة"""
        strength = 0
        count = 0

        for col in df.columns:
            if col.startswith('EMA_') or col.startswith('SMA_'):
                if len(df[col]) >= 5:
                    # مقارنة آخر 3 قيم
                    recent_values = df[col].tail(3).values
                    if len(recent_values) == 3:
                        if recent_values[2] > recent_values[1] > recent_values[0]:
                            strength += 1
                        elif recent_values[2] < recent_values[1] < recent_values[0]:
                            strength -= 1
                        count += 1

        return strength / max(count, 1)

    def _calculate_rsi(self, prices: pd.Series, period: int) -> pd.Series:
        """حساب مؤشر RSI"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

    def _analyze_rsi_signal(self, rsi: pd.Series) -> Dict[str, Any]:
        """تحليل إشارة RSI"""
        if len(rsi) < 2:
            return {'signal': 'NEUTRAL', 'strength': 0}

        current_rsi = rsi.iloc[-1]

        if pd.isna(current_rsi):
            return {'signal': 'NEUTRAL', 'strength': 0}

        if current_rsi > 70:
            return {'signal': 'OVERBOUGHT', 'strength': 0.6, 'value': current_rsi}
        elif current_rsi < 30:
            return {'signal': 'OVERSOLD', 'strength': 0.6, 'value': current_rsi}
        elif current_rsi > 50:
            return {'signal': 'BULLISH', 'strength': 0.3, 'value': current_rsi}
        elif current_rsi < 50:
            return {'signal': 'BEARISH', 'strength': 0.3, 'value': current_rsi}
        else:
            return {'signal': 'NEUTRAL', 'strength': 0, 'value': current_rsi}

    def _calculate_macd(self, prices: pd.Series) -> Dict[str, pd.Series]:
        """حساب MACD"""
        ema12 = prices.ewm(span=12).mean()
        ema26 = prices.ewm(span=26).mean()
        macd_line = ema12 - ema26
        signal_line = macd_line.ewm(span=9).mean()
        histogram = macd_line - signal_line

        return {
            'macd': macd_line,
            'signal': signal_line,
            'histogram': histogram
        }

    def _analyze_macd_signal(self, macd_data: Dict[str, pd.Series]) -> Dict[str, Any]:
        """تحليل إشارة MACD"""
        macd = macd_data['macd']
        signal = macd_data['signal']
        histogram = macd_data['histogram']

        if len(macd) < 2 or len(signal) < 2:
            return {'signal': 'NEUTRAL', 'strength': 0}

        current_macd = macd.iloc[-1]
        current_signal = signal.iloc[-1]
        prev_macd = macd.iloc[-2]
        prev_signal = signal.iloc[-2]
        current_hist = histogram.iloc[-1]

        # تقاطع MACD
        if current_macd > current_signal and prev_macd <= prev_signal:
            return {'signal': 'BULLISH_CROSS', 'strength': 0.7}
        elif current_macd < current_signal and prev_macd >= prev_signal:
            return {'signal': 'BEARISH_CROSS', 'strength': 0.7}
        elif current_hist > 0:
            return {'signal': 'BULLISH', 'strength': 0.3}
        elif current_hist < 0:
            return {'signal': 'BEARISH', 'strength': 0.3}
        else:
            return {'signal': 'NEUTRAL', 'strength': 0}

    def _calculate_momentum_strength(self, oscillator_signals: Dict[str, Any]) -> float:
        """حساب قوة الزخم"""
        total_strength = 0
        count = 0

        for signal_name, signal_data in oscillator_signals.items():
            if isinstance(signal_data, dict) and 'strength' in signal_data:
                strength = signal_data['strength']
                signal_type = signal_data.get('signal', '')

                if 'BULLISH' in signal_type or 'OVERSOLD' in signal_type:
                    total_strength += strength
                elif 'BEARISH' in signal_type or 'OVERBOUGHT' in signal_type:
                    total_strength -= strength

                count += 1

        return total_strength / max(count, 1)

    def _analyze_bb_signal(self, price: float, upper: float, lower: float, middle: float) -> Dict[str, Any]:
        """تحليل إشارة بولينجر باندز"""
        if price > upper:
            return {'signal': 'OVERBOUGHT', 'strength': 0.6, 'position': 'above_upper'}
        elif price < lower:
            return {'signal': 'OVERSOLD', 'strength': 0.6, 'position': 'below_lower'}
        elif price > middle:
            return {'signal': 'BULLISH', 'strength': 0.3, 'position': 'above_middle'}
        elif price < middle:
            return {'signal': 'BEARISH', 'strength': 0.3, 'position': 'below_middle'}
        else:
            return {'signal': 'NEUTRAL', 'strength': 0, 'position': 'at_middle'}

    def _calculate_bb_position(self, price: float, upper: float, lower: float) -> float:
        """حساب موقع السعر في بولينجر باندز (0-1)"""
        if upper == lower:
            return 0.5
        return (price - lower) / (upper - lower)

    def _calculate_volatility_level(self, current_std: float, avg_std: float) -> str:
        """تحديد مستوى التقلبات"""
        if current_std > avg_std * 1.5:
            return "HIGH"
        elif current_std < avg_std * 0.5:
            return "LOW"
        else:
            return "NORMAL"

    def _calculate_candle_score(self, candle_analysis: Dict[str, Any]) -> float:
        """حساب نقاط تحليل الشموع"""
        if 'error' in candle_analysis:
            return 0

        patterns = candle_analysis.get('patterns', {})
        pattern_strength = candle_analysis.get('pattern_strength', 0)
        trend_direction = candle_analysis.get('trend_direction', 'NEUTRAL')

        score = pattern_strength

        # إضافة نقاط للترند
        if trend_direction == 'UPTREND':
            score += 0.2
        elif trend_direction == 'DOWNTREND':
            score -= 0.2

        return max(-1, min(1, score))

    def _calculate_ma_score(self, ma_analysis: Dict[str, Any]) -> float:
        """حساب نقاط المتوسطات المتحركة"""
        if 'error' in ma_analysis:
            return 0

        score = 0

        # نقاط إشارات SMA
        sma_signals = ma_analysis.get('sma_signals', {})
        for signal_data in sma_signals.values():
            if 'BULLISH' in signal_data.get('signal', ''):
                score += signal_data.get('strength', 0)
            elif 'BEARISH' in signal_data.get('signal', ''):
                score -= signal_data.get('strength', 0)

        # نقاط إشارات EMA
        ema_signals = ma_analysis.get('ema_signals', {})
        for signal_data in ema_signals.values():
            if 'BULLISH' in signal_data.get('signal', ''):
                score += signal_data.get('strength', 0)
            elif 'BEARISH' in signal_data.get('signal', ''):
                score -= signal_data.get('strength', 0)

        # نقاط التقاطعات
        crossover_signals = ma_analysis.get('crossover_signals', {})
        for signal_data in crossover_signals.values():
            if signal_data.get('signal') == 'GOLDEN_CROSS':
                score += signal_data.get('strength', 0)
            elif signal_data.get('signal') == 'DEATH_CROSS':
                score -= signal_data.get('strength', 0)

        return max(-1, min(1, score / 3))  # تطبيع النتيجة

    def _calculate_oscillator_score(self, oscillator_analysis: Dict[str, Any]) -> float:
        """حساب نقاط المذبذبات"""
        if 'error' in oscillator_analysis:
            return 0

        score = 0
        oscillator_signals = oscillator_analysis.get('oscillator_signals', {})

        for signal_data in oscillator_signals.values():
            if isinstance(signal_data, dict):
                signal_type = signal_data.get('signal', '')
                strength = signal_data.get('strength', 0)

                if 'BULLISH' in signal_type or 'OVERSOLD' in signal_type:
                    score += strength
                elif 'BEARISH' in signal_type or 'OVERBOUGHT' in signal_type:
                    score -= strength

        momentum_strength = oscillator_analysis.get('momentum_strength', 0)
        score += momentum_strength

        return max(-1, min(1, score / 2))

    def _calculate_bb_score(self, bb_analysis: Dict[str, Any]) -> float:
        """حساب نقاط بولينجر باندز"""
        if 'error' in bb_analysis:
            return 0

        bb_signal = bb_analysis.get('bb_signal', {})
        signal_type = bb_signal.get('signal', '')
        strength = bb_signal.get('strength', 0)

        score = 0
        if signal_type == 'OVERSOLD':
            score = strength  # إشارة شراء
        elif signal_type == 'OVERBOUGHT':
            score = -strength  # إشارة بيع
        elif signal_type == 'BULLISH':
            score = strength * 0.5
        elif signal_type == 'BEARISH':
            score = -strength * 0.5

        # تعديل النقاط حسب مستوى التقلبات
        volatility_level = bb_analysis.get('volatility_level', 'NORMAL')
        if volatility_level == 'HIGH':
            score *= 0.8  # تقليل الثقة في التقلبات العالية
        elif volatility_level == 'LOW':
            score *= 1.2  # زيادة الثقة في التقلبات المنخفضة

        return max(-1, min(1, score))

    def _suggest_expiry_time(self, strength: float, confidence: float, bb_analysis: Dict[str, Any]) -> int:
        """اقتراح زمن انتهاء الصفقة بناءً على التحليل الفني"""

        # تحديد مستوى التقلبات
        volatility_level = bb_analysis.get('volatility_level', 'NORMAL')

        # إشارة قوية جداً
        if confidence >= 90 and strength >= 0.8:
            if volatility_level == 'HIGH':
                return 1  # دقيقة واحدة للتقلبات العالية
            else:
                return 2  # دقيقتان للتقلبات العادية

        # إشارة قوية
        elif confidence >= 80 and strength >= 0.6:
            if volatility_level == 'HIGH':
                return 2  # دقيقتان
            else:
                return 3  # ثلاث دقائق

        # إشارة متوسطة
        elif confidence >= 70 and strength >= 0.4:
            if volatility_level == 'HIGH':
                return 3  # ثلاث دقائق
            else:
                return 5  # خمس دقائق

        # إشارة ضعيفة
        else:
            return 5  # خمس دقائق كحد أقصى
