"""
Trading Connector - موصل التداول
يستخدم الاتصال الموجود من main.py للتداول الفعلي
"""

import asyncio
import json
import os
import sys
from datetime import datetime
from typing import Dict, List, Any, Optional
import logging

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from pyquotex import Quotex
from pyquotex.constants import codes

logger = logging.getLogger(__name__)

class TradingConnector:
    """موصل التداول الفعلي مع منصة Quotex"""
    
    def __init__(self):
        """تهيئة موصل التداول"""
        self.client = None
        self.is_connected = False
        self.account_type = "PRACTICE"  # PRACTICE أو REAL
        self.account_balance = 0
        self.available_pairs = []
        
        logger.info("🔌 Trading Connector initialized")
    
    async def connect(self, email: str, password: str) -> Dict[str, Any]:
        """الاتصال بمنصة Quotex"""
        try:
            logger.info("🔗 Connecting to Quotex platform...")
            
            self.client = Quotex(email=email, password=password)
            
            # محاولة الاتصال
            check_connect, reason = await self.client.connect()
            
            if check_connect:
                self.is_connected = True
                logger.info("✅ Successfully connected to Quotex")
                
                # جلب معلومات الحساب
                await self._load_account_info()
                
                return {
                    'status': 'SUCCESS',
                    'connected': True,
                    'account_type': self.account_type,
                    'balance': self.account_balance,
                    'message': 'Connected successfully'
                }
            else:
                logger.error(f"❌ Failed to connect: {reason}")
                return {
                    'status': 'ERROR',
                    'connected': False,
                    'error': reason
                }
                
        except Exception as e:
            logger.error(f"Error connecting to Quotex: {e}")
            return {
                'status': 'ERROR',
                'connected': False,
                'error': str(e)
            }
    
    async def _load_account_info(self):
        """تحميل معلومات الحساب"""
        try:
            if not self.client:
                return
            
            # جلب نوع الحساب والرصيد
            # هذا يعتمد على API المتاح في pyquotex
            # سنحتاج للتحقق من الطرق المتاحة
            
            # افتراضياً نبدأ بالحساب التجريبي
            self.account_type = "PRACTICE"
            
            # محاولة جلب الرصيد
            try:
                balance_info = await self.client.get_balance()
                if balance_info:
                    self.account_balance = balance_info
                else:
                    self.account_balance = 10000  # رصيد افتراضي للحساب التجريبي
            except:
                self.account_balance = 10000  # رصيد افتراضي
            
            logger.info(f"💰 Account loaded - Type: {self.account_type}, Balance: {self.account_balance}")
            
        except Exception as e:
            logger.error(f"Error loading account info: {e}")
            self.account_balance = 10000  # رصيد افتراضي في حالة الخطأ
    
    async def switch_account_type(self, account_type: str) -> Dict[str, Any]:
        """تبديل نوع الحساب"""
        try:
            if not self.is_connected:
                return {'status': 'ERROR', 'error': 'Not connected'}
            
            if account_type not in ['PRACTICE', 'REAL']:
                return {'status': 'ERROR', 'error': 'Invalid account type'}
            
            # تبديل نوع الحساب
            if account_type == 'PRACTICE':
                # التبديل للحساب التجريبي
                await self.client.change_account("PRACTICE")
                self.account_type = "PRACTICE"
            else:
                # التبديل للحساب الحقيقي
                await self.client.change_account("REAL")
                self.account_type = "REAL"
            
            # تحديث معلومات الحساب
            await self._load_account_info()
            
            logger.info(f"🔄 Account switched to {account_type}")
            
            return {
                'status': 'SUCCESS',
                'account_type': self.account_type,
                'balance': self.account_balance
            }
            
        except Exception as e:
            logger.error(f"Error switching account: {e}")
            return {'status': 'ERROR', 'error': str(e)}
    
    async def get_account_info(self) -> Dict[str, Any]:
        """الحصول على معلومات الحساب"""
        try:
            if not self.is_connected:
                return {'status': 'ERROR', 'error': 'Not connected'}
            
            # تحديث الرصيد
            await self._load_account_info()
            
            return {
                'status': 'SUCCESS',
                'account_type': self.account_type,
                'balance': self.account_balance,
                'connected': self.is_connected
            }
            
        except Exception as e:
            logger.error(f"Error getting account info: {e}")
            return {'status': 'ERROR', 'error': str(e)}
    
    async def get_available_pairs(self) -> List[Dict[str, Any]]:
        """الحصول على أزواج العملات المتاحة مع نسب الربح من الملف المحفوظ"""
        try:
            # أولاً، محاولة جلب البيانات من ملف pairs.json
            pairs_data = self._load_pairs_from_file()

            if pairs_data:
                logger.info(f"✅ Loaded {len(pairs_data)} pairs from local file")
                return pairs_data

            # إذا لم تكن متاحة، استخدام قائمة افتراضية
            logger.warning("⚠️ No pairs file found, using default pairs")
            return self._get_default_pairs()

        except Exception as e:
            logger.error(f"Error getting available pairs: {e}")
            return self._get_default_pairs()

    def _load_pairs_from_file(self) -> List[Dict[str, Any]]:
        """تحميل الأزواج من ملف pairs.json"""
        try:
            pairs_file = "data/pairs.json"

            if not os.path.exists(pairs_file):
                # محاولة تحميل من currency_pairs.json
                pairs_file = "data/currency_pairs.json"

            if not os.path.exists(pairs_file):
                return []

            with open(pairs_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            pairs_data = []

            # إذا كان الملف من نوع currency_pairs.json
            if 'pairs' in data:
                for pair_name, pair_info in data['pairs'].items():
                    pairs_data.append({
                        'symbol': pair_name,
                        'payout_percentage': pair_info.get('profit', 80),
                        'available': pair_info.get('is_open', True),
                        'pair_type': pair_info.get('pair_type', 'Regular'),
                        'last_updated': pair_info.get('last_updated', datetime.now().isoformat())
                    })

            # إذا كان الملف من نوع pairs.json البسيط
            elif isinstance(data, list):
                for pair_info in data:
                    pairs_data.append({
                        'symbol': pair_info.get('symbol', pair_info.get('name', 'UNKNOWN')),
                        'payout_percentage': pair_info.get('payout_percentage', pair_info.get('profit', 80)),
                        'available': pair_info.get('available', pair_info.get('is_open', True)),
                        'last_updated': pair_info.get('last_updated', datetime.now().isoformat())
                    })

            # ترتيب حسب نسبة الربح
            pairs_data.sort(key=lambda x: x['payout_percentage'], reverse=True)

            # إرجاع أفضل 20 زوج متاح
            available_pairs = [pair for pair in pairs_data if pair['available']]
            return available_pairs[:20]

        except Exception as e:
            logger.error(f"Error loading pairs from file: {e}")
            return []

    def _get_default_pairs(self) -> List[Dict[str, Any]]:
        """الحصول على قائمة افتراضية من الأزواج"""
        try:
            # قائمة الأزواج الشائعة مع نسب ربح افتراضية
            default_pairs = [
                {"symbol": "EURUSD", "payout_percentage": 85},
                {"symbol": "GBPUSD", "payout_percentage": 84},
                {"symbol": "USDJPY", "payout_percentage": 83},
                {"symbol": "AUDUSD", "payout_percentage": 82},
                {"symbol": "USDCAD", "payout_percentage": 81},
                {"symbol": "NZDUSD", "payout_percentage": 80},
                {"symbol": "USDCHF", "payout_percentage": 80},
                {"symbol": "EURJPY", "payout_percentage": 84},
                {"symbol": "GBPJPY", "payout_percentage": 83},
                {"symbol": "AUDJPY", "payout_percentage": 82},
                {"symbol": "EURGBP", "payout_percentage": 85},
                {"symbol": "EURAUD", "payout_percentage": 81},
                {"symbol": "GBPAUD", "payout_percentage": 80},
                {"symbol": "AUDCAD", "payout_percentage": 79},
                {"symbol": "CADJPY", "payout_percentage": 78},
                {"symbol": "CHFJPY", "payout_percentage": 77},
                {"symbol": "EURNZD", "payout_percentage": 76},
                {"symbol": "GBPNZD", "payout_percentage": 75},
                {"symbol": "AUDNZD", "payout_percentage": 74},
                {"symbol": "NZDJPY", "payout_percentage": 73}
            ]

            # إضافة معلومات إضافية
            for pair in default_pairs:
                pair.update({
                    'available': True,
                    'last_updated': datetime.now().isoformat()
                })

            return default_pairs

        except Exception as e:
            logger.error(f"Error creating default pairs: {e}")
            return []
    
    async def _get_pair_payout(self, pair: str) -> float:
        """الحصول على نسبة الربح لزوج معين"""
        try:
            # هذا يعتمد على API المتاح في pyquotex
            # قد نحتاج لاستخدام طريقة مختلفة
            
            # محاولة جلب معلومات الزوج
            if hasattr(self.client, 'get_payout'):
                payout = await self.client.get_payout(pair)
                return payout if payout else 80.0
            else:
                # نسبة افتراضية
                return 80.0 + (hash(pair) % 20)  # نسبة عشوائية بين 80-99
                
        except Exception as e:
            logger.warning(f"Could not get payout for {pair}: {e}")
            return 80.0
    
    async def place_trade(self, pair: str, direction: str, amount: float, 
                         duration: int) -> Dict[str, Any]:
        """تنفيذ صفقة"""
        try:
            if not self.is_connected:
                return {'status': 'ERROR', 'error': 'Not connected'}
            
            logger.info(f"📈 Placing trade: {pair} {direction} ${amount} {duration}min")
            
            # تحويل الاتجاه
            action = "call" if direction.upper() == "CALL" else "put"
            
            # التحقق من أن الزوج متاح
            asset_name, asset_data = await self.client.get_available_asset(pair, force_open=True)

            if not asset_data or len(asset_data) < 3 or not asset_data[2]:
                logger.error(f"Asset {pair} is not available for trading")
                return {'status': 'ERROR', 'error': f'Asset {pair} is not available'}

            # تنفيذ الصفقة (تقريب المبلغ لرقمين عشريين)
            rounded_amount = round(amount, 2)
            status, trade_result = await self.client.buy(
                amount=rounded_amount,
                asset=asset_name,
                direction=action,
                duration=duration * 60,  # تحويل إلى ثواني
                time_mode="TIMER"
            )

            if status and trade_result:
                trade_id = trade_result.get('id') if isinstance(trade_result, dict) else None

                if trade_id:
                    logger.info(f"✅ Trade placed successfully - ID: {trade_id}")

                    return {
                        'status': 'SUCCESS',
                        'trade_id': trade_id,
                        'pair': pair,
                        'direction': direction,
                        'amount': rounded_amount,
                        'duration': duration,
                        'timestamp': datetime.now().isoformat()
                    }
                else:
                    logger.error("❌ Trade executed but no trade ID received")
                    return {'status': 'ERROR', 'error': 'Trade executed but no trade ID received'}
            else:
                error_msg = str(trade_result) if trade_result else 'Trade execution failed'
                logger.error(f"❌ Trade failed: {error_msg}")
                return {'status': 'ERROR', 'error': error_msg}
                
        except Exception as e:
            logger.error(f"Error placing trade: {e}")
            return {'status': 'ERROR', 'error': str(e)}
    
    async def check_trade_result(self, trade_id: str) -> Dict[str, Any]:
        """فحص نتيجة الصفقة"""
        try:
            if not self.is_connected:
                return {'status': 'ERROR', 'error': 'Not connected'}

            if not trade_id:
                return {'status': 'ERROR', 'error': 'No trade ID provided'}

            logger.info(f"🔍 Checking trade result for ID: {trade_id}")

            # محاولة فحص نتيجة الصفقة باستخدام check_win
            try:
                win_result = await self.client.check_win(trade_id)
                profit = self.client.get_profit()

                if win_result:
                    logger.info(f"✅ Trade {trade_id} WON - Profit: ${profit:.2f}")
                    return {
                        'status': 'SUCCESS',
                        'trade_id': trade_id,
                        'result': 'WIN',
                        'profit': profit
                    }
                else:
                    logger.info(f"❌ Trade {trade_id} LOST - Loss: ${abs(profit):.2f}")
                    return {
                        'status': 'SUCCESS',
                        'trade_id': trade_id,
                        'result': 'LOSS',
                        'profit': profit
                    }
            except Exception as check_error:
                logger.warning(f"Could not check trade result: {check_error}")
                return {
                    'status': 'ERROR',
                    'error': f'Trade result check failed: {str(check_error)}',
                    'trade_id': trade_id
                }

        except Exception as e:
            logger.error(f"Error checking trade result: {e}")
            return {'status': 'ERROR', 'error': str(e)}
    
    async def disconnect(self):
        """قطع الاتصال"""
        try:
            if self.client and self.is_connected:
                await self.client.close()
                self.is_connected = False
                logger.info("🔌 Disconnected from Quotex")
        except Exception as e:
            logger.error(f"Error disconnecting: {e}")
    
    def is_market_open(self) -> bool:
        """فحص إذا كان السوق مفتوحاً"""
        # فحص بسيط لأوقات السوق
        current_hour = datetime.now().hour
        
        # السوق مفتوح من الاثنين إلى الجمعة، 24 ساعة
        # هذا مبسط ويمكن تحسينه
        weekday = datetime.now().weekday()
        
        # 0 = الاثنين، 6 = الأحد
        if weekday >= 5:  # السبت والأحد
            return False
        
        return True
