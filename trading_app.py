#!/usr/bin/env python3
"""
Trading Application - تطبيق التداول
التطبيق الرئيسي للتداول الآلي بالخيارات الثنائية
"""

import asyncio
import sys
import os
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pyquotex.stable_api import Quotex
from pyquotex.config import credentials
from src.risk_management.risk_manager import RiskManager
from src.strategy.decision_maker import StrategyDecisionMaker
from src.trading.auto_trader import AutoTrader

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# إخفاء رسائل websocket المزعجة
logging.getLogger('websocket').setLevel(logging.WARNING)
logging.getLogger('pyquotex.ws.client').setLevel(logging.WARNING)

class TradingApp:
    """تطبيق التداول الرئيسي"""

    def __init__(self):
        """تهيئة التطبيق"""
        self.client = None
        self.risk_manager = RiskManager()
        self.strategy = StrategyDecisionMaker()
        self.is_connected = False
        self.current_account_info = {}
        self.available_pairs = []
        self.active_trades = {}  # تتبع الصفقات النشطة
        self.session_data = {
            'trades': [],
            'total_trades': 0,
            'wins': 0,
            'losses': 0,
            'total_profit': 0
        }

        print("🚀 Binary Options Auto Trading System")
        print("="*60)
    
    async def run(self):
        """تشغيل التطبيق الرئيسي"""
        try:
            # الاتصال بالمنصة
            if not await self._connect_to_platform():
                return
            
            # القائمة الرئيسية
            while True:
                choice = await self._show_main_menu()
                
                if choice == '1':
                    await self._switch_account_type()
                elif choice == '2':
                    await self._start_auto_trading()
                elif choice == '3':
                    await self._manual_trading()
                elif choice == '4':
                    await self._repeat_last_session()
                elif choice == '5':
                    await self._view_session_history()
                elif choice == '6':
                    await self._view_account_info()
                elif choice == '7':
                    print("👋 Goodbye!")
                    break
                else:
                    print("❌ Invalid choice. Please try again.")
                
                input("\nPress Enter to continue...")
                
        except KeyboardInterrupt:
            print("\n🛑 Application stopped by user")
        except Exception as e:
            print(f"❌ Application error: {e}")
        finally:
            await self._cleanup()
    
    async def _connect_to_platform(self) -> bool:
        """الاتصال بمنصة التداول"""
        try:
            print("\n🔗 Connecting to Quotex Platform")
            print("-" * 40)

            # الحصول على بيانات الاعتماد من ملف الإعدادات
            try:
                email, password = credentials()
            except Exception as e:
                print(f"❌ Could not load credentials: {e}")
                return False

            # إنشاء عميل Quotex
            self.client = Quotex(
                email=email,
                password=password,
                lang="en"
            )

            print("⏳ Connecting to Quotex...")

            # محاولة الاتصال
            check_connect, message = await self.client.connect()

            if check_connect:
                self.is_connected = True

                # جلب معلومات الحساب
                await self._load_account_info()

                print("✅ Connected successfully!")
                print(f"Account Type: {self.current_account_info.get('account_type', 'PRACTICE')}")
                print(f"Balance: ${self.current_account_info.get('balance', 0):.2f}")

                return True
            else:
                print(f"❌ Connection failed: {message}")
                return False

        except Exception as e:
            logger.error(f"Connection error: {e}")
            print(f"❌ Connection error: {e}")
            return False

    async def _load_account_info(self):
        """تحميل معلومات الحساب الفعلية"""
        try:
            if not self.client:
                return

            # جلب معلومات الملف الشخصي
            profile = await self.client.get_profile()

            # جلب الرصيد الحالي
            balance = await self.client.get_balance()

            # تحديد نوع الحساب (افتراضياً PRACTICE)
            account_type = "PRACTICE" if self.client.account_is_demo else "REAL"

            self.current_account_info = {
                'account_type': account_type,
                'balance': balance,
                'connected': True,
                'profile': {
                    'name': profile.nick_name,
                    'demo_balance': profile.demo_balance,
                    'live_balance': profile.live_balance,
                    'currency': profile.currency_symbol
                }
            }

            logger.info(f"Account loaded - Type: {account_type}, Balance: {balance}")

        except Exception as e:
            logger.error(f"Error loading account info: {e}")
            # استخدام قيم افتراضية في حالة الخطأ
            self.current_account_info = {
                'account_type': 'PRACTICE',
                'balance': 0,
                'connected': True
            }

    async def _get_available_pairs(self) -> List[Dict[str, Any]]:
        """جلب الأزواج المتاحة من ملف currency_pairs.json"""
        try:
            pairs_file = "data/currency_pairs.json"
            if not os.path.exists(pairs_file):
                logger.error("Currency pairs file not found")
                return []

            with open(pairs_file, 'r', encoding='utf-8') as f:
                pairs_data = json.load(f)

            # تحويل البيانات إلى قائمة مرتبة حسب الربح
            pairs_list = []
            for symbol, data in pairs_data.get('pairs', {}).items():
                if data.get('is_open', False):  # فقط الأزواج المفتوحة
                    pairs_list.append({
                        'symbol': symbol,
                        'display_name': data.get('display_name', symbol),
                        'payout': data.get('profit', 80),
                        'pair_type': data.get('pair_type', 'Regular'),
                        'is_open': data.get('is_open', False)
                    })

            # ترتيب حسب الربح (من الأعلى للأقل)
            pairs_list.sort(key=lambda x: x['payout'], reverse=True)

            logger.info(f"Loaded {len(pairs_list)} available pairs")
            return pairs_list

        except Exception as e:
            logger.error(f"Error loading pairs: {e}")
            return []

    def _get_candles_from_saved_data(self, asset: str, count: int = 100) -> List[Dict[str, Any]]:
        """جلب الشموع من البيانات المحفوظة"""
        try:
            # قراءة البيانات من الملف المحفوظ
            historical_file = f"data/historical/{asset}.json"

            if not os.path.exists(historical_file):
                logger.warning(f"Historical data file not found for {asset}")
                return []

            with open(historical_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            candles = data.get('candles', [])

            if not candles:
                logger.warning(f"No candles found in historical data for {asset}")
                return []

            # أخذ آخر count شمعة
            recent_candles = candles[-count:] if len(candles) > count else candles

            logger.info(f"Loaded {len(recent_candles)} candles from saved data for {asset}")
            return recent_candles

        except Exception as e:
            logger.error(f"Error loading saved candles for {asset}: {e}")
            return []

    def _get_live_candle_data(self, asset: str) -> Optional[Dict[str, Any]]:
        """جلب بيانات الشمعة الحية الحالية"""
        try:
            # محاولة قراءة البيانات الحية من الملف
            live_file = f"data/live/{asset}_live.json"

            if not os.path.exists(live_file):
                # إذا لم يكن هناك ملف بيانات حية، استخدم آخر شمعة من البيانات التاريخية
                logger.debug(f"No live data file for {asset}, using latest historical candle")
                return None

            with open(live_file, 'r', encoding='utf-8') as f:
                live_data = json.load(f)

            # الحصول على الشمعة الحية الحالية
            current_candle = live_data.get('current_candle')

            if current_candle:
                logger.info(f"Loaded live candle data for {asset}")
                return current_candle

            return None

        except Exception as e:
            logger.debug(f"No live candle data available for {asset}: {e}")
            return None

    async def _execute_trade(self, asset: str, amount: float, direction: str, duration: int = 60) -> Dict[str, Any]:
        """تنفيذ صفقة فعلية"""
        try:
            if not self.client or not self.is_connected:
                return {'status': 'ERROR', 'error': 'Not connected'}

            # Executing trade silently

            # التحقق من أن الزوج متاح
            asset_name, asset_data = await self.client.get_available_asset(asset, force_open=True)

            if not asset_data or len(asset_data) < 3 or not asset_data[2]:
                return {'status': 'ERROR', 'error': f'Asset {asset} is not available'}

            # تنفيذ الصفقة (تقريب المبلغ لرقمين عشريين)
            rounded_amount = round(amount, 2)
            status, buy_info = await self.client.buy(
                rounded_amount, asset_name, direction, duration, time_mode="TIMER"
            )

            if status:
                trade_id = buy_info.get('id') if isinstance(buy_info, dict) else None
                if trade_id:
                    return {
                        'status': 'SUCCESS',
                        'trade_id': trade_id,
                        'amount': rounded_amount,
                        'asset': asset,
                        'direction': direction,
                        'duration': duration
                    }
                else:
                    return {'status': 'ERROR', 'error': 'Trade executed but no trade ID received'}
            else:
                error_msg = str(buy_info) if buy_info else 'Unknown error'
                return {'status': 'ERROR', 'error': error_msg}

        except Exception as e:
            logger.error(f"Error executing trade: {e}")
            return {'status': 'ERROR', 'error': str(e)}

    async def _check_trade_result(self, trade_id: str) -> Dict[str, Any]:
        """فحص نتيجة الصفقة"""
        try:
            if not self.client:
                return {'status': 'ERROR', 'error': 'Not connected'}

            if not trade_id:
                return {'status': 'ERROR', 'error': 'No trade ID provided'}

            print(f"🔍 Checking result for trade ID: {trade_id}")

            # محاولة فحص نتيجة الصفقة
            try:
                win_result = await self.client.check_win(trade_id)
                profit = self.client.get_profit()

                if win_result:
                    print(f"✅ Trade WON! Profit: ${profit:.2f}")
                    return {
                        'status': 'WIN',
                        'profit': profit,
                        'trade_id': trade_id
                    }
                else:
                    print(f"❌ Trade LOST! Loss: ${abs(profit):.2f}")
                    return {
                        'status': 'LOSS',
                        'loss': abs(profit),
                        'trade_id': trade_id
                    }
            except Exception as check_error:
                logger.warning(f"Could not determine trade result: {check_error}")
                print(f"⚠️ Could not determine trade result for ID {trade_id}")
                return {
                    'status': 'UNKNOWN',
                    'error': f'Result check failed: {str(check_error)}',
                    'trade_id': trade_id
                }

        except Exception as e:
            logger.error(f"Error checking trade result: {e}")
            return {'status': 'ERROR', 'error': str(e)}

    async def _monitor_trade(self, trade_id: str, trade_info: Dict[str, Any]):
        """مراقبة الصفقة وحفظ النتيجة - انتظار كامل حتى انتهاء الصفقة"""
        try:
            if not trade_id:
                print("❌ Cannot monitor trade: No trade ID provided")
                return

            # انتظار انتهاء الصفقة
            duration = trade_info.get('duration', 60)
            print(f"⏰ Monitoring trade {trade_id} for {duration} seconds...")

            # انتظار مع عرض العد التنازلي كل 30 ثانية
            remaining = duration
            while remaining > 0:
                if remaining <= 30:
                    print(f"⏳ {remaining} seconds remaining...")
                    await asyncio.sleep(remaining)
                    break
                else:
                    print(f"⏳ {remaining} seconds remaining...")
                    await asyncio.sleep(30)
                    remaining -= 30

            # انتظار إضافي للتأكد من معالجة النتيجة
            print("⏳ Waiting for result processing...")
            await asyncio.sleep(10)

            print("🔍 Checking trade result...")

            # فحص نتيجة الصفقة
            result = await self._check_trade_result(trade_id)

            # تحديث إحصائيات الجلسة
            trade_record = {
                'trade_id': trade_id,
                'pair': trade_info.get('asset'),
                'amount': trade_info.get('amount'),
                'direction': trade_info.get('direction'),
                'duration': trade_info.get('duration'),
                'timestamp': datetime.now().isoformat(),
                'result': result
            }

            self.session_data['trades'].append(trade_record)
            self.session_data['total_trades'] += 1

            if result['status'] == 'WIN':
                self.session_data['wins'] += 1
                profit = result.get('profit', 0)
                self.session_data['total_profit'] += profit
                print(f"🎉 WIN! Profit: ${profit:.2f}")
            elif result['status'] == 'LOSS':
                self.session_data['losses'] += 1
                loss = result.get('loss', 0)
                self.session_data['total_profit'] -= abs(loss)
                print(f"💔 LOSS! Loss: ${abs(loss):.2f}")
            else:
                print(f"❓ Unknown result: {result}")

            # عرض الإحصائيات المحدثة
            await self._update_account_balance()
            self._display_session_stats()

            # حفظ بيانات الجلسة
            await self._save_session_data()

            print("✅ Trade monitoring completed, ready for next trade")

        except Exception as e:
            logger.error(f"Error monitoring trade: {e}")
            print(f"❌ Error monitoring trade: {e}")

    async def _update_account_balance(self):
        """تحديث رصيد الحساب"""
        try:
            await self._load_account_info()
        except Exception as e:
            logger.error(f"Error updating balance: {e}")

    def _display_session_stats(self):
        """عرض إحصائيات الجلسة الحالية"""
        total = self.session_data['total_trades']
        wins = self.session_data['wins']
        losses = self.session_data['losses']
        profit = self.session_data['total_profit']
        win_rate = (wins / total * 100) if total > 0 else 0

        print(f"\n📊 Session Statistics:")
        print(f"Total Trades: {total}")
        print(f"Wins: {wins} | Losses: {losses}")
        print(f"Win Rate: {win_rate:.1f}%")
        print(f"Total Profit: ${profit:.2f}")
        print(f"Current Balance: ${self.current_account_info.get('balance', 0):.2f}")

    async def _save_session_data(self):
        """حفظ بيانات الجلسة"""
        try:
            sessions_dir = "data/sessions"
            os.makedirs(sessions_dir, exist_ok=True)

            session_file = os.path.join(sessions_dir, f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")

            session_data = {
                'start_time': datetime.now().isoformat(),
                'account_type': self.current_account_info.get('account_type', 'Unknown'),
                'trades_count': self.session_data['total_trades'],
                'wins': self.session_data['wins'],
                'losses': self.session_data['losses'],
                'total_profit': self.session_data['total_profit'],
                'trades': self.session_data['trades']
            }

            with open(session_file, 'w', encoding='utf-8') as f:
                json.dump(session_data, f, indent=2, ensure_ascii=False)

            logger.info(f"Session data saved to {session_file}")

        except Exception as e:
            logger.error(f"Error saving session data: {e}")

    async def _show_main_menu(self) -> str:
        """عرض القائمة الرئيسية"""
        print("\n" + "="*60)
        print("📊 MAIN MENU")
        print("="*60)
        
        # عرض معلومات الحساب الحالي
        if self.current_account_info:
            account_type = self.current_account_info.get('account_type', 'Unknown')
            balance = self.current_account_info.get('balance', 0)
            print(f"Current Account: {account_type} | Balance: ${balance:.2f}")
            print("-" * 60)
        
        print("1. Switch Account Type (Practice/Real)")
        print("2. Start Auto Trading")
        print("3. Manual Trading")
        print("4. Repeat Last Session")
        print("5. View Session History")
        print("6. View Account Info")
        print("7. Exit")
        print("="*60)
        
        return input("Select option (1-7): ").strip()
    
    async def _switch_account_type(self):
        """تبديل نوع الحساب"""
        try:
            if not self.client or not self.is_connected:
                print("❌ Not connected to platform")
                return

            print("\n🔄 Switch Account Type")
            print("-" * 30)
            print("1. Practice Account")
            print("2. Real Account")

            choice = input("Select account type (1-2): ").strip()

            if choice == '1':
                account_mode = 'PRACTICE'
            elif choice == '2':
                account_mode = 'REAL'

                # تأكيد للحساب الحقيقي
                confirm = input("⚠️  Are you sure you want to switch to REAL account? (yes/no): ").strip().lower()
                if confirm != 'yes':
                    print("❌ Cancelled")
                    return
            else:
                print("❌ Invalid choice")
                return

            print(f"⏳ Switching to {account_mode} account...")

            try:
                # تبديل نوع الحساب باستخدام الطريقة الصحيحة
                self.client.change_account(account_mode)

                # انتظار قصير للتأكد من التبديل
                await asyncio.sleep(2)

                # تحديث معلومات الحساب
                await self._load_account_info()

                print(f"✅ Switched to {account_mode} account")
                print(f"New Balance: ${self.current_account_info.get('balance', 0):.2f}")

            except Exception as e:
                logger.error(f"Error switching account: {e}")
                print(f"❌ Failed to switch account: {e}")

        except Exception as e:
            logger.error(f"Error in switch account: {e}")
            print(f"❌ Error switching account: {e}")
    
    async def _start_auto_trading(self):
        """بدء التداول الآلي"""
        try:
            if not self.client or not self.is_connected:
                print("❌ Not connected to platform")
                return

            print("\n🤖 Auto Trading Setup")
            print("="*40)

            # الحصول على أفضل الأزواج من ملف currency_pairs.json
            print("⏳ Loading available pairs...")
            pairs = await self._get_available_pairs()

            if not pairs:
                print("❌ No pairs available")
                return

            # عرض أفضل 20 زوج
            print("\n📈 Top 20 Pairs by Payout:")
            print("-" * 40)
            for i, pair in enumerate(pairs[:20], 1):
                print(f"{i:2d}. {pair['symbol']} - {pair['payout']:.1f}%")

            # اختيار الزوج
            try:
                pair_choice = int(input("\nSelect pair (1-20): ").strip())
                if 1 <= pair_choice <= len(pairs):
                    selected_pair = pairs[pair_choice - 1]['symbol']
                else:
                    print("❌ Invalid pair selection")
                    return
            except ValueError:
                print("❌ Invalid input")
                return
            
            # إعدادات التداول
            print(f"\n⚙️ Trading Settings for {selected_pair}")
            print("-" * 40)
            
            # مبلغ الصفقة
            current_balance = self.current_account_info.get('balance', 0)
            recommended_amount = self.risk_manager.get_recommended_trade_amount(current_balance)
            
            print(f"Current Balance: ${current_balance:.2f}")
            print(f"Recommended Amount (2%): ${recommended_amount:.2f}")
            
            amount_input = input(f"Trade Amount (default ${recommended_amount:.2f}): ").strip()
            
            if amount_input:
                try:
                    trade_amount = float(amount_input)
                except ValueError:
                    print("❌ Invalid amount")
                    return
            else:
                trade_amount = recommended_amount
            
            # عدد الصفقات
            try:
                max_trades = int(input("Number of trades in session (default 10): ").strip() or "10")
            except ValueError:
                print("❌ Invalid number")
                return
            
            # تأكيد الإعدادات
            print(f"\n📋 Session Configuration:")
            print(f"Account Type: {self.current_account_info.get('account_type', 'Unknown')}")
            print(f"Balance: ${current_balance:.2f}")
            print(f"Selected Pair: {selected_pair}")
            print(f"Trade Amount: ${trade_amount:.2f}")
            print(f"Max Trades: {max_trades}")
            
            confirm = input("\n✅ Are you sure? (yes/no): ").strip().lower()
            
            if confirm != 'yes':
                print("❌ Cancelled")
                return
            
            # التحقق من أن الزوج متاح للتداول
            print(f"\n⚙️ Setting up trading session for {selected_pair}...")

            # التحقق من حالة الزوج
            asset_name, asset_data = await self.client.get_available_asset(selected_pair, force_open=True)

            if not asset_data or len(asset_data) < 3 or not asset_data[2]:
                print(f"❌ Asset {selected_pair} is closed or not available")
                return

            print(f"✅ Asset {selected_pair} is available for trading")

            # إنشاء نظام التداول الآلي مع العميل الموجود
            auto_trader = AutoTrader(client=self.client)

            # إعداد جلسة التداول
            session_config = {
                'pair': selected_pair,
                'trade_amount': trade_amount,
                'max_trades': max_trades,
                'account_type': self.current_account_info.get('account_type', 'PRACTICE')
            }

            auto_trader.session_config = session_config

            # بدء التداول الآلي الفعلي
            print("🚀 Starting automated trading session...")
            print("⏳ Waiting for strong trading signals...")
            print("Press Ctrl+C to stop")

            trades_executed = 0

            try:
                while trades_executed < max_trades:
                    print(f"\n🔍 Analyzing market for {selected_pair}... ({trades_executed + 1}/{max_trades})")

                    # تحليل السوق باستخدام الاستراتيجية
                    # الاستراتيجية ستقوم بقراءة البيانات من الملفات بنفسها
                    market_analysis = self.strategy.analyze_market(selected_pair)

                    if 'error' in market_analysis:
                        print(f"❌ Analysis error: {market_analysis['error']}")
                        await asyncio.sleep(30)
                        continue

                    # اتخاذ قرار التداول
                    trading_signal = self.strategy.make_trading_decision(market_analysis)

                    if trading_signal['decision'] == 'TRADE':
                        confidence = trading_signal['confidence']
                        direction = trading_signal['direction']
                        expiry_minutes = trading_signal['expiry_minutes']
                        layer_agreement = trading_signal.get('layer_agreement', {})

                        print(f"🎯 Strong signal detected!")
                        print(f"Direction: {direction.upper()}")
                        print(f"Confidence: {confidence:.1f}%")
                        print(f"Expiry: {expiry_minutes} minutes")
                        print(f"Layer Agreement: {layer_agreement.get('agreeing_layers', 0)}/4 layers")

                        # شرط الثقة العالية واتفاق الطبقات (2 طبقات كحد أدنى)
                        if confidence >= 70 and layer_agreement.get('agreeing_layers', 0) >= 2:
                            print("⏳ Executing trade...")

                            # تنفيذ الصفقة باستخدام AutoTrader
                            trade_result = await auto_trader._execute_trade_direct(
                                selected_pair, trade_amount, direction, expiry_minutes * 60
                            )

                            if trade_result['status'] == 'SUCCESS':
                                trades_executed += 1
                                trade_id = trade_result['trade_id']
                                print(f"✅ Trade {trades_executed} executed successfully!")
                                print(f"Trade ID: {trade_id}")
                                print(f"Amount: ${trade_amount}")
                                print(f"Direction: {direction.upper()}")

                                # مراقبة الصفقة وانتظار انتهائها قبل التداول التالي
                                await self._monitor_trade(trade_id, trade_result)

                                # انتظار قصير قبل التداول التالي
                                print("⏳ Waiting before next analysis...")
                                await asyncio.sleep(10)
                            else:
                                error_msg = trade_result.get('error', 'Unknown error')
                                print(f"❌ Trade execution failed!")
                                print(f"Error: {error_msg}")
                                print("⏳ Waiting before retry...")
                                await asyncio.sleep(30)  # انتظار أطول في حالة الخطأ
                        else:
                            print(f"⚠️ Signal not strong enough (Confidence: {confidence:.1f}%, Agreement: {layer_agreement.get('agreeing_layers', 0)}/4), waiting...")
                    else:
                        print("📊 No trading signal, waiting...")

                    # انتظار قصير قبل التحليل التالي
                    await asyncio.sleep(5)  # انتظار 5 ثوانٍ فقط

                print(f"\n🏁 Trading session completed! {trades_executed} trades executed.")
                self._display_session_stats()

            except asyncio.CancelledError:
                print("\n🛑 Trading session cancelled")

        except KeyboardInterrupt:
            print("\n🛑 Trading stopped by user")
        except Exception as e:
            logger.error(f"Error in auto trading: {e}")
            print(f"❌ Error in auto trading: {e}")

    async def _manual_trading(self):
        """التداول اليدوي"""
        try:
            if not self.client or not self.is_connected:
                print("❌ Not connected to platform")
                return

            print("\n📊 Manual Trading")
            print("="*40)

            # الحصول على الأزواج المتاحة
            print("⏳ Loading available pairs...")
            pairs = await self._get_available_pairs()

            if not pairs:
                print("❌ No pairs available")
                return

            # عرض الأزواج
            print("\n📈 Available Pairs:")
            print("-" * 40)
            for i, pair in enumerate(pairs[:20], 1):
                print(f"{i:2d}. {pair['symbol']} - {pair['payout']:.1f}%")

            # اختيار الزوج
            try:
                pair_choice = int(input("\nSelect pair (1-20): ").strip())
                if 1 <= pair_choice <= len(pairs):
                    selected_pair = pairs[pair_choice - 1]['symbol']
                else:
                    print("❌ Invalid pair selection")
                    return
            except ValueError:
                print("❌ Invalid input")
                return

            # مبلغ الصفقة
            current_balance = self.current_account_info.get('balance', 0)
            recommended_amount = self.risk_manager.get_recommended_trade_amount(current_balance)

            print(f"\nCurrent Balance: ${current_balance:.2f}")
            print(f"Recommended Amount (2%): ${recommended_amount:.2f}")

            amount_input = input(f"Trade Amount (default ${recommended_amount:.2f}): ").strip()

            if amount_input:
                try:
                    trade_amount = float(amount_input)
                except ValueError:
                    print("❌ Invalid amount")
                    return
            else:
                trade_amount = recommended_amount

            # نوع الصفقة
            print("\nTrade Direction:")
            print("1. CALL (Buy/Up)")
            print("2. PUT (Sell/Down)")

            direction_choice = input("Select direction (1-2): ").strip()
            if direction_choice == '1':
                direction = 'call'
            elif direction_choice == '2':
                direction = 'put'
            else:
                print("❌ Invalid direction")
                return

            # زمن انتهاء الصفقة
            print("\nExpiry Time:")
            print("1. 1 minute")
            print("2. 2 minutes")
            print("3. 3 minutes")
            print("4. 5 minutes")

            expiry_choice = input("Select expiry time (1-4): ").strip()
            expiry_times = {'1': 60, '2': 120, '3': 180, '4': 300}

            if expiry_choice in expiry_times:
                duration = expiry_times[expiry_choice]
            else:
                print("❌ Invalid expiry time")
                return

            # تأكيد الصفقة
            print(f"\n📋 Trade Confirmation:")
            print(f"Pair: {selected_pair}")
            print(f"Amount: ${trade_amount:.2f}")
            print(f"Direction: {direction.upper()}")
            print(f"Duration: {duration//60} minute(s)")
            print(f"Account: {self.current_account_info.get('account_type', 'Unknown')}")

            confirm = input("\n✅ Execute trade? (yes/no): ").strip().lower()

            if confirm != 'yes':
                print("❌ Trade cancelled")
                return

            # تنفيذ الصفقة
            print("⏳ Executing trade...")

            trade_result = await self._execute_trade(selected_pair, trade_amount, direction, duration)

            if trade_result['status'] == 'SUCCESS':
                trade_id = trade_result['trade_id']
                print(f"✅ Trade executed successfully!")
                print(f"Trade ID: {trade_id}")
                print(f"Amount: ${trade_amount}")
                print(f"Direction: {direction.upper()}")

                # مراقبة الصفقة
                print("⏳ Monitoring trade result...")
                await self._monitor_trade(trade_id, trade_result)

            else:
                error_msg = trade_result.get('error', 'Unknown error')
                print(f"❌ Trade execution failed!")
                print(f"Error details: {error_msg}")

                # اقتراحات للحل
                if "WebSocket error" in error_msg:
                    print("💡 Suggestion: Check internet connection and try again")
                elif "not available" in error_msg:
                    print("💡 Suggestion: Try a different trading pair or check market hours")
                elif "timeout" in error_msg:
                    print("💡 Suggestion: Server may be busy, try again in a few moments")

        except Exception as e:
            logger.error(f"Error in manual trading: {e}")
            print(f"❌ Error in manual trading: {e}")

    async def _repeat_last_session(self):
        """تكرار الجلسة السابقة"""
        try:
            print("\n🔄 Repeat Last Session")
            print("-" * 30)
            
            # البحث عن آخر جلسة
            sessions_dir = "data/sessions"
            if not os.path.exists(sessions_dir):
                print("❌ No previous sessions found")
                return
            
            session_files = [f for f in os.listdir(sessions_dir) if f.startswith('session_') and f.endswith('.json')]
            
            if not session_files:
                print("❌ No previous sessions found")
                return
            
            # أحدث جلسة
            latest_session = max(session_files)
            session_path = os.path.join(sessions_dir, latest_session)
            
            with open(session_path, 'r', encoding='utf-8') as f:
                last_session = json.load(f)
            
            print(f"📋 Last Session Details:")
            print(f"Date: {last_session.get('start_time', 'Unknown')}")
            print(f"Account: {last_session.get('account_type', 'Unknown')}")
            print(f"Trades: {last_session.get('trades_count', 0)}")
            print(f"Wins: {last_session.get('wins', 0)}")
            print(f"Losses: {last_session.get('losses', 0)}")
            
            confirm = input("\n🔄 Repeat this session? (yes/no): ").strip().lower()
            
            if confirm == 'yes':
                print("🚀 Starting repeated session...")
                # هنا يمكن تنفيذ تكرار الجلسة
                print("⚠️  Feature coming soon!")
            else:
                print("❌ Cancelled")
                
        except Exception as e:
            print(f"❌ Error repeating session: {e}")
    
    async def _view_session_history(self):
        """عرض تاريخ الجلسات"""
        try:
            print("\n📊 Session History")
            print("="*50)
            
            sessions_dir = "data/sessions"
            if not os.path.exists(sessions_dir):
                print("❌ No sessions found")
                return
            
            session_files = [f for f in os.listdir(sessions_dir) if f.startswith('session_') and f.endswith('.json')]
            
            if not session_files:
                print("❌ No sessions found")
                return
            
            # ترتيب الجلسات حسب التاريخ
            session_files.sort(reverse=True)
            
            for i, session_file in enumerate(session_files[:10], 1):  # آخر 10 جلسات
                session_path = os.path.join(sessions_dir, session_file)
                
                try:
                    with open(session_path, 'r', encoding='utf-8') as f:
                        session_data = json.load(f)
                    
                    start_time = session_data.get('start_time', 'Unknown')
                    account_type = session_data.get('account_type', 'Unknown')
                    trades = session_data.get('trades_count', 0)
                    wins = session_data.get('wins', 0)
                    losses = session_data.get('losses', 0)
                    win_rate = (wins / trades * 100) if trades > 0 else 0
                    
                    print(f"{i:2d}. {start_time[:19]} | {account_type:8} | "
                          f"Trades: {trades:2d} | Wins: {wins:2d} | Win Rate: {win_rate:5.1f}%")
                    
                except Exception as e:
                    print(f"{i:2d}. Error reading {session_file}: {e}")
            
        except Exception as e:
            print(f"❌ Error viewing session history: {e}")
    
    async def _view_account_info(self):
        """عرض معلومات الحساب"""
        try:
            if not self.client or not self.is_connected:
                print("❌ Not connected to platform")
                return

            print("\n💰 Account Information")
            print("="*40)

            # تحديث معلومات الحساب
            await self._load_account_info()

            print(f"Account Type: {self.current_account_info.get('account_type', 'Unknown')}")
            print(f"Balance: ${self.current_account_info.get('balance', 0):.2f}")
            print(f"Connected: {self.current_account_info.get('connected', False)}")

            # عرض معلومات إضافية إذا كانت متاحة
            profile = self.current_account_info.get('profile', {})
            if profile:
                print(f"Name: {profile.get('name', 'Unknown')}")
                print(f"Demo Balance: ${profile.get('demo_balance', 0):.2f}")
                print(f"Live Balance: ${profile.get('live_balance', 0):.2f}")
                print(f"Currency: {profile.get('currency', 'USD')}")

        except Exception as e:
            logger.error(f"Error getting account info: {e}")
            print(f"❌ Error getting account info: {e}")
    
    def _display_session_results(self, stats: Dict[str, Any]):
        """عرض نتائج الجلسة"""
        print("\n📊 Session Results")
        print("="*40)
        
        total_trades = stats.get('trades_executed', 0)
        wins = stats.get('wins', 0)
        losses = stats.get('losses', 0)
        total_profit = stats.get('total_profit', 0)
        win_rate = (wins / total_trades * 100) if total_trades > 0 else 0
        
        print(f"Total Trades: {total_trades}")
        print(f"Wins: {wins}")
        print(f"Losses: {losses}")
        print(f"Win Rate: {win_rate:.1f}%")
        print(f"Total Profit: ${total_profit:.2f}")
        
        if win_rate >= 80:
            print("🎉 Excellent performance!")
        elif win_rate >= 70:
            print("👍 Good performance!")
        elif win_rate >= 60:
            print("👌 Acceptable performance")
        else:
            print("⚠️  Performance needs improvement")
    
    async def _cleanup(self):
        """تنظيف الموارد"""
        try:
            if self.is_connected and self.client:
                await self.client.close()
                print("🔌 Disconnected from platform")
                self.is_connected = False
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
            print(f"Error during cleanup: {e}")

async def main():
    """الدالة الرئيسية"""
    app = TradingApp()
    await app.run()

if __name__ == "__main__":
    asyncio.run(main())
