#!/usr/bin/env python3
"""
اختبار التحسينات على نظام التداول
"""

import asyncio
import logging
from datetime import datetime
from trading_app import TradingApp

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_trading_improvements():
    """اختبار التحسينات على نظام التداول"""
    print("🧪 Testing Trading System Improvements")
    print("=" * 50)
    
    app = TradingApp()
    
    try:
        # اختبار الاتصال
        print("\n1. Testing Connection...")
        connected = await app.connect()
        
        if not connected:
            print("❌ Connection failed - cannot proceed with tests")
            return
        
        print("✅ Connection successful")
        
        # اختبار تنفيذ صفقة واحدة
        print("\n2. Testing Single Trade Execution...")
        
        # معاملات الاختبار
        test_pair = "AUDCHF_otc"
        test_amount = 10.0
        test_direction = "PUT"
        test_duration = 60  # ثانية
        
        print(f"Test Parameters:")
        print(f"  Pair: {test_pair}")
        print(f"  Amount: ${test_amount}")
        print(f"  Direction: {test_direction}")
        print(f"  Duration: {test_duration}s")
        
        # تنفيذ الصفقة
        trade_result = await app._execute_trade(
            test_pair, test_amount, test_direction, test_duration
        )
        
        print(f"\nTrade Result:")
        print(f"  Status: {trade_result['status']}")
        
        if trade_result['status'] == 'SUCCESS':
            print(f"  Trade ID: {trade_result['trade_id']}")
            print("✅ Trade execution test passed")
            
            # اختبار مراقبة الصفقة
            print("\n3. Testing Trade Monitoring...")
            await app._monitor_trade(trade_result['trade_id'], trade_result)
            print("✅ Trade monitoring test completed")
            
        else:
            print(f"  Error: {trade_result.get('error', 'Unknown')}")
            print("⚠️ Trade execution test failed - this is expected if market is closed")
        
        # اختبار فحص نتيجة الصفقة
        print("\n4. Testing Trade Result Check...")
        if trade_result['status'] == 'SUCCESS':
            result = await app._check_trade_result(trade_result['trade_id'])
            print(f"Result check status: {result['status']}")
            print("✅ Trade result check test completed")
        else:
            print("⏭️ Skipping trade result check (no successful trade)")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        logging.error(f"Test error: {e}")
    
    finally:
        # تنظيف
        try:
            await app.disconnect()
            print("\n🔌 Disconnected successfully")
        except:
            pass
    
    print("\n🏁 Testing completed")

async def test_error_handling():
    """اختبار معالجة الأخطاء"""
    print("\n🧪 Testing Error Handling")
    print("=" * 30)
    
    app = TradingApp()
    
    try:
        # اختبار تنفيذ صفقة بدون اتصال
        print("1. Testing trade without connection...")
        trade_result = await app._execute_trade("EURUSD", 10.0, "CALL", 60)
        
        if trade_result['status'] == 'ERROR':
            print(f"✅ Correctly handled no connection: {trade_result['error']}")
        else:
            print("⚠️ Expected error for no connection")
        
        # اختبار فحص نتيجة صفقة بدون معرف
        print("\n2. Testing trade result check without ID...")
        result = await app._check_trade_result("")
        
        if result['status'] == 'ERROR':
            print(f"✅ Correctly handled empty trade ID: {result['error']}")
        else:
            print("⚠️ Expected error for empty trade ID")
        
    except Exception as e:
        print(f"❌ Error handling test failed: {e}")
    
    print("✅ Error handling tests completed")

if __name__ == "__main__":
    print(f"🚀 Starting Trading System Tests - {datetime.now()}")
    
    # تشغيل الاختبارات
    asyncio.run(test_trading_improvements())
    asyncio.run(test_error_handling())
    
    print(f"\n✅ All tests completed - {datetime.now()}")
