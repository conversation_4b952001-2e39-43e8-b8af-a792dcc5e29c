statsmodels-0.14.4.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
statsmodels-0.14.4.dist-info/LICENSE.txt,sha256=CJchLCSwm6acSRdJDYgoCB1gu0MD7ABF5VOp5HDzjvo,1670
statsmodels-0.14.4.dist-info/METADATA,sha256=3tIJxUMmHrAm4fJ2CeXdT31IbQ3Cp65cqG0wi1afyPg,9475
statsmodels-0.14.4.dist-info/RECORD,,
statsmodels-0.14.4.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels-0.14.4.dist-info/WHEEL,sha256=3vidnDuZ-QSnHIxLhNbI1gIM-KgyEcMHuZuv1mWPd_Q,101
statsmodels-0.14.4.dist-info/top_level.txt,sha256=cucLjFr_CwyszbZXR9yr9y0AB3j85RPFlmC11dsyblU,12
statsmodels/LICENSE.txt,sha256=CJchLCSwm6acSRdJDYgoCB1gu0MD7ABF5VOp5HDzjvo,1670
statsmodels/__init__.py,sha256=eFJdtuz5htGEJyhCwzmzvYP8Qt7jCiAze9gIdXfEV_c,1157
statsmodels/__pycache__/__init__.cpython-312.pyc,,
statsmodels/__pycache__/_version.cpython-312.pyc,,
statsmodels/__pycache__/api.cpython-312.pyc,,
statsmodels/__pycache__/conftest.cpython-312.pyc,,
statsmodels/_version.py,sha256=G0MGDp1Ehbz82LNcNGqyQi767CZBghPZfG8NE9nAGYU,429
statsmodels/api.py,sha256=B8FliVEFw5samPeOSY046p3MqEc3r9myd8coQQuQKH0,3553
statsmodels/base/__init__.py,sha256=FNIIWqMJDn_RaWzLhb1qyMHw6Oh_msp2OmLojqBG2_M,82
statsmodels/base/__pycache__/__init__.cpython-312.pyc,,
statsmodels/base/__pycache__/_constraints.cpython-312.pyc,,
statsmodels/base/__pycache__/_parameter_inference.cpython-312.pyc,,
statsmodels/base/__pycache__/_penalized.cpython-312.pyc,,
statsmodels/base/__pycache__/_penalties.cpython-312.pyc,,
statsmodels/base/__pycache__/_prediction_inference.cpython-312.pyc,,
statsmodels/base/__pycache__/_screening.cpython-312.pyc,,
statsmodels/base/__pycache__/covtype.cpython-312.pyc,,
statsmodels/base/__pycache__/data.cpython-312.pyc,,
statsmodels/base/__pycache__/distributed_estimation.cpython-312.pyc,,
statsmodels/base/__pycache__/elastic_net.cpython-312.pyc,,
statsmodels/base/__pycache__/l1_cvxopt.cpython-312.pyc,,
statsmodels/base/__pycache__/l1_slsqp.cpython-312.pyc,,
statsmodels/base/__pycache__/l1_solvers_common.cpython-312.pyc,,
statsmodels/base/__pycache__/model.cpython-312.pyc,,
statsmodels/base/__pycache__/optimizer.cpython-312.pyc,,
statsmodels/base/__pycache__/transform.cpython-312.pyc,,
statsmodels/base/__pycache__/wrapper.cpython-312.pyc,,
statsmodels/base/_constraints.py,sha256=bmmdhco3PayoXfS_YmEzhlVUO9GNUDXHi9FzVa6gsqs,13559
statsmodels/base/_parameter_inference.py,sha256=CaL4yunFJKcMc_jkLgNMwiDB6ijq6JTIZrinaaRpzdw,15781
statsmodels/base/_penalized.py,sha256=NZxU0ODEHgIhrUDWLQS0yCj70IVjXr14-Gpw3pJTjtk,7814
statsmodels/base/_penalties.py,sha256=05gvcocWg9JzYrwbNo2qnsAG1s5XuD7uYjwoA1dkyCI,17650
statsmodels/base/_prediction_inference.py,sha256=LyjxKYfymU-szpVZGoK-trjFg6MfPo0bIEQb6jvccAE,30791
statsmodels/base/_screening.py,sha256=c89ApLHT0k3M2MS1IthlHh14-M1UpvJyHIJbvEA-JCY,18555
statsmodels/base/covtype.py,sha256=dKAa1nRFTuORY3fn8zoI7YBdLAfP2wULGIt5yGGtzTM,15828
statsmodels/base/data.py,sha256=eH6E7SLchPCs8KUuT1sJ0mSUqAGG9RfFCQ7S5xje7J8,25025
statsmodels/base/distributed_estimation.py,sha256=3ADDp0ZccfziRx0V-gq5K-QaMd8gDWtw76h5YMQ1Fzg,22831
statsmodels/base/elastic_net.py,sha256=qvgDf7NrTB8soJuITP-1rspHl3TXH5A-fU82LXwvqPE,13189
statsmodels/base/l1_cvxopt.py,sha256=nKSeM7A357UPHQc7yN5kwuO3wFOns6fEnn5BDJ2rpsk,6894
statsmodels/base/l1_slsqp.py,sha256=FTPaBrsylY_ORkuPnQj8H7RQk9rV5DuFICdjGLtRMAo,5772
statsmodels/base/l1_solvers_common.py,sha256=vvUb4JWinSsjBiYjIWPFaALfIpuAsk9Pi42L8sUT6Ck,5745
statsmodels/base/model.py,sha256=tsAZFAM3_8Yen7uQo55jqW0aSaxGkj86T39oJGy3yy8,115245
statsmodels/base/optimizer.py,sha256=KYocGnq1di8JfmcOe1ahs2p5u9iIQQxQmEbqbtrmcgU,44539
statsmodels/base/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/base/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/base/tests/__pycache__/test_data.cpython-312.pyc,,
statsmodels/base/tests/__pycache__/test_distributed_estimation.cpython-312.pyc,,
statsmodels/base/tests/__pycache__/test_generic_methods.cpython-312.pyc,,
statsmodels/base/tests/__pycache__/test_optimize.cpython-312.pyc,,
statsmodels/base/tests/__pycache__/test_penalized.cpython-312.pyc,,
statsmodels/base/tests/__pycache__/test_penalties.cpython-312.pyc,,
statsmodels/base/tests/__pycache__/test_predict.cpython-312.pyc,,
statsmodels/base/tests/__pycache__/test_screening.cpython-312.pyc,,
statsmodels/base/tests/__pycache__/test_shrink_pickle.cpython-312.pyc,,
statsmodels/base/tests/__pycache__/test_transform.cpython-312.pyc,,
statsmodels/base/tests/test_data.py,sha256=uwuFo6T0s8AZUyiY52XllV3KUye2OZxKD2K8-yrsUUI,34957
statsmodels/base/tests/test_distributed_estimation.py,sha256=_znU2WX3kgpHmnocaqb_zFfLca6kM5RsLljWLdbh7_Y,16513
statsmodels/base/tests/test_generic_methods.py,sha256=GfCr_NvdF9dEeqHHeNx3l1DdKUHgGZYx6f4gvQ1G59I,29895
statsmodels/base/tests/test_optimize.py,sha256=3LSbHmiUb5qW2r6Rb5BCpKVVlCJo5_3j2711zNnNay4,4653
statsmodels/base/tests/test_penalized.py,sha256=KO0wT80TEQrJzGeodHH6U-3cUAweUtoZi-H9QgfzLoo,26346
statsmodels/base/tests/test_penalties.py,sha256=-C9rOMGuNXmyAFMFBOaLMogV2pt2qpGT_6EmAMKgOEw,4580
statsmodels/base/tests/test_predict.py,sha256=fzDyZ6cO4ZUEsPXIxmZPayFQ-xjIEyWvA45Mt3QvWCE,5506
statsmodels/base/tests/test_screening.py,sha256=i5q69u8vaBMmaKjMLGBT6E5pdTFwMqu6NQRReEQc710,12036
statsmodels/base/tests/test_shrink_pickle.py,sha256=hfaVSdt2DUZbmgF9uggah_1Xuea7aSzB0rAU7RTkDkM,11052
statsmodels/base/tests/test_transform.py,sha256=dIKPLajKZtaawPzroFAZLWnFkd_NgnDjsbnN7xosxzY,4577
statsmodels/base/transform.py,sha256=wjHAzTnmrvrY3kEdUj0UbzwXt7AQujmC8unodHYd0tY,8435
statsmodels/base/wrapper.py,sha256=5QFvQV1AK4hTtGi9NzEsHWvoZ6PQULJcZbxdw0wEfwQ,3710
statsmodels/compat/__init__.py,sha256=X-qk7xeNjqn1sGe3FJSZnMlEtXyWSOsN9086sdM_1Ss,337
statsmodels/compat/__pycache__/__init__.cpython-312.pyc,,
statsmodels/compat/__pycache__/_scipy_multivariate_t.cpython-312.pyc,,
statsmodels/compat/__pycache__/numpy.cpython-312.pyc,,
statsmodels/compat/__pycache__/pandas.cpython-312.pyc,,
statsmodels/compat/__pycache__/patsy.cpython-312.pyc,,
statsmodels/compat/__pycache__/platform.cpython-312.pyc,,
statsmodels/compat/__pycache__/pytest.cpython-312.pyc,,
statsmodels/compat/__pycache__/python.cpython-312.pyc,,
statsmodels/compat/__pycache__/scipy.cpython-312.pyc,,
statsmodels/compat/_scipy_multivariate_t.py,sha256=tPZDuzCArPvfAgLHBXQiYsFersbF8iTER8Y_DAy6B0w,40631
statsmodels/compat/numpy.py,sha256=zkTduwYsKjVDpa9u7ifgj7_Hq4F7l2CNa9l_trzSHmg,2438
statsmodels/compat/pandas.py,sha256=sawQWNEOt3DSYqbvIQ9P8wSN2lVPTP6UCXpDptUfEbU,4401
statsmodels/compat/patsy.py,sha256=7JTgEKES9Ebk73ptZnzLVcphIoSiDc3jQw_exW6JLyM,394
statsmodels/compat/platform.py,sha256=tJIW_gt6ydZKyIsSp-vmfkVOpGU48thKEuBeRsDSGh8,479
statsmodels/compat/pytest.py,sha256=blw_7s8X8lou3lx-yId1NlOhXBmuESq1XMXjQ4wDi-4,1215
statsmodels/compat/python.py,sha256=bXEuTL-6a0K3ZD07O8vkCUL774TiRDooKvOEU2SjQHU,1440
statsmodels/compat/scipy.py,sha256=OMTXEq9C7qLCeouMy7iWlNnsUp9SedCRqZ5GvKdOCc0,2127
statsmodels/compat/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/compat/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/compat/tests/__pycache__/test_itercompat.cpython-312.pyc,,
statsmodels/compat/tests/__pycache__/test_pandas.cpython-312.pyc,,
statsmodels/compat/tests/__pycache__/test_scipy_compat.cpython-312.pyc,,
statsmodels/compat/tests/test_itercompat.py,sha256=7L_gi2mBlcBnifr0RVR_CT-X8oblk5pQMVRbMq50j2U,1204
statsmodels/compat/tests/test_pandas.py,sha256=AjuNMWj-7jwoziE3WFoVD1tsndHuGVu4q27mguqPEkk,1280
statsmodels/compat/tests/test_scipy_compat.py,sha256=ZbY8ZmgQx-hdmmCS3WWdNe8rR-gs0r23Del5-7zFC-A,2902
statsmodels/conftest.py,sha256=tJpXiLdN1qFJ4WU8xTkm42z86WEpGhThkNLlG2q895c,5097
statsmodels/datasets/__init__.py,sha256=mgeuOmWrHEX9siHbhy-pLDHYOp6EeGMB7MrlR_VyHvs,1261
statsmodels/datasets/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/__pycache__/template_data.cpython-312.pyc,,
statsmodels/datasets/__pycache__/utils.cpython-312.pyc,,
statsmodels/datasets/anes96/__init__.py,sha256=KVfn2paR7EPVBNJxjjr2EYSaoPIevZFVxpYZ8wzVJ7c,219
statsmodels/datasets/anes96/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/anes96/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/anes96/anes96.csv,sha256=NuVaDXygyGMEwYaNQgHzAtIELcB4up10i26Y9t29Umc,22535
statsmodels/datasets/anes96/data.py,sha256=tQ2eGih1gUn0dwQS7052Dp4nQ4k7SMQjbdEcARJq_RU,3806
statsmodels/datasets/cancer/__init__.py,sha256=KVfn2paR7EPVBNJxjjr2EYSaoPIevZFVxpYZ8wzVJ7c,219
statsmodels/datasets/cancer/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/cancer/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/cancer/cancer.csv,sha256=GcvSK4S5N_jvkIwyZIpPLedaZazB1YNz8ojug2_aaS4,2784
statsmodels/datasets/cancer/data.py,sha256=seUVtNGBBqnVQCCLaAD072CqRhviBu6T-JQ4znYIuGc,1226
statsmodels/datasets/ccard/__init__.py,sha256=KVfn2paR7EPVBNJxjjr2EYSaoPIevZFVxpYZ8wzVJ7c,219
statsmodels/datasets/ccard/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/ccard/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/ccard/ccard.csv,sha256=GeVQ-HyJL2V4cFTAWnA7fx0noO8APlkh5nb6IVEk1sE,1644
statsmodels/datasets/ccard/data.py,sha256=vS6Jqa7DZ_GcLj-cteq1kPNRAFqz43ZO9LKvuyN6Hpo,1412
statsmodels/datasets/china_smoking/__init__.py,sha256=KVfn2paR7EPVBNJxjjr2EYSaoPIevZFVxpYZ8wzVJ7c,219
statsmodels/datasets/china_smoking/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/china_smoking/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/china_smoking/china_smoking.csv,sha256=0URFgqqIOna04YTs7qb6FMoRFz2XDBRpr6zFq9AxYeI,289
statsmodels/datasets/china_smoking/data.py,sha256=mG0yWvTP_47bBulUHnu-YY_3i4P0qkYJrY5YiPsEY10,1574
statsmodels/datasets/co2/__init__.py,sha256=KVfn2paR7EPVBNJxjjr2EYSaoPIevZFVxpYZ8wzVJ7c,219
statsmodels/datasets/co2/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/co2/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/co2/co2.csv,sha256=Vgn4AaClKchfYB5wAvRw77XX9VSfy205IOYs-2AVlSU,36259
statsmodels/datasets/co2/data.py,sha256=Alrxzbtii2nzZsE0NngH-04ho3HLrE4knSfozc1M8wg,2419
statsmodels/datasets/committee/__init__.py,sha256=KVfn2paR7EPVBNJxjjr2EYSaoPIevZFVxpYZ8wzVJ7c,219
statsmodels/datasets/committee/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/committee/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/committee/committee.csv,sha256=rsujg0DXlFRTy_TW5fZtD5aUUB4ZwAZiMd6Hl3-NHiM,733
statsmodels/datasets/committee/data.py,sha256=PAGDu_LlJjLVRDPUBfwKd8a_0jrjVP1FhnVlyWkn_GQ,2386
statsmodels/datasets/copper/__init__.py,sha256=KVfn2paR7EPVBNJxjjr2EYSaoPIevZFVxpYZ8wzVJ7c,219
statsmodels/datasets/copper/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/copper/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/copper/copper.csv,sha256=v9ETBmbwdIg-Pl9geXkdE_Co6D9Pt9lTDnQQ9sjlQrM,1008
statsmodels/datasets/copper/data.py,sha256=lVGzxfCPbbXncOpCN1wHDs5kJ6kRIMZqiZm7DrDvioA,2101
statsmodels/datasets/cpunish/__init__.py,sha256=KVfn2paR7EPVBNJxjjr2EYSaoPIevZFVxpYZ8wzVJ7c,219
statsmodels/datasets/cpunish/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/cpunish/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/cpunish/cpunish.csv,sha256=wzHlczU2z03tMMOLQofl8nj0wageMG3dU4lFTZk45-g,764
statsmodels/datasets/cpunish/data.py,sha256=LpfYR9ADadnNh7FqJhJUGGoFzpYhuDZhhHHyyVn3GO8,2395
statsmodels/datasets/danish_data/__init__.py,sha256=mUklYDgr7O5pCqLqA3baKGxPMdeeIRfysZ1OVNgt4bc,284
statsmodels/datasets/danish_data/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/danish_data/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/danish_data/data.csv,sha256=BNfF4j4mdnczJuxBB8MtTix4rvJh98xhBwtsyhPeh94,3349
statsmodels/datasets/danish_data/data.py,sha256=FxJELx1IkDRGEFnlpBgoCvUFYrR3zKMwkcqC6kfPpSM,1818
statsmodels/datasets/elec_equip/__init__.py,sha256=KVfn2paR7EPVBNJxjjr2EYSaoPIevZFVxpYZ8wzVJ7c,219
statsmodels/datasets/elec_equip/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/elec_equip/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/elec_equip/data.py,sha256=s0eOVmkjncrBgokZ7cg5_jme-Pnl3qcg04Tg7fl5Lrg,1583
statsmodels/datasets/elec_equip/elec_equip.csv,sha256=jaoAqV_bL7LAK7LWM19OqgQIo0WJn888nKH4PnYLTnI,4763
statsmodels/datasets/elnino/__init__.py,sha256=KVfn2paR7EPVBNJxjjr2EYSaoPIevZFVxpYZ8wzVJ7c,219
statsmodels/datasets/elnino/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/elnino/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/elnino/data.py,sha256=mAAVzN65iNpNw9_IvJm29W2yAU21JBEUIWef7wCUe88,1525
statsmodels/datasets/elnino/elnino.csv,sha256=6XT0a29hYheX0AEVGg0-4Bzfxl2lfxWM8-VuRYCzNng,5570
statsmodels/datasets/engel/__init__.py,sha256=KVfn2paR7EPVBNJxjjr2EYSaoPIevZFVxpYZ8wzVJ7c,219
statsmodels/datasets/engel/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/engel/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/engel/data.py,sha256=V_wPOpDhjoTnkp3NGhZ5ycbu6imCdlEDB91PRRVq_wg,1422
statsmodels/datasets/engel/engel.csv,sha256=BbYOCRMlvBcMtoA854-AwCm_4q4HKJHsV38evo3u_WE,8177
statsmodels/datasets/fair/__init__.py,sha256=KVfn2paR7EPVBNJxjjr2EYSaoPIevZFVxpYZ8wzVJ7c,219
statsmodels/datasets/fair/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/fair/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/fair/data.py,sha256=Tw9lB1-MVjVQwV4OjzYC_jZ197M6bjtMSCtX6MpJbn0,2562
statsmodels/datasets/fair/fair.csv,sha256=ECx2w_67vomOD9ID4tX44c3s1Th11WkS7KNRyLhlveg,158183
statsmodels/datasets/fair/fair_pt.csv,sha256=IvqdHVZFo--j_YalEVEMXycscEBTa4lc9Y71f0gW7jY,22995
statsmodels/datasets/fertility/__init__.py,sha256=KVfn2paR7EPVBNJxjjr2EYSaoPIevZFVxpYZ8wzVJ7c,219
statsmodels/datasets/fertility/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/fertility/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/fertility/data.py,sha256=Zq6P7FShIFdDpaET3m21VhctbSFvRIx748GtWQJ1pQU,2102
statsmodels/datasets/fertility/fertility.csv,sha256=Pk8tksl3CZ-Uiloi0GoV4AluxxPvlFPuDIJA5YgODkQ,94674
statsmodels/datasets/grunfeld/__init__.py,sha256=KVfn2paR7EPVBNJxjjr2EYSaoPIevZFVxpYZ8wzVJ7c,219
statsmodels/datasets/grunfeld/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/grunfeld/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/grunfeld/data.py,sha256=XDZr-vx-VyzZQD2qt_38T8bLeYgf9mXkZDp1ooDhsWQ,2394
statsmodels/datasets/grunfeld/grunfeld.csv,sha256=Z8lT2HynNPRgCdV65f7-JGT-OJh0a1iwEcHoKBRncaA,7850
statsmodels/datasets/heart/__init__.py,sha256=KVfn2paR7EPVBNJxjjr2EYSaoPIevZFVxpYZ8wzVJ7c,219
statsmodels/datasets/heart/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/heart/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/heart/data.py,sha256=anr4kZg1jW40LCcbLsr8TccfqFtyLAYQYjHUN2kmCDo,1365
statsmodels/datasets/heart/heart.csv,sha256=Q01N8guovFEyYEq2Fq-aKwIYrkvcRyp9MrFRNTb5zvw,2134
statsmodels/datasets/interest_inflation/E6.csv,sha256=rEVSGoibghqJWqSwZHTmR7NvV4wYgsc2byTwgIiyJr8,2642
statsmodels/datasets/interest_inflation/E6_jmulti.csv,sha256=2GNjHjaAEqaQPf1rF9ViVZS0pOARouB94odIN7DOwPE,1866
statsmodels/datasets/interest_inflation/__init__.py,sha256=KVfn2paR7EPVBNJxjjr2EYSaoPIevZFVxpYZ8wzVJ7c,219
statsmodels/datasets/interest_inflation/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/interest_inflation/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/interest_inflation/data.py,sha256=HhXaR6K61s00JhjnPOz3Nd2l4LtTh0Eg9l5JhdamRlU,1466
statsmodels/datasets/longley/__init__.py,sha256=KVfn2paR7EPVBNJxjjr2EYSaoPIevZFVxpYZ8wzVJ7c,219
statsmodels/datasets/longley/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/longley/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/longley/data.py,sha256=yCoxFYe4d1PDNPf75Q7IlgeoIy5dCnLnpa1DfiUDZ4o,1753
statsmodels/datasets/longley/longley.csv,sha256=YoCPHEz-mpNlUSSpbkk0YSaEw-obGcTV0P5Q3tf2zRA,759
statsmodels/datasets/macrodata/__init__.py,sha256=KVfn2paR7EPVBNJxjjr2EYSaoPIevZFVxpYZ8wzVJ7c,219
statsmodels/datasets/macrodata/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/macrodata/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/macrodata/data.py,sha256=DEokpulS5hcJ5yf0EPiEct1iMo1bhwtL08PeHvUUBWE,3026
statsmodels/datasets/macrodata/macrodata.csv,sha256=eFP8wNkqm4m2J8tustJ_r0oAx1FeOzepokAB71l8qPA,18033
statsmodels/datasets/macrodata/macrodata.dta,sha256=zVSRVduL-pxTDNFoNmxS_wY-NXwNUg7eS3d1Yte27t8,13255
statsmodels/datasets/modechoice/__init__.py,sha256=KVfn2paR7EPVBNJxjjr2EYSaoPIevZFVxpYZ8wzVJ7c,219
statsmodels/datasets/modechoice/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/modechoice/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/modechoice/data.py,sha256=u5hieAt9Oi-nuciYKRw-GLhAMd-Kywe9CJDiYkDeZd4,2713
statsmodels/datasets/modechoice/modechoice.csv,sha256=8nH5KLpNdqrTXVoHu92XptT6KKK4wNIiTxC2vn0Tc68,22407
statsmodels/datasets/nile/__init__.py,sha256=KVfn2paR7EPVBNJxjjr2EYSaoPIevZFVxpYZ8wzVJ7c,219
statsmodels/datasets/nile/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/nile/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/nile/data.py,sha256=IbgJ5uqZAA-k8OkO_GtGVOArH4N2P6ClcpEi4yeE3MY,1453
statsmodels/datasets/nile/nile.csv,sha256=heRJTQD3pjhFqNRMCQrOgiF3o6KY2YodvLbH_URC7kQ,1043
statsmodels/datasets/randhie/__init__.py,sha256=KVfn2paR7EPVBNJxjjr2EYSaoPIevZFVxpYZ8wzVJ7c,219
statsmodels/datasets/randhie/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/randhie/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/randhie/data.py,sha256=eaW6Ue4fUh2GE8qInqrliqoGPmHrLzwHXljKvedYn-8,2388
statsmodels/datasets/randhie/randhie.csv,sha256=fuDS3qDKjs_PuwLDHWOoir5jIxj1oRDWONagNfSCf5E,768372
statsmodels/datasets/scotland/__init__.py,sha256=KVfn2paR7EPVBNJxjjr2EYSaoPIevZFVxpYZ8wzVJ7c,219
statsmodels/datasets/scotland/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/scotland/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/scotland/data.py,sha256=YvRcwV9nOmFHSHXT9WrlRlun_a2NzK11zH4D7nR7C3I,2810
statsmodels/datasets/scotland/scotvote.csv,sha256=cPn5-2MJu5le_SYYRrD8O3LJ330lNaqmR1qbNnKX8a0,1923
statsmodels/datasets/spector/__init__.py,sha256=KVfn2paR7EPVBNJxjjr2EYSaoPIevZFVxpYZ8wzVJ7c,219
statsmodels/datasets/spector/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/spector/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/spector/data.py,sha256=FTpfcCj1esLUlSS7N6UYDgEv8Zxn_ou6KaBiy6al_-U,1811
statsmodels/datasets/spector/spector.csv,sha256=yvIeIhHJ33MUAULPG5STJlC-k7VD7Li1eknwY2JLJdE,530
statsmodels/datasets/stackloss/__init__.py,sha256=KVfn2paR7EPVBNJxjjr2EYSaoPIevZFVxpYZ8wzVJ7c,219
statsmodels/datasets/stackloss/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/stackloss/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/stackloss/data.py,sha256=QaOIbJ9nn846G7f9GlCFEtgZf0Lj_rDcmNFdfTpc-c0,1668
statsmodels/datasets/stackloss/stackloss.csv,sha256=CfZ8uy52-A7nJ4-uXIewfKJgGE6cmRCThg8uTYNmHx8,314
statsmodels/datasets/star98/__init__.py,sha256=KVfn2paR7EPVBNJxjjr2EYSaoPIevZFVxpYZ8wzVJ7c,219
statsmodels/datasets/star98/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/star98/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/star98/data.py,sha256=DY0sucM27mpMaoafgoqG6qLgaHkstWA5914PPjb6DVA,3598
statsmodels/datasets/star98/star98.csv,sha256=uBI3Ar-ZCgHZ1JhOXgRJ0-qaqkINBMr7A52FsQonLPE,65581
statsmodels/datasets/statecrime/__init__.py,sha256=KVfn2paR7EPVBNJxjjr2EYSaoPIevZFVxpYZ8wzVJ7c,219
statsmodels/datasets/statecrime/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/statecrime/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/statecrime/data.py,sha256=xBJHcUXq6_H2dAgINucw5ZlhRQtOFJOJnohudI9LHkI,2573
statsmodels/datasets/statecrime/statecrime.csv,sha256=kXppiChtJC90VRpnXpNRhDGfKJFJWL9XxBQ0TzU5eJM,2421
statsmodels/datasets/strikes/__init__.py,sha256=KVfn2paR7EPVBNJxjjr2EYSaoPIevZFVxpYZ8wzVJ7c,219
statsmodels/datasets/strikes/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/strikes/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/strikes/data.py,sha256=G5-5Zo0oeet9fCWzj2pdYXNszKG0_1JKtDLToOcE9OU,1698
statsmodels/datasets/strikes/strikes.csv,sha256=6uCAAyku1mcqVTlEwJnAsI85y8CWiug_jMRqb8JYw_8,781
statsmodels/datasets/sunspots/__init__.py,sha256=KVfn2paR7EPVBNJxjjr2EYSaoPIevZFVxpYZ8wzVJ7c,219
statsmodels/datasets/sunspots/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/sunspots/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/sunspots/data.py,sha256=HE-U-dvH6YEflT3qaMWwnOvWXjaFSbhiElQonPqqDls,1595
statsmodels/datasets/sunspots/sunspots.csv,sha256=3wS2KaabxoYRpNHRqM96OpsQ1DoANpPCLXuqxWoXvEY,3254
statsmodels/datasets/template_data.py,sha256=nrawzAK3lU1j_JHhDOig9_vRNneG1bTDUYuGbAy2D7g,1279
statsmodels/datasets/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/datasets/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/tests/__pycache__/test_data.cpython-312.pyc,,
statsmodels/datasets/tests/__pycache__/test_utils.cpython-312.pyc,,
statsmodels/datasets/tests/test_data.py,sha256=DB5B5V1KQR6ESarv_M0PKr77-9ozfxcumW_tNrQaMz4,1234
statsmodels/datasets/tests/test_utils.py,sha256=gTX1MuAYCRLFickTOq6Wm70Dp4yzHtzjHujE3KgOZLo,3484
statsmodels/datasets/utils.py,sha256=eJknvVQ3ARePokdy2vfDh-3rSOSyyZMr-qsJ4pPWveo,10693
statsmodels/discrete/__init__.py,sha256=FNIIWqMJDn_RaWzLhb1qyMHw6Oh_msp2OmLojqBG2_M,82
statsmodels/discrete/__pycache__/__init__.cpython-312.pyc,,
statsmodels/discrete/__pycache__/_diagnostics_count.cpython-312.pyc,,
statsmodels/discrete/__pycache__/conditional_models.cpython-312.pyc,,
statsmodels/discrete/__pycache__/count_model.cpython-312.pyc,,
statsmodels/discrete/__pycache__/diagnostic.cpython-312.pyc,,
statsmodels/discrete/__pycache__/discrete_margins.cpython-312.pyc,,
statsmodels/discrete/__pycache__/discrete_model.cpython-312.pyc,,
statsmodels/discrete/__pycache__/truncated_model.cpython-312.pyc,,
statsmodels/discrete/_diagnostics_count.py,sha256=HD5nk01M5SP8J90TgFHsVUb5fJH32wXTNJ7IRCaqUcM,20736
statsmodels/discrete/conditional_models.py,sha256=24w68kO4_luMyxrvXXGMd2OHTp2-Axledu43M_j3Puw,19350
statsmodels/discrete/count_model.py,sha256=wE9YVdVIwmnYJ8x-mkOFlZ7Sk_94bCMKncgb4SO7HH4,44789
statsmodels/discrete/diagnostic.py,sha256=7vpj5rhtxKAfyo0ejbQSsgVQo9H7hpjT1NnL-YOef1I,9319
statsmodels/discrete/discrete_margins.py,sha256=KlEvKp_GZjfYhr4K4qRU7I5187tOy05N0h7JEVuCRUY,27360
statsmodels/discrete/discrete_model.py,sha256=ByeXwGuol9C9kvSUa7BkCbJmyZReTfN1TvC1Kq-Vx4M,205808
statsmodels/discrete/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/discrete/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/discrete/tests/__pycache__/test_conditional.cpython-312.pyc,,
statsmodels/discrete/tests/__pycache__/test_constrained.cpython-312.pyc,,
statsmodels/discrete/tests/__pycache__/test_count_model.cpython-312.pyc,,
statsmodels/discrete/tests/__pycache__/test_diagnostic.cpython-312.pyc,,
statsmodels/discrete/tests/__pycache__/test_discrete.cpython-312.pyc,,
statsmodels/discrete/tests/__pycache__/test_margins.cpython-312.pyc,,
statsmodels/discrete/tests/__pycache__/test_predict.cpython-312.pyc,,
statsmodels/discrete/tests/__pycache__/test_sandwich_cov.cpython-312.pyc,,
statsmodels/discrete/tests/__pycache__/test_truncated_model.cpython-312.pyc,,
statsmodels/discrete/tests/results/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/discrete/tests/results/__pycache__/__init__.cpython-312.pyc,,
statsmodels/discrete/tests/results/__pycache__/results_count_margins.cpython-312.pyc,,
statsmodels/discrete/tests/results/__pycache__/results_count_robust_cluster.cpython-312.pyc,,
statsmodels/discrete/tests/results/__pycache__/results_discrete.cpython-312.pyc,,
statsmodels/discrete/tests/results/__pycache__/results_glm_logit_constrained.cpython-312.pyc,,
statsmodels/discrete/tests/results/__pycache__/results_poisson_constrained.cpython-312.pyc,,
statsmodels/discrete/tests/results/__pycache__/results_predict.cpython-312.pyc,,
statsmodels/discrete/tests/results/__pycache__/results_truncated.cpython-312.pyc,,
statsmodels/discrete/tests/results/__pycache__/results_truncated_st.cpython-312.pyc,,
statsmodels/discrete/tests/results/mn_logit_summary.txt,sha256=ujDzYPVxx3Sq3WRpn60ysJFJHm4lYWMthlgMIFCFk3Q,3920
statsmodels/discrete/tests/results/mnlogit_resid.csv,sha256=mStrJp6mwksPlQqAOskrGMNw3BCAmm2C2X61DL_KaYA,4721
statsmodels/discrete/tests/results/nbinom_resids.csv,sha256=2dnKLZpLu2NOWZd41gi59P7KQ0DQgmHxMia7LSg3jyI,961
statsmodels/discrete/tests/results/phat_mnlogit.csv,sha256=dvezQK_KgnDOhc-Ew-PKi1rEz53TpqqvlM7IRRS8nek,166144
statsmodels/discrete/tests/results/poisson_resid.csv,sha256=pRmJltqef_C9y-U-aSP98c_6cLVnK6Yev7lzYRIAkfA,135257
statsmodels/discrete/tests/results/predict_prob_poisson.csv,sha256=3gvfunHfwtBOv0wn0YTLdlrVyMxK33VerRVcxCv-xS0,195100
statsmodels/discrete/tests/results/results_count_margins.py,sha256=3ZeoD-Gxavmw0At22mfw1lW9RZYUZWB-nkQt7-uXkfM,17517
statsmodels/discrete/tests/results/results_count_robust_cluster.py,sha256=Q2EKNwJyWLnTn5bL3J8vDJyj7fNvceeyVG9QudpsbLs,24112
statsmodels/discrete/tests/results/results_discrete.py,sha256=81AuzHJbRoyTlc-UgNfbda9GNGsMrojkRGLrekFuLZA,53639
statsmodels/discrete/tests/results/results_glm_logit_constrained.py,sha256=m7ntCr6GCeqr3Y16lLAbOcO6BB4hYuB0DL6jsG603EQ,25244
statsmodels/discrete/tests/results/results_poisson_constrained.py,sha256=Fqulyt6e_Z3ZBzSv5QCXEJyv6pnR2liFocvMkUq1l0A,28587
statsmodels/discrete/tests/results/results_predict.py,sha256=u8mjCeY1xGFH4SaiPq-na7kj3HmHXZQqLCuUV-Iirxs,11862
statsmodels/discrete/tests/results/results_truncated.py,sha256=A6UlempWO3wuZFIVfp3D8Lg3DwjE3R-drZgtCQ1hMRI,2344
statsmodels/discrete/tests/results/results_truncated_st.py,sha256=7ZzvffC4OnjC1L-FaFWIB6zXM6CdikM2Vggrnnl6_uU,27252
statsmodels/discrete/tests/results/ships.csv,sha256=Oaaox6MIRKLIAyqQMQZf5P5vPYezj1zvgygM3T__jWY,920
statsmodels/discrete/tests/results/sm3533.csv,sha256=gXs7cJ8nX0IyeQdC-MJFiOQME6NdT5ngJT5ns8DQN2A,6808
statsmodels/discrete/tests/results/yhat_mnlogit.csv,sha256=E_jFCARzEk2L1vVKHULxQdE-wlh5e-XPSy8QeG4ZzX8,169444
statsmodels/discrete/tests/results/yhat_poisson.csv,sha256=ZU5aXWCknAQbX6kBZZG4o15KQPxQHBPpNdzxQP8MiAc,378979
statsmodels/discrete/tests/test_conditional.py,sha256=V6A_PywWpFWbY6w-h5Xce93YmB5RqYtxSxd1gF52_l4,9974
statsmodels/discrete/tests/test_constrained.py,sha256=nUOCL-NS4D_tDE8VDcrhgcfKtsvS1aKrHfJHJ7Gkq0A,23473
statsmodels/discrete/tests/test_count_model.py,sha256=weksfBtCpiK-mcb0Uwb_NdULy4CN3CR76YRrKFzxFk8,29389
statsmodels/discrete/tests/test_diagnostic.py,sha256=xp01EjTfGSmMvghrhIIzwHeqIHNuzJ8m4wagRDZzano,4814
statsmodels/discrete/tests/test_discrete.py,sha256=i8a72KeT2-fD6QsX8dOxCbYEt06gpPlTUBjQbwWMjUo,101812
statsmodels/discrete/tests/test_margins.py,sha256=ED3YQbpxePacsodmlBGQ5klDnZijOOg3LiVbo-od9Yk,4764
statsmodels/discrete/tests/test_predict.py,sha256=TBu0zwDRUB3-Y4D1fUwybRUiQDE2DxPVU6n3vLZa3VQ,15915
statsmodels/discrete/tests/test_sandwich_cov.py,sha256=pruhwFPfWBhw-2PMW5ks-uBn9VR67AuFkH_-4x8wL-4,27789
statsmodels/discrete/tests/test_truncated_model.py,sha256=92GEMMUwPJWSgHC0Rx9ccL66uK_Uvfku4VC53HpeFvU,21690
statsmodels/discrete/truncated_model.py,sha256=miYf77z-1CZ6E3G8zaPNo2lqEhX617PwUfHHk2D0Cvc,52311
statsmodels/distributions/__init__.py,sha256=1esUW3jgSNilAgWgYUmtL8zR7NuiavTtGWwZlHGaPnk,533
statsmodels/distributions/__pycache__/__init__.cpython-312.pyc,,
statsmodels/distributions/__pycache__/bernstein.cpython-312.pyc,,
statsmodels/distributions/__pycache__/discrete.cpython-312.pyc,,
statsmodels/distributions/__pycache__/edgeworth.cpython-312.pyc,,
statsmodels/distributions/__pycache__/empirical_distribution.cpython-312.pyc,,
statsmodels/distributions/__pycache__/mixture_rvs.cpython-312.pyc,,
statsmodels/distributions/__pycache__/tools.cpython-312.pyc,,
statsmodels/distributions/bernstein.py,sha256=-2ZfsfjekCvVHh0MveGgHcdYKQnIYKwN4c_umMXi3dQ,7596
statsmodels/distributions/copula/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/distributions/copula/__pycache__/__init__.cpython-312.pyc,,
statsmodels/distributions/copula/__pycache__/_special.cpython-312.pyc,,
statsmodels/distributions/copula/__pycache__/api.cpython-312.pyc,,
statsmodels/distributions/copula/__pycache__/archimedean.cpython-312.pyc,,
statsmodels/distributions/copula/__pycache__/copulas.cpython-312.pyc,,
statsmodels/distributions/copula/__pycache__/depfunc_ev.cpython-312.pyc,,
statsmodels/distributions/copula/__pycache__/elliptical.cpython-312.pyc,,
statsmodels/distributions/copula/__pycache__/extreme_value.cpython-312.pyc,,
statsmodels/distributions/copula/__pycache__/other_copulas.cpython-312.pyc,,
statsmodels/distributions/copula/__pycache__/transforms.cpython-312.pyc,,
statsmodels/distributions/copula/_special.py,sha256=x794dFtjHJBRnCh5pKkURvzp4lw0DNelfliADZQTJOE,2528
statsmodels/distributions/copula/api.py,sha256=EVfyKCczegdx0VUaJtDTLfX1zauCyxvglCEH-doSEVY,921
statsmodels/distributions/copula/archimedean.py,sha256=yjqlblyEHSdDboAO_dZ6Lm3_SMXbnCGgE4cgrmozoNw,14947
statsmodels/distributions/copula/copulas.py,sha256=-0Iby7-0QivfXM4nozbWB7kynszgyirxhRh1f03itTw,18240
statsmodels/distributions/copula/depfunc_ev.py,sha256=m9MVzZJR3iRqtd86OWIVT_J3NAh_IxHbFFss87MMr58,9548
statsmodels/distributions/copula/elliptical.py,sha256=RMozEopu-HPTXICj-RAUZGO1-bNx6UGm6aHXQiG78FM,10554
statsmodels/distributions/copula/extreme_value.py,sha256=UKkaAFyJFp55TkgIsI3gU1kWmNvYsA2a09oSq294Xjg,5115
statsmodels/distributions/copula/other_copulas.py,sha256=88Q706aM0hxUbV-sNU2fqljESfv6UtfAhnXxXOTKh04,3607
statsmodels/distributions/copula/transforms.py,sha256=s6tOoaZbI4z9XngayzjHSNC9DKcvcid5HfyGWmPDfVU,6651
statsmodels/distributions/discrete.py,sha256=WIhPZHpL0g77UXjtjd-A_DyGo9vmf6hQ58cY-eMp68Y,15658
statsmodels/distributions/edgeworth.py,sha256=mcDidd3lw9bkhTRY_OQlV35XGnVJlWoYNJ_xw2fNfYc,6860
statsmodels/distributions/empirical_distribution.py,sha256=cnY2U-ruA_6rUhtnEBzpOyXe9-K_dbLeLxmi9hdF_KY,7257
statsmodels/distributions/mixture_rvs.py,sha256=CZ9Janb86QzwHv7H6PtdKz9KnRWDjNlQbaMcbzqTFOM,10429
statsmodels/distributions/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/distributions/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/distributions/tests/__pycache__/test_bernstein.cpython-312.pyc,,
statsmodels/distributions/tests/__pycache__/test_discrete.cpython-312.pyc,,
statsmodels/distributions/tests/__pycache__/test_ecdf.cpython-312.pyc,,
statsmodels/distributions/tests/__pycache__/test_edgeworth.cpython-312.pyc,,
statsmodels/distributions/tests/__pycache__/test_mixture.cpython-312.pyc,,
statsmodels/distributions/tests/__pycache__/test_tools.cpython-312.pyc,,
statsmodels/distributions/tests/test_bernstein.py,sha256=K03mDtCHh7iA2Tw3GP8_GOFHMJ4gsb87-9dZU_doTmc,5720
statsmodels/distributions/tests/test_discrete.py,sha256=31lyxsALMyJ1_OKB2lQl0PBnDn40rfxH1tpLAWLSap8,20094
statsmodels/distributions/tests/test_ecdf.py,sha256=0EZd7dnvA6ZZkQnqZ8WIbKHK-3jmQtImzpFh_5-E5Jk,2131
statsmodels/distributions/tests/test_edgeworth.py,sha256=SJ3CvkEqKN_TSxXfQI8reQySqdTzApYZM3sIP1a8TzE,6601
statsmodels/distributions/tests/test_mixture.py,sha256=l3E5CLSmQakhllOzMYhLgLHLSIT3bpcNpvHyTvTC8Jg,5168
statsmodels/distributions/tests/test_tools.py,sha256=onyciF-FE8goi_ygH-Gx7vLyKeypeSkJbpz6Sn558PE,5728
statsmodels/distributions/tools.py,sha256=x2tAgjhuD0oJSKmO2cH1W1oby5x-POM0OFf8wTrGaMQ,15578
statsmodels/duration/__init__.py,sha256=FNIIWqMJDn_RaWzLhb1qyMHw6Oh_msp2OmLojqBG2_M,82
statsmodels/duration/__pycache__/__init__.cpython-312.pyc,,
statsmodels/duration/__pycache__/_kernel_estimates.cpython-312.pyc,,
statsmodels/duration/__pycache__/api.cpython-312.pyc,,
statsmodels/duration/__pycache__/hazard_regression.cpython-312.pyc,,
statsmodels/duration/__pycache__/survfunc.cpython-312.pyc,,
statsmodels/duration/_kernel_estimates.py,sha256=sGEC2Y7cHcJc536OBDrZigtXFwXfFnyNmifWKQSV-rs,6430
statsmodels/duration/api.py,sha256=aHR1ulFuSphGXb-Fuq5l3jiDCdt75ZlH9c58I4Aea2o,201
statsmodels/duration/hazard_regression.py,sha256=A2xdKVr2F_5j6KBL_vg1JoY8PWypROAOJGPqJS9TDl4,60244
statsmodels/duration/survfunc.py,sha256=SMQa4i2Wih3p2fJesP5pREyk6NJqXzayy0HNYap_0s8,28044
statsmodels/duration/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/duration/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/duration/tests/__pycache__/test_phreg.cpython-312.pyc,,
statsmodels/duration/tests/__pycache__/test_survfunc.cpython-312.pyc,,
statsmodels/duration/tests/results/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/duration/tests/results/__pycache__/__init__.cpython-312.pyc,,
statsmodels/duration/tests/results/__pycache__/phreg_gentests.cpython-312.pyc,,
statsmodels/duration/tests/results/__pycache__/survival_enet_r_results.cpython-312.pyc,,
statsmodels/duration/tests/results/__pycache__/survival_r_results.cpython-312.pyc,,
statsmodels/duration/tests/results/bmt.csv,sha256=OjeLKU33MidbNUQP0g17HQV6oIXIHGXRo-kSEHF6CD8,2482
statsmodels/duration/tests/results/bmt_results.csv,sha256=zbahgoqX_8Oxmcq0RxrEOJ_56wGmQOxnjO3eocQyPOQ,1641
statsmodels/duration/tests/results/phreg_gentests.py,sha256=ALyn9vAP_vosgHbMM8ttDHR3kbkliieoq_UVt7oliM4,1997
statsmodels/duration/tests/results/survival_data_1000_10.csv,sha256=FD9BANEp-_AX1KCWOqSPrCd5VBmXHvyM3HYEPCe0ubY,110015
statsmodels/duration/tests/results/survival_data_100_5.csv,sha256=4CYwarZTCqDQSbrV8V3TFsPEu2SYxKa5w8laLI3olZY,6739
statsmodels/duration/tests/results/survival_data_20_1.csv,sha256=qRDss2tPozv8Vp4spWm9oPtQjRyI1cZHUvXVOmVoMTU,668
statsmodels/duration/tests/results/survival_data_50_1.csv,sha256=cpSAba6N6itb17NyJAXFe926yyQtgbmXz5BXc8mpGUQ,1670
statsmodels/duration/tests/results/survival_data_50_2.csv,sha256=2h_rYBLu2BnmMXXqTI6NPg5TeXJ1To_5lGBRhpYc8zg,2108
statsmodels/duration/tests/results/survival_enet_r_results.py,sha256=QVxZq6ygdThDeGzFzv7V22H45SpGc__XiHijc8x8TRs,283
statsmodels/duration/tests/results/survival_r_results.py,sha256=REhFFCXdV9t9vLU1qEw8PViiRsgdv9pg2iV6YJyi-co,13678
statsmodels/duration/tests/test_phreg.py,sha256=vQtj0TWz8KrYf3MUYHS-mEvixXf3Z2ZJixmYNYA7_PU,15411
statsmodels/duration/tests/test_survfunc.py,sha256=bkfiIYQK1gC9b8l2CnuKaxL-DDOpJKc8H2u-GpzNY94,19850
statsmodels/emplike/__init__.py,sha256=FNIIWqMJDn_RaWzLhb1qyMHw6Oh_msp2OmLojqBG2_M,82
statsmodels/emplike/__pycache__/__init__.cpython-312.pyc,,
statsmodels/emplike/__pycache__/aft_el.cpython-312.pyc,,
statsmodels/emplike/__pycache__/api.cpython-312.pyc,,
statsmodels/emplike/__pycache__/descriptive.cpython-312.pyc,,
statsmodels/emplike/__pycache__/elanova.cpython-312.pyc,,
statsmodels/emplike/__pycache__/elregress.cpython-312.pyc,,
statsmodels/emplike/__pycache__/originregress.cpython-312.pyc,,
statsmodels/emplike/aft_el.py,sha256=bo6Z4ONPt4CIH--Clcm5yZzmZuI1Y8CpQVHUV2fL0VE,18684
statsmodels/emplike/api.py,sha256=hGYbOwc-uKKQEsZXzE98Ena5PpF0ezCgfbShc1_Yrpw,314
statsmodels/emplike/descriptive.py,sha256=g408574XyK_gVIwhmStzIyK6Jy6-L_DoyDdg1e636CA,40083
statsmodels/emplike/elanova.py,sha256=_4v_Vs39SkQowZCfRhlVf91cckze-eZiTZ6ONcEeNuU,3795
statsmodels/emplike/elregress.py,sha256=3aOFNTgvs05iXTSA7bI3HqKd1t4BmsHX350l8XliBRE,3180
statsmodels/emplike/originregress.py,sha256=K3LAm8rCfHn9MGNnQU5mq8X4FxvVDubXnlvUYC8gCcg,9210
statsmodels/emplike/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/emplike/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/emplike/tests/__pycache__/test_aft.cpython-312.pyc,,
statsmodels/emplike/tests/__pycache__/test_anova.cpython-312.pyc,,
statsmodels/emplike/tests/__pycache__/test_descriptive.cpython-312.pyc,,
statsmodels/emplike/tests/__pycache__/test_origin.cpython-312.pyc,,
statsmodels/emplike/tests/__pycache__/test_regression.cpython-312.pyc,,
statsmodels/emplike/tests/results/__init__.py,sha256=rwQEFG0yc_DHXZH1fGbwGBl8-JrnkVfI0TgYcefsoLM,20
statsmodels/emplike/tests/results/__pycache__/__init__.cpython-312.pyc,,
statsmodels/emplike/tests/results/__pycache__/el_results.cpython-312.pyc,,
statsmodels/emplike/tests/results/el_results.py,sha256=qLeMpTPOnLoJfVhcPkSNBd5idadC7OnqfZTzvE31ybA,18799
statsmodels/emplike/tests/test_aft.py,sha256=xzBKUcrTEuTm6Kh1C3a3CDaii7IOhJl-akjUvWtUQ5s,1648
statsmodels/emplike/tests/test_anova.py,sha256=CQbDHNugDwjxxcrvrveJ9Xwtw87QDzBOdIyYb-mVDLw,889
statsmodels/emplike/tests/test_descriptive.py,sha256=p9K3MYRlrsyoPWBebgEb7LbQzBeRRqEetN9Ubws4tVM,4431
statsmodels/emplike/tests/test_origin.py,sha256=zAvL-4UVf3nnISMtPi1shcAI3Yk47jFNYKC6Zg9YTjc,1486
statsmodels/emplike/tests/test_regression.py,sha256=RbctDrfhhI7Cfunv98VIOETdKkqq4d1OLBM_szPqcW0,5919
statsmodels/formula/__init__.py,sha256=zGm6fm2LTKSlMqbhfmxoDNJKwpWnyPtELxg6h_fgZg8,174
statsmodels/formula/__pycache__/__init__.cpython-312.pyc,,
statsmodels/formula/__pycache__/api.cpython-312.pyc,,
statsmodels/formula/__pycache__/formulatools.cpython-312.pyc,,
statsmodels/formula/api.py,sha256=j1E18zQsLKAySZoPIizXNp52J-wFhQbUW87eSwLi4Qg,1815
statsmodels/formula/formulatools.py,sha256=BPqYYryF_IL3jtQp1aVE9-0aWgoiLH9VuCx9AbnpPgc,3914
statsmodels/formula/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/formula/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/formula/tests/__pycache__/test_formula.cpython-312.pyc,,
statsmodels/formula/tests/test_formula.py,sha256=No8t6cFgNo9_dxV3V1mhNlEOcxXidlFfMvvQKYzZ3Bw,8974
statsmodels/gam/__init__.py,sha256=FNIIWqMJDn_RaWzLhb1qyMHw6Oh_msp2OmLojqBG2_M,82
statsmodels/gam/__pycache__/__init__.cpython-312.pyc,,
statsmodels/gam/__pycache__/api.cpython-312.pyc,,
statsmodels/gam/__pycache__/gam_penalties.cpython-312.pyc,,
statsmodels/gam/__pycache__/generalized_additive_model.cpython-312.pyc,,
statsmodels/gam/__pycache__/smooth_basis.cpython-312.pyc,,
statsmodels/gam/api.py,sha256=V5lLvaI4YAbt3O3lHcL-HnAaVYG-4XRAXZBt_XhBWOQ,265
statsmodels/gam/gam_cross_validation/__init__.py,sha256=pOaPv0JMH6dsIIxMdx89hwdYPuSRNTC-CWrZhRUW6V8,51
statsmodels/gam/gam_cross_validation/__pycache__/__init__.cpython-312.pyc,,
statsmodels/gam/gam_cross_validation/__pycache__/cross_validators.cpython-312.pyc,,
statsmodels/gam/gam_cross_validation/__pycache__/gam_cross_validation.cpython-312.pyc,,
statsmodels/gam/gam_cross_validation/cross_validators.py,sha256=QKLFSZvu0sqOR6r5PRXeI6MDu6Sy6zYEFw5Qe-dpR_k,1702
statsmodels/gam/gam_cross_validation/gam_cross_validation.py,sha256=lnIEYjbgk3UBnFPJmHT_8g9AUSYDUDgGp4m8smgd9HI,7771
statsmodels/gam/gam_penalties.py,sha256=Y_APf2MXc1wEGIpOgIZnRlmS0qtaUBa3iX8tU8U5VeQ,10051
statsmodels/gam/generalized_additive_model.py,sha256=TUIZBwGTveCC3hLyj-dLyopRe7Jt-PWystWFYV2Df-0,39238
statsmodels/gam/smooth_basis.py,sha256=KptfqEfekosD7YwKJQPb3XmpH6YTSOKdPOj4Cv-xxjk,40263
statsmodels/gam/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/gam/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/gam/tests/__pycache__/test_gam.cpython-312.pyc,,
statsmodels/gam/tests/__pycache__/test_penalized.cpython-312.pyc,,
statsmodels/gam/tests/__pycache__/test_smooth_basis.cpython-312.pyc,,
statsmodels/gam/tests/results/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/gam/tests/results/__pycache__/__init__.cpython-312.pyc,,
statsmodels/gam/tests/results/__pycache__/results_mpg_bs.cpython-312.pyc,,
statsmodels/gam/tests/results/__pycache__/results_mpg_bs_poisson.cpython-312.pyc,,
statsmodels/gam/tests/results/__pycache__/results_pls.cpython-312.pyc,,
statsmodels/gam/tests/results/autos.csv,sha256=CfL-Hs9afCMJE7LuuQwXxSAg30a3DFlYvbQ-TXXOTHc,29050
statsmodels/gam/tests/results/autos_exog.csv,sha256=B-N9Pm4KVtTO2BJfhOERdYfEwQlr4BfdDMRfW2EuDAQ,36128
statsmodels/gam/tests/results/autos_predict.csv,sha256=FiDCmksDjyqEINBlf0pxnWQ0SJyS5t2IQvk0PWPSYJ4,36390
statsmodels/gam/tests/results/cubic_cyclic_splines_from_mgcv.csv,sha256=UxCem9V8HGs0Xz40zAyYyP9T9WnfRMJT084IqrRQ-NQ,65920
statsmodels/gam/tests/results/gam_PIRLS_results.csv,sha256=7gGZiFE1NJZ55TYUdd74PuLOgt5MvLyUZ6JRQJGjeiE,16641
statsmodels/gam/tests/results/logit_gam_mgcv.csv,sha256=75iLqs8P4L_C4CSCovIBHCkh1f0afu_dSqArye5sPug,8655
statsmodels/gam/tests/results/motorcycle.csv,sha256=ILaeeVjdmeuGsL4szcoL1Pruno0lltJjUwMObfTwqOU,1458
statsmodels/gam/tests/results/prediction_from_mgcv.csv,sha256=UOoYVQtokCZ4Rf2D54_qH8f8L1gaQWUUg42IS_phWTI,119662
statsmodels/gam/tests/results/results_mpg_bs.py,sha256=fyePlpgGRFjkfe_ZJTSg4r3cO08WidUTSuiPTR2vlPQ,77765
statsmodels/gam/tests/results/results_mpg_bs_poisson.py,sha256=h0HkMQD0E6srQCXt9bckXvjOl-ZbZGlIcevWDI--BAg,106148
statsmodels/gam/tests/results/results_pls.py,sha256=nTdhKU_EB8NENwA283xLQOZIWSxq8xyEwsfoTlXTOJ8,22140
statsmodels/gam/tests/test_gam.py,sha256=5XeBZ37jjxkzz2fLRVvhi9YDqFWsmmWA43liJdYP11g,26979
statsmodels/gam/tests/test_penalized.py,sha256=P-6Ln45gvQWgljJK8OET4JPKmd60fa4okFLmvIbykUU,25345
statsmodels/gam/tests/test_smooth_basis.py,sha256=q6Bm1g5hEpJ2N_kALAWGWxoebeZx5PTpD0KKFSc8N3E,1289
statsmodels/genmod/__init__.py,sha256=FNIIWqMJDn_RaWzLhb1qyMHw6Oh_msp2OmLojqBG2_M,82
statsmodels/genmod/__pycache__/__init__.cpython-312.pyc,,
statsmodels/genmod/__pycache__/_tweedie_compound_poisson.cpython-312.pyc,,
statsmodels/genmod/__pycache__/api.cpython-312.pyc,,
statsmodels/genmod/__pycache__/bayes_mixed_glm.cpython-312.pyc,,
statsmodels/genmod/__pycache__/cov_struct.cpython-312.pyc,,
statsmodels/genmod/__pycache__/generalized_estimating_equations.cpython-312.pyc,,
statsmodels/genmod/__pycache__/generalized_linear_model.cpython-312.pyc,,
statsmodels/genmod/__pycache__/qif.cpython-312.pyc,,
statsmodels/genmod/_tweedie_compound_poisson.py,sha256=RfNGV1aaZKKbXq7QRsdkLyrCc4KA5-njbjjD-rBQYBE,2934
statsmodels/genmod/api.py,sha256=a381PG10xpQBZd8DrDWk1Ee1N8rACR8XnXMFYXMd4Zg,389
statsmodels/genmod/bayes_mixed_glm.py,sha256=C_uPsoNRG3E2vfm0Xf2tCDrHtP8DrEeVVc6DEgA6vQQ,40036
statsmodels/genmod/cov_struct.py,sha256=_CM_M4OkLC5kLi14kGwoieNwDIEtyUcD6Yd4kFT-eGM,52750
statsmodels/genmod/families/__init__.py,sha256=Z5KqJHy2Shl4b8gMwEJQyA-gSnCZs5_7S-isyhG4hFA,689
statsmodels/genmod/families/__pycache__/__init__.cpython-312.pyc,,
statsmodels/genmod/families/__pycache__/family.cpython-312.pyc,,
statsmodels/genmod/families/__pycache__/links.cpython-312.pyc,,
statsmodels/genmod/families/__pycache__/varfuncs.cpython-312.pyc,,
statsmodels/genmod/families/family.py,sha256=PrBcpxVinMTh9ZIgtAcNCLS62kSArDC8T_PYmBvRx2I,59942
statsmodels/genmod/families/links.py,sha256=ZhfqPtB3qA7TJJT7UoqsKbx1PR75FrSwDLaPvlfSgY8,33762
statsmodels/genmod/families/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/genmod/families/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/genmod/families/tests/__pycache__/test_family.cpython-312.pyc,,
statsmodels/genmod/families/tests/__pycache__/test_link.cpython-312.pyc,,
statsmodels/genmod/families/tests/test_family.py,sha256=ER_jYCvHmzzr9XNRhq5YOxSMmunqXaFV7Ngy7sG0gfM,3789
statsmodels/genmod/families/tests/test_link.py,sha256=GhPwEJ9CK7bSYbpyg1tnxfquIHq2_tDVPw6D43h_MXQ,6531
statsmodels/genmod/families/varfuncs.py,sha256=bbZnt6kuDl7PtgwQd3DQZgzNrER_a9n1jaIgfeW7ZFI,5660
statsmodels/genmod/generalized_estimating_equations.py,sha256=uMK1UXApbhiIpKRWbCHGYyo7PYQKY6Djr8JzxECOo3s,119136
statsmodels/genmod/generalized_linear_model.py,sha256=zRbRGb1vcx1kHvrJGKtEPd2Elv0qriDiSa8l22zor0g,106791
statsmodels/genmod/qif.py,sha256=mpWslLxXn_clEhnpu20WkibMOstpxVGL6mNQm-Xyq4Q,16963
statsmodels/genmod/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/genmod/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/genmod/tests/__pycache__/gee_categorical_simulation_check.cpython-312.pyc,,
statsmodels/genmod/tests/__pycache__/gee_gaussian_simulation_check.cpython-312.pyc,,
statsmodels/genmod/tests/__pycache__/gee_poisson_simulation_check.cpython-312.pyc,,
statsmodels/genmod/tests/__pycache__/gee_simulation_check.cpython-312.pyc,,
statsmodels/genmod/tests/__pycache__/test_bayes_mixed_glm.cpython-312.pyc,,
statsmodels/genmod/tests/__pycache__/test_constrained.cpython-312.pyc,,
statsmodels/genmod/tests/__pycache__/test_gee.cpython-312.pyc,,
statsmodels/genmod/tests/__pycache__/test_gee_glm.cpython-312.pyc,,
statsmodels/genmod/tests/__pycache__/test_glm.cpython-312.pyc,,
statsmodels/genmod/tests/__pycache__/test_glm_weights.cpython-312.pyc,,
statsmodels/genmod/tests/__pycache__/test_qif.cpython-312.pyc,,
statsmodels/genmod/tests/__pycache__/test_score_test.cpython-312.pyc,,
statsmodels/genmod/tests/gee_categorical_simulation_check.py,sha256=Abp3YhAsUv-EzN8m1HdfkcIPnoVRzQCwD2ms7EhXlwg,8869
statsmodels/genmod/tests/gee_gaussian_simulation_check.py,sha256=7q2DNpf3W9vbOyM2sYFv03jFSZkQMYOtn6qCXAr6bV0,10249
statsmodels/genmod/tests/gee_poisson_simulation_check.py,sha256=O2PNgfeHG-jdMyx0VqHU2MmUSwty6Fud3EOCZkFS_9o,9398
statsmodels/genmod/tests/gee_simulation_check.py,sha256=gNWmgH5Mc7qmlp1jCrSJrObErXaBjEORAQ-PrG9asro,9780
statsmodels/genmod/tests/results/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/genmod/tests/results/__pycache__/__init__.cpython-312.pyc,,
statsmodels/genmod/tests/results/__pycache__/elastic_net_generate_tests.cpython-312.pyc,,
statsmodels/genmod/tests/results/__pycache__/gee_generate_tests.cpython-312.pyc,,
statsmodels/genmod/tests/results/__pycache__/glm_test_resids.cpython-312.pyc,,
statsmodels/genmod/tests/results/__pycache__/glmnet_r_results.cpython-312.pyc,,
statsmodels/genmod/tests/results/__pycache__/res_R_var_weight.cpython-312.pyc,,
statsmodels/genmod/tests/results/__pycache__/results_glm.cpython-312.pyc,,
statsmodels/genmod/tests/results/__pycache__/results_glm_poisson_weights.cpython-312.pyc,,
statsmodels/genmod/tests/results/elastic_net_generate_tests.py,sha256=Rl2g0ne6uwWWT8jrHwvj9FgoOtLYIgzxHwRwt4-AKGo,662
statsmodels/genmod/tests/results/enet_binomial.csv,sha256=gTkPW5C52OffNaeGZDjBUrrj418gLbaGu44ZCh9-bJE,6729
statsmodels/genmod/tests/results/enet_poisson.csv,sha256=RmxIFBkZ6A8uluICxi6O4yO4bsetgpp8GHgtfQ9GW3U,6673
statsmodels/genmod/tests/results/epil.csv,sha256=QO8GNb7Tg1utbsVQ-UJUMf6cHA_0tETpHdO9aSBYFPE,16442
statsmodels/genmod/tests/results/gee_generate_tests.py,sha256=MJsQWhIG5P-EF1iQnOEKTSgyONApgrEwDquMYr1-4L0,5595
statsmodels/genmod/tests/results/gee_linear_1.csv,sha256=kLz-bTvRycEqXR7fVR5PPhHS5IOtJDaBLLf10Pgfte4,10540
statsmodels/genmod/tests/results/gee_logistic_1.csv,sha256=E8rm3yX37t7lwd8ctt98dxTKciIOMn-DGTEfxfezrmE,10322
statsmodels/genmod/tests/results/gee_nested_linear_1.csv,sha256=fRilZjAGaz6nRU9RWtNOrPbrnFTuSVVmUJGysdQt3BE,60996
statsmodels/genmod/tests/results/gee_nominal_1.csv,sha256=-3C5Qfs-xu_dgh1ea4zTk9E0vkGvbPCvM1MXoLbxNh4,15080
statsmodels/genmod/tests/results/gee_ordinal_1.csv,sha256=ycrbypd3BT9grrIXjNLHSLkIR3ZsK5HiDYuJR9SRb0Y,30610
statsmodels/genmod/tests/results/gee_poisson_1.csv,sha256=Kuan37mgiT4RgH7yDCE6Mf4i1k4PqlP_7Jvou4WlS80,15368
statsmodels/genmod/tests/results/glm_test_resids.py,sha256=RVwasYp_23_aVr13pMs_j13T4l1YOcRjB_dAli1SYrI,321735
statsmodels/genmod/tests/results/glmnet_r_results.py,sha256=-xMbV2KdcdVtY4zVf9Kp_zo04wKGVmb86mP3NULsCis,1981
statsmodels/genmod/tests/results/igaussident_resids.csv,sha256=WTYk6LpSIM-uext0DumhCnm_yNxn7xo5IehsTuUCxiQ,177638
statsmodels/genmod/tests/results/inv_gaussian.csv,sha256=ybl0WWkgLoGpy2eb14LbyL0F-2c3ST8aC4qyJ2euvBU,766639
statsmodels/genmod/tests/results/iris.csv,sha256=VcnoJw929OBI4gdmyFtJLgCV80cWw7ewyB9Xl6D_gKk,2885
statsmodels/genmod/tests/results/medparlogresids.csv,sha256=4a4JVlFkeKHbrGdb6yo7jT2J3bdypLsXXuF9HupOetE,475066
statsmodels/genmod/tests/results/res_R_var_weight.py,sha256=wjl4EjS-nRzFyXtV0GVoI-n3U-ef-PS3vJoxUyKcBAk,8720
statsmodels/genmod/tests/results/results_glm.py,sha256=sFZri40AzaSwC7gCLDxUdgbSz5USg7QXtNdXx3J3sfg,277902
statsmodels/genmod/tests/results/results_glm_poisson_weights.py,sha256=lQfGM8U5dpYBFvUHlwL4vwZvbPiu6XaWdTJhgtb_-Ic,97644
statsmodels/genmod/tests/results/results_tweedie_aweights_nonrobust.csv,sha256=gK7jZkXh8Ozw9Sy4vFmrcFFWh3tVewL9hohxAbwtCao,402286
statsmodels/genmod/tests/results/stata_cancer_glm.csv,sha256=ihfsTU5rYwL0FRQ-Vrp9QCtJbCVtBah0ALTZZk00mbk,435
statsmodels/genmod/tests/results/stata_lbw_glm.csv,sha256=JPqEKAkwzUY-Ah6gEo7Mik9cPjmW7hCsAVxxBCKwVqs,6946
statsmodels/genmod/tests/results/stata_medpar1_glm.csv,sha256=1jkqDWJ_p-EpXz4r7HEUwfLVrPLsUomBeqPC3ENxV5g,53225
statsmodels/genmod/tests/test_bayes_mixed_glm.py,sha256=SI_V9u71OxZ1xp0K-rjbP9kuGJV8OKU6-JF4KMzTxPM,18777
statsmodels/genmod/tests/test_constrained.py,sha256=gJEd0kYINYpHGSl-7zMxp6XY0Omnej82sftGXVQWEOg,9461
statsmodels/genmod/tests/test_gee.py,sha256=JnUoHDCWkacdrcoiUHL-rS8SIoMd7LIGWVhKgbkD6a0,81921
statsmodels/genmod/tests/test_gee_glm.py,sha256=mdlW59V1HYZt6Iwz0pYq-el7dzsItLtUpsGkak-I9Zs,4411
statsmodels/genmod/tests/test_glm.py,sha256=3TlUB6_O3_RmO05h2OwxuiJcGF_pPifR3V0Vj2ems30,105369
statsmodels/genmod/tests/test_glm_weights.py,sha256=16OZiusnQEzyKYoXuC4tAlJUXdskp2nlpkRDw_EQgEg,40511
statsmodels/genmod/tests/test_qif.py,sha256=nxoJH0daDgCy1C71bpepi7dQ4DC_00y3yuhHiVg28zA,4025
statsmodels/genmod/tests/test_score_test.py,sha256=6MWFL99wJW0VJdj38Y_4aSY6rddJgBy73uHOcMmTujI,9800
statsmodels/graphics/__init__.py,sha256=FNIIWqMJDn_RaWzLhb1qyMHw6Oh_msp2OmLojqBG2_M,82
statsmodels/graphics/__pycache__/__init__.cpython-312.pyc,,
statsmodels/graphics/__pycache__/_regressionplots_doc.cpython-312.pyc,,
statsmodels/graphics/__pycache__/agreement.cpython-312.pyc,,
statsmodels/graphics/__pycache__/api.cpython-312.pyc,,
statsmodels/graphics/__pycache__/boxplots.cpython-312.pyc,,
statsmodels/graphics/__pycache__/correlation.cpython-312.pyc,,
statsmodels/graphics/__pycache__/dotplots.cpython-312.pyc,,
statsmodels/graphics/__pycache__/factorplots.cpython-312.pyc,,
statsmodels/graphics/__pycache__/functional.cpython-312.pyc,,
statsmodels/graphics/__pycache__/gofplots.cpython-312.pyc,,
statsmodels/graphics/__pycache__/mosaicplot.cpython-312.pyc,,
statsmodels/graphics/__pycache__/plot_grids.cpython-312.pyc,,
statsmodels/graphics/__pycache__/plottools.cpython-312.pyc,,
statsmodels/graphics/__pycache__/regressionplots.cpython-312.pyc,,
statsmodels/graphics/__pycache__/tsaplots.cpython-312.pyc,,
statsmodels/graphics/__pycache__/tukeyplot.cpython-312.pyc,,
statsmodels/graphics/__pycache__/utils.cpython-312.pyc,,
statsmodels/graphics/_regressionplots_doc.py,sha256=yyDXZt27J8XdJpyJ5mwpDkCIXRQNeNMpKB-bvY5dVIU,7948
statsmodels/graphics/agreement.py,sha256=-wp1wT8sNE7yHGubdRWj7G9p1-zwueyvF6Vglh7954g,5446
statsmodels/graphics/api.py,sha256=8jFyMQmpG-fYpfz9iwTZI4cxPEa-78ccsubYTgk54sA,1011
statsmodels/graphics/boxplots.py,sha256=JaKWBD-FGzJ5FhXg1io4muN6_d3hGbKBzWVRTecXvIs,17104
statsmodels/graphics/correlation.py,sha256=94HwpbIq3qQEjNzDdSijv1pvU86LMP1J3sPzTVLSDMg,7976
statsmodels/graphics/dotplots.py,sha256=0-lHWMyYoRSoW8fSRrI55BlVRh3kw2ZvSj7Sq_q_N5I,18392
statsmodels/graphics/factorplots.py,sha256=S5HQ2ZZBffoNDJRViVbr6IusmR2c5JIe43zsVy3ZaVU,7622
statsmodels/graphics/functional.py,sha256=MRE81QVEJ60t88VCKnBwun_vr-Wnfq8RT-pAmT7nOzU,32376
statsmodels/graphics/gofplots.py,sha256=dDXc3Ce28zV62Vpo5IYCbFAERw22oY5G35l7C-oNVcA,36566
statsmodels/graphics/mosaicplot.py,sha256=jRCJ0qEKspxKT7TDJxtlENuSZ_6a_zJ9hQmU9kExU7s,27608
statsmodels/graphics/plot_grids.py,sha256=euM4HL6W6x6_1wOey60IGh8TQQ8ZYEBhwsraF4QlMOA,6306
statsmodels/graphics/plottools.py,sha256=P_l4bMyjrVfc4D-21YnWxfhJ_5dNDFnvDe7VSWJdZoI,662
statsmodels/graphics/regressionplots.py,sha256=hsvrXsBURct4gCZ6CGZ8GPjwzAs3FeWPXLfVkXY7JLo,45785
statsmodels/graphics/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/graphics/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/graphics/tests/__pycache__/test_agreement.cpython-312.pyc,,
statsmodels/graphics/tests/__pycache__/test_boxplots.cpython-312.pyc,,
statsmodels/graphics/tests/__pycache__/test_correlation.cpython-312.pyc,,
statsmodels/graphics/tests/__pycache__/test_dotplot.cpython-312.pyc,,
statsmodels/graphics/tests/__pycache__/test_factorplots.cpython-312.pyc,,
statsmodels/graphics/tests/__pycache__/test_functional.cpython-312.pyc,,
statsmodels/graphics/tests/__pycache__/test_gofplots.cpython-312.pyc,,
statsmodels/graphics/tests/__pycache__/test_mosaicplot.cpython-312.pyc,,
statsmodels/graphics/tests/__pycache__/test_regressionplots.cpython-312.pyc,,
statsmodels/graphics/tests/__pycache__/test_tsaplots.cpython-312.pyc,,
statsmodels/graphics/tests/test_agreement.py,sha256=vcMGn2olzt0KqropjBn82hDoPYbYu5SsXVxOo0ZPF-M,1242
statsmodels/graphics/tests/test_boxplots.py,sha256=Gglx59Ut3Sw70ROJu7hnwqRrj7VK-GoCOGZEzaNLmYY,3284
statsmodels/graphics/tests/test_correlation.py,sha256=6lrnEEQbYzPa88y8JCr76VVtdmY5TuLN2NjQ0OELd60,876
statsmodels/graphics/tests/test_dotplot.py,sha256=kkeMQzN-whTxj4a2NAk2pmWuQeNjDdoWTAtbQMahdW0,15520
statsmodels/graphics/tests/test_factorplots.py,sha256=OcZEmGSa7oKXyi9CVBOVTE7P9-ZKmF3R4QhZ_uPQFXs,3097
statsmodels/graphics/tests/test_functional.py,sha256=fdeofvIlFgMslzgXM2IDGNVsznAv_hH9vOlDSUl9c8A,10049
statsmodels/graphics/tests/test_gofplots.py,sha256=dmEph8i-TaOBI1enW5vigDDF2TjwI9enyJb1_Vpp7JQ,22768
statsmodels/graphics/tests/test_mosaicplot.py,sha256=bia0L0oy28ntgFGC7KJdiUGIqB0a63zYgI7oCZmhf4o,19361
statsmodels/graphics/tests/test_regressionplots.py,sha256=FVhrmi6k3P-Si9BQ4CPP0CrXcmEhWdGuB5ZVLuRWt_8,13100
statsmodels/graphics/tests/test_tsaplots.py,sha256=CDAf05l0_5sQ72Z0kc6NKntgsrkiYsHuoZrp8rDBdoU,11703
statsmodels/graphics/tsaplots.py,sha256=cuQpe8u5_mK-i0UuKflzLOcauS-vAC24qGeY0jLcleI,29114
statsmodels/graphics/tukeyplot.py,sha256=fM6sDLkXRjIWy0nc230lY94aESgqX3h1PFzKmROxVb0,2467
statsmodels/graphics/utils.py,sha256=TQWckN63GYBdMQEMii_rJuYD-uAA1AOQnR7sEh-_Dd8,4179
statsmodels/imputation/__init__.py,sha256=FNIIWqMJDn_RaWzLhb1qyMHw6Oh_msp2OmLojqBG2_M,82
statsmodels/imputation/__pycache__/__init__.cpython-312.pyc,,
statsmodels/imputation/__pycache__/bayes_mi.cpython-312.pyc,,
statsmodels/imputation/__pycache__/mice.cpython-312.pyc,,
statsmodels/imputation/__pycache__/ros.cpython-312.pyc,,
statsmodels/imputation/bayes_mi.py,sha256=-BbQ9f48eut0Yrjn0EqMEbHjbwdn-RrIJRQ2rXp-6fY,13921
statsmodels/imputation/mice.py,sha256=2I3cz2m88zJtef2cehXzU1jecYycvoVhFjm8Lo4a4TY,46640
statsmodels/imputation/ros.py,sha256=TQamHPr-_73_LTAvdYJaw58xLnsOKTll4j9X9zIEgF0,19490
statsmodels/imputation/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/imputation/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/imputation/tests/__pycache__/test_bayes_mi.cpython-312.pyc,,
statsmodels/imputation/tests/__pycache__/test_mice.cpython-312.pyc,,
statsmodels/imputation/tests/__pycache__/test_ros.cpython-312.pyc,,
statsmodels/imputation/tests/test_bayes_mi.py,sha256=mbqvd83nX438E4go1NMeT0LV3fhtxuFyte-s9KmCvhM,5251
statsmodels/imputation/tests/test_mice.py,sha256=0EAQahwcAdQHDc1Jz7wET4bAejw4xAX0gTz-tfqvREY,13596
statsmodels/imputation/tests/test_ros.py,sha256=lkrvjM5ihedxemUQEFmtkGrueeFwTLAOKjoatrCaL9c,28554
statsmodels/interface/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/interface/__pycache__/__init__.cpython-312.pyc,,
statsmodels/iolib/__init__.py,sha256=2ZyHT8jguFDIZ4L0D3VPD78e_ZKo5mtlr5eRD0m_H9c,302
statsmodels/iolib/__pycache__/__init__.cpython-312.pyc,,
statsmodels/iolib/__pycache__/api.cpython-312.pyc,,
statsmodels/iolib/__pycache__/foreign.cpython-312.pyc,,
statsmodels/iolib/__pycache__/openfile.cpython-312.pyc,,
statsmodels/iolib/__pycache__/smpickle.cpython-312.pyc,,
statsmodels/iolib/__pycache__/stata_summary_examples.cpython-312.pyc,,
statsmodels/iolib/__pycache__/summary.cpython-312.pyc,,
statsmodels/iolib/__pycache__/summary2.cpython-312.pyc,,
statsmodels/iolib/__pycache__/table.cpython-312.pyc,,
statsmodels/iolib/__pycache__/tableformatting.cpython-312.pyc,,
statsmodels/iolib/api.py,sha256=y97RsMdQtSgDlRXegj9VRT3clXJztRmcqtIdJyZhIS8,209
statsmodels/iolib/foreign.py,sha256=onGYDfD06p2Rs9EYK0qy0XtnxEB88fYYV2nwPlVwNao,4485
statsmodels/iolib/openfile.py,sha256=MmYBXthBRrJ_WBf29S8pboEtKErFTYZPD7qjYnY-WII,2208
statsmodels/iolib/smpickle.py,sha256=fycCZ_VCXQe-bPUgp04pYEBX0rzmn_Qnp1hKUmNJF7w,964
statsmodels/iolib/stata_summary_examples.py,sha256=yyJ54GzhJlP7oQ26TNT7KBFPp0mMyNduxhinhjYocFQ,4697
statsmodels/iolib/summary.py,sha256=1tLxkLmxH-oXGir1jdsIzMnMlj6sHoWq52sqCB8uMe0,32004
statsmodels/iolib/summary2.py,sha256=brlGWpgiGH8ynsY3FQ4-ZAvVI510D9Y7SlwXO6tmev8,23155
statsmodels/iolib/table.py,sha256=oi0wK8W8YzVTq-0a2t4b3yv--1THI7QYtgkyKksqnk8,34242
statsmodels/iolib/tableformatting.py,sha256=lPdjsENGSWTOzSzEs8MWc_3xdDFzuLVfj6FHbN8yzPo,3614
statsmodels/iolib/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/iolib/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/iolib/tests/__pycache__/test_pickle.cpython-312.pyc,,
statsmodels/iolib/tests/__pycache__/test_summary.cpython-312.pyc,,
statsmodels/iolib/tests/__pycache__/test_summary2.cpython-312.pyc,,
statsmodels/iolib/tests/__pycache__/test_summary_old.cpython-312.pyc,,
statsmodels/iolib/tests/__pycache__/test_table.cpython-312.pyc,,
statsmodels/iolib/tests/__pycache__/test_table_econpy.cpython-312.pyc,,
statsmodels/iolib/tests/results/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/iolib/tests/results/__pycache__/__init__.cpython-312.pyc,,
statsmodels/iolib/tests/results/__pycache__/macrodata.cpython-312.pyc,,
statsmodels/iolib/tests/results/data_missing.dta,sha256=BqDWGqR2JOVEqy8xrmyFj071kc-PujilaVJ27bE9Pss,1130
statsmodels/iolib/tests/results/macrodata.py,sha256=d4VgU_bOCUIz1Afk1y4OAMs82K9LhYoNTPnxyRu1oYA,23982
statsmodels/iolib/tests/results/time_series_examples.dta,sha256=gu5nIm245mw9hWRhfAK2pHybxai0Ca7kwfjVScxZE9g,1760
statsmodels/iolib/tests/test_pickle.py,sha256=LRtmEzgczMWI9Wd9qNJqRpNVruVL4OMjOQjrPpq8nLA,1795
statsmodels/iolib/tests/test_summary.py,sha256=nkQIT363a7h0upFqARWEx6cA2nNfpleWwZ3zc4sfwIM,3373
statsmodels/iolib/tests/test_summary2.py,sha256=xaB3Rmg0J0nEmHYq1tx6-fLYIr7T69KmERAerDiezCg,6739
statsmodels/iolib/tests/test_summary_old.py,sha256=Dsd4lEx-A_lS04U1raaMa4_2dcBKGT9pP6B-u3u33VA,3585
statsmodels/iolib/tests/test_table.py,sha256=-3twnwUVhOs8M-a0KmtDb5awTtDbFN3avK64Gq5VRSg,9717
statsmodels/iolib/tests/test_table_econpy.py,sha256=jUX6K_k4QZwbWcZYQdgd8U_g0stE3GaoGhnjPA4m5_E,4335
statsmodels/miscmodels/__init__.py,sha256=FNIIWqMJDn_RaWzLhb1qyMHw6Oh_msp2OmLojqBG2_M,82
statsmodels/miscmodels/__pycache__/__init__.cpython-312.pyc,,
statsmodels/miscmodels/__pycache__/api.cpython-312.pyc,,
statsmodels/miscmodels/__pycache__/count.cpython-312.pyc,,
statsmodels/miscmodels/__pycache__/nonlinls.cpython-312.pyc,,
statsmodels/miscmodels/__pycache__/ordinal_model.cpython-312.pyc,,
statsmodels/miscmodels/__pycache__/tmodel.cpython-312.pyc,,
statsmodels/miscmodels/__pycache__/try_mlecov.cpython-312.pyc,,
statsmodels/miscmodels/api.py,sha256=Q5YgrreY3e0lEg44eshkM9cClITAtOuwoYJHIcJCvHc,246
statsmodels/miscmodels/count.py,sha256=CwbGa8YmqVHlcuvfAqhsdUKpdcSV3L2GfJre9g9gfIo,7236
statsmodels/miscmodels/nonlinls.py,sha256=VodwRqoXSjvNb6lHzEndomEGkEW7ojSzmANgxHbp4lQ,9633
statsmodels/miscmodels/ordinal_model.py,sha256=9rr4IaRFlvr68XoNlcHz51j-AJAzYp9pYJ-FNF_nECc,25168
statsmodels/miscmodels/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/miscmodels/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/miscmodels/tests/__pycache__/results_tmodel.cpython-312.pyc,,
statsmodels/miscmodels/tests/__pycache__/test_generic_mle.cpython-312.pyc,,
statsmodels/miscmodels/tests/__pycache__/test_ordinal_model.cpython-312.pyc,,
statsmodels/miscmodels/tests/__pycache__/test_poisson.cpython-312.pyc,,
statsmodels/miscmodels/tests/__pycache__/test_tmodel.cpython-312.pyc,,
statsmodels/miscmodels/tests/results/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/miscmodels/tests/results/__pycache__/__init__.cpython-312.pyc,,
statsmodels/miscmodels/tests/results/__pycache__/results_ordinal_model.cpython-312.pyc,,
statsmodels/miscmodels/tests/results/ologit_ucla.csv,sha256=HqAu0JdSwd_14v7PzD1Cu4M1xU4S7jcnbVBPdy6iCh8,8679
statsmodels/miscmodels/tests/results/results_ordinal_model.py,sha256=xKIwn4_EgY19L1Ko4GAlkp0XL8WgWVp1RmdDuVc5Y4I,5575
statsmodels/miscmodels/tests/results_tmodel.py,sha256=hhkINxEtZmKUD3RpnolFZav0my6GWQZdVzNkbE2oY8o,12160
statsmodels/miscmodels/tests/test_generic_mle.py,sha256=U-7KmjWy8VsEAAE7uGzlYXi_BYm0vtBMD3je1K6x6KE,8094
statsmodels/miscmodels/tests/test_ordinal_model.py,sha256=7KHrCy3-QJkbOrwOJxsk6HcUxj2CX-0GS6R0kgVVL0Q,20119
statsmodels/miscmodels/tests/test_poisson.py,sha256=znKPKtHTIDlgmSAyTly4b6SJnH8I7okCL9V9Uwy0_q0,8088
statsmodels/miscmodels/tests/test_tmodel.py,sha256=B7xf0i1K3MdhqJu5hgqo8Iv2b0Ns2N6O0zj8nH77PUI,7799
statsmodels/miscmodels/tmodel.py,sha256=qAghpeB6gsK6OnWxZJwG8YlWYjDFrpyweku0TmZp_Pc,7497
statsmodels/miscmodels/try_mlecov.py,sha256=GsXx8OL_bKlE0VHN0-w_cHqTLvNI9KL8Q12fodlNdTk,7194
statsmodels/multivariate/__init__.py,sha256=FNIIWqMJDn_RaWzLhb1qyMHw6Oh_msp2OmLojqBG2_M,82
statsmodels/multivariate/__pycache__/__init__.cpython-312.pyc,,
statsmodels/multivariate/__pycache__/api.cpython-312.pyc,,
statsmodels/multivariate/__pycache__/cancorr.cpython-312.pyc,,
statsmodels/multivariate/__pycache__/factor.cpython-312.pyc,,
statsmodels/multivariate/__pycache__/manova.cpython-312.pyc,,
statsmodels/multivariate/__pycache__/multivariate_ols.cpython-312.pyc,,
statsmodels/multivariate/__pycache__/pca.cpython-312.pyc,,
statsmodels/multivariate/__pycache__/plots.cpython-312.pyc,,
statsmodels/multivariate/api.py,sha256=DPLApuYCC9z4J6TUOZem8sCE-88giCzLIkVl04QZsPY,255
statsmodels/multivariate/cancorr.py,sha256=dDv2dzmgWp6mgw6s6lwtiTzkdfl1IYqQ4zyvd0e1OG8,6063
statsmodels/multivariate/factor.py,sha256=vHVn3yL1Ch5yQLWv8-qkrXBkzkqOMRkIRvCe3c_5FyY,36706
statsmodels/multivariate/factor_rotation/__init__.py,sha256=yPFhbWBwL-3becuCSvyjVFxZFE7mm76k5WDhmFqjKfY,1268
statsmodels/multivariate/factor_rotation/__pycache__/__init__.cpython-312.pyc,,
statsmodels/multivariate/factor_rotation/__pycache__/_analytic_rotation.cpython-312.pyc,,
statsmodels/multivariate/factor_rotation/__pycache__/_gpa_rotation.cpython-312.pyc,,
statsmodels/multivariate/factor_rotation/__pycache__/_wrappers.cpython-312.pyc,,
statsmodels/multivariate/factor_rotation/_analytic_rotation.py,sha256=OLTkm5BoPYw7ieHCM-QLFoVovtUmLtLvTe2UMruLjG4,4136
statsmodels/multivariate/factor_rotation/_gpa_rotation.py,sha256=_LCvOtGU4PG_JNSatsCfN-QzI1i5iKWj02dvRSq824E,19217
statsmodels/multivariate/factor_rotation/_wrappers.py,sha256=QmJNLAU2CRYIkvDEy2465jFDr8oDss6f7fed02Dg6us,13961
statsmodels/multivariate/factor_rotation/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/multivariate/factor_rotation/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/multivariate/factor_rotation/tests/__pycache__/test_rotation.cpython-312.pyc,,
statsmodels/multivariate/factor_rotation/tests/test_rotation.py,sha256=zhpbsW4YDi0rewLAGUBNhXa3LeS0ifdbRMaqJcp3QhA,22389
statsmodels/multivariate/manova.py,sha256=yvJ97npxFdpOlieSH29y3QSWVkXnwXSZl8hkYGy5lHs,4885
statsmodels/multivariate/multivariate_ols.py,sha256=06NgNHSBuujr5BrlowRgqB9HxcIsIKmbmrs-hfWI9rE,21521
statsmodels/multivariate/pca.py,sha256=yPhfSoVlTjTJm_iVJuEpy5mEP_5YVW0ejFKx-fALTSE,33940
statsmodels/multivariate/plots.py,sha256=mrbRkKa9VytaTdJwoZOO4VH_Ojcrj83ldsNnvKy2Has,4498
statsmodels/multivariate/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/multivariate/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/multivariate/tests/__pycache__/test_cancorr.cpython-312.pyc,,
statsmodels/multivariate/tests/__pycache__/test_factor.cpython-312.pyc,,
statsmodels/multivariate/tests/__pycache__/test_manova.cpython-312.pyc,,
statsmodels/multivariate/tests/__pycache__/test_ml_factor.cpython-312.pyc,,
statsmodels/multivariate/tests/__pycache__/test_multivariate_ols.cpython-312.pyc,,
statsmodels/multivariate/tests/__pycache__/test_pca.cpython-312.pyc,,
statsmodels/multivariate/tests/results/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/multivariate/tests/results/__pycache__/__init__.cpython-312.pyc,,
statsmodels/multivariate/tests/results/__pycache__/datamlw.cpython-312.pyc,,
statsmodels/multivariate/tests/results/datamlw.py,sha256=MSqr6bmXJbrcwcuDh4TaKRY-_553YIEutgb4XocxF_0,15865
statsmodels/multivariate/tests/results/factor_data.csv,sha256=4Cu5zThP4cSay1ZQaLh7UjD2kOmeoDy8PXtaTxUMjD4,3820
statsmodels/multivariate/tests/results/factors_stata.csv,sha256=ooROHqMOBG-BLdXSKDXyyLJ9I70djrMnBPm_ZsYZXyg,8452
statsmodels/multivariate/tests/test_cancorr.py,sha256=0o4Sfwhfir8YgHgngOsox749Z0FZCynK0EyfcaSbddY,5416
statsmodels/multivariate/tests/test_factor.py,sha256=NYLjjNHHGHZ5BWB0IDExnogaJY72BSv_vqMHfcbXzHw,11811
statsmodels/multivariate/tests/test_manova.py,sha256=b4ZDdE0p3qB4WWZZ2TACVZ4P1bTDyX0dXPgOWg44wKY,9864
statsmodels/multivariate/tests/test_ml_factor.py,sha256=DbQL9jH4dX0Fuog88Rwna9Jw9fy4aOgkuLqbkft7O0w,6330
statsmodels/multivariate/tests/test_multivariate_ols.py,sha256=BRbz0aVeOLfTrtPOSUW4ZRaFtHnkQ2YBmbcK947-ozU,9181
statsmodels/multivariate/tests/test_pca.py,sha256=tPZFZ7fh8t_8j2-Fz2seFMIV-y0EVHae4bN5W1pI4qg,16987
statsmodels/nonparametric/__init__.py,sha256=-MQbxNZiYWo8WcJKPVxqHeBFqZ98Vbq4XTulVxRM7Os,242
statsmodels/nonparametric/__pycache__/__init__.cpython-312.pyc,,
statsmodels/nonparametric/__pycache__/_kernel_base.cpython-312.pyc,,
statsmodels/nonparametric/__pycache__/api.cpython-312.pyc,,
statsmodels/nonparametric/__pycache__/bandwidths.cpython-312.pyc,,
statsmodels/nonparametric/__pycache__/kde.cpython-312.pyc,,
statsmodels/nonparametric/__pycache__/kdetools.cpython-312.pyc,,
statsmodels/nonparametric/__pycache__/kernel_density.cpython-312.pyc,,
statsmodels/nonparametric/__pycache__/kernel_regression.cpython-312.pyc,,
statsmodels/nonparametric/__pycache__/kernels.cpython-312.pyc,,
statsmodels/nonparametric/__pycache__/kernels_asymmetric.cpython-312.pyc,,
statsmodels/nonparametric/__pycache__/smoothers_lowess.cpython-312.pyc,,
statsmodels/nonparametric/__pycache__/smoothers_lowess_old.cpython-312.pyc,,
statsmodels/nonparametric/_kernel_base.py,sha256=SJ__tby3YvZ_sUc0Kq0K9t-9ciFsEiAQ-IkmcE3_uEQ,18815
statsmodels/nonparametric/_smoothers_lowess.cp312-win_amd64.pyd,sha256=hcfGa9W7JkB_YhfvitMBComwg3ZYyFCPifOn6b7iZDU,206848
statsmodels/nonparametric/api.py,sha256=ex-EmDiK4ddkQ8l--qSjolivkhihIBn-rTFCHdrB0ao,547
statsmodels/nonparametric/bandwidths.py,sha256=cXI7D41jK-Jj6hHWs3AfUKcezJrGjPzuAJRVyJ6m8IQ,4762
statsmodels/nonparametric/kde.py,sha256=FcaA-96IeH1xYp15IvrHUNPmSKoaENOaNZA2cqbq0xY,19769
statsmodels/nonparametric/kdetools.py,sha256=lSyli-RBU-7ShpYd5O65cUmfoN3nou8vTZ6QTgPVCys,1405
statsmodels/nonparametric/kernel_density.py,sha256=ioAD94df7EwSbPWPdodGZ-LxYreGgwYuehRg2YoM-Ig,26573
statsmodels/nonparametric/kernel_regression.py,sha256=PEcYchVLYfPfRJfThB1d2jrVQL1c9IYa90s4NEysLYQ,35815
statsmodels/nonparametric/kernels.py,sha256=DZpPkBRp8sIFosohIHDrrJ8ztALXFDLrZZN-S3JJR2c,7223
statsmodels/nonparametric/kernels_asymmetric.py,sha256=xW6RgMGqLec9SwWEJIcXTZJNTyFBvdnLw459XVafXJQ,27338
statsmodels/nonparametric/linbin.cp312-win_amd64.pyd,sha256=jikqRodPmIlueDCMzOgagYwEWdFUb4xAXv8dnBtE7iw,61952
statsmodels/nonparametric/smoothers_lowess.py,sha256=Caba3SfdZPMG7GJrWT9N6zuy3DbIrA1wmXzlxuqXj44,10365
statsmodels/nonparametric/smoothers_lowess_old.py,sha256=h9W1lNAPBbbizOAOmSucdmoIpCfxE2ISeUXZsKCRoZc,10740
statsmodels/nonparametric/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/nonparametric/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/nonparametric/tests/__pycache__/test_asymmetric.cpython-312.pyc,,
statsmodels/nonparametric/tests/__pycache__/test_bandwidths.cpython-312.pyc,,
statsmodels/nonparametric/tests/__pycache__/test_kde.cpython-312.pyc,,
statsmodels/nonparametric/tests/__pycache__/test_kernel_density.cpython-312.pyc,,
statsmodels/nonparametric/tests/__pycache__/test_kernel_regression.cpython-312.pyc,,
statsmodels/nonparametric/tests/__pycache__/test_kernels.cpython-312.pyc,,
statsmodels/nonparametric/tests/__pycache__/test_lowess.cpython-312.pyc,,
statsmodels/nonparametric/tests/results/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/nonparametric/tests/results/__pycache__/__init__.cpython-312.pyc,,
statsmodels/nonparametric/tests/results/results_kcde.csv,sha256=HjXujtiTpVzvT46nGPpuG6zZ6bsZoqdvlV1STQC21eo,12288
statsmodels/nonparametric/tests/results/results_kde.csv,sha256=9KrpTJmZSlfOh4LItPdxkFiQ9cxVsF5UFfdNHtGww0Q,9779
statsmodels/nonparametric/tests/results/results_kde_fft.csv,sha256=VscsaPY61SHfIjTnL1A8-GAamwycdLnh5zco_JLFTvs,13312
statsmodels/nonparametric/tests/results/results_kde_univ_weights.csv,sha256=0pHvvCxEpYfcK7zT0eHwNQRxi6ysMlarMXMzZaXejgM,6104
statsmodels/nonparametric/tests/results/results_kde_weights.csv,sha256=hcoWNkmM5mLbDWlqmtkrKb4itDA35crE9b26GwXAGa8,913
statsmodels/nonparametric/tests/results/results_kernel_regression.csv,sha256=xYdlJi1vlwHPKM4JO_b0nps8Vjp8jWjscdGXLPuQi10,6841
statsmodels/nonparametric/tests/results/test_lowess_delta.csv,sha256=thG6BAfa6Z71IG5cY_FLia6ueg7Og-Obj1CdfQmwmNQ,8505
statsmodels/nonparametric/tests/results/test_lowess_frac.csv,sha256=w5pTrx0PTpGmX1R6oE8zD4ojTzEFB7vHDWRTkuVXWDw,2211
statsmodels/nonparametric/tests/results/test_lowess_iter.csv,sha256=dXhwwXbbJXM6Jre8Ja9_SXe4fk1IDDRmQnsCZW-ZppQ,999
statsmodels/nonparametric/tests/results/test_lowess_simple.csv,sha256=smNVW0InhfWuJSX7fnpsJ0X-M0ecSbDO1aUc4GHHJDc,652
statsmodels/nonparametric/tests/test_asymmetric.py,sha256=oPaE0tOrJ1_2NTtxU5-ghja2EbublXU_rApGKuS4z14,4692
statsmodels/nonparametric/tests/test_bandwidths.py,sha256=Wn6hipYEsn4p8Ew6fmVhThqRmOn2AQpMuy_j_VfRv8Y,2548
statsmodels/nonparametric/tests/test_kde.py,sha256=UjEdAVjCBoqKfm1N7Tg1d2HzPvfnrF-IKh9_B_Kexlo,13462
statsmodels/nonparametric/tests/test_kernel_density.py,sha256=P0OrA6xBUBH5aHRfh4IhGr-3R-SjLzgdpUhB9ywzYyE,19277
statsmodels/nonparametric/tests/test_kernel_regression.py,sha256=xVvVXmrznXZAP7lBM5WeNtt4VF95_AaB_KuIS_sSKL4,16329
statsmodels/nonparametric/tests/test_kernels.py,sha256=rA1NzpF5jYkkagjD1htAhtrvjIJbcbv2S0FTlhlVQ-s,5112
statsmodels/nonparametric/tests/test_lowess.py,sha256=XtxjUtl6-iUc3Poq2yioKVIRelDegahbHb67LG-6h_k,11172
statsmodels/othermod/__init__.py,sha256=FNIIWqMJDn_RaWzLhb1qyMHw6Oh_msp2OmLojqBG2_M,82
statsmodels/othermod/__pycache__/__init__.cpython-312.pyc,,
statsmodels/othermod/__pycache__/api.cpython-312.pyc,,
statsmodels/othermod/__pycache__/betareg.cpython-312.pyc,,
statsmodels/othermod/api.py,sha256=7ENwoOuoRMFVNhK0sYM7_bjLptAArgQaPnyRGS9GZUI,59
statsmodels/othermod/betareg.py,sha256=7PV8i_k9HHRnQcsgCfHZARLxcSZhwsamaYcEZexWADY,32375
statsmodels/othermod/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/othermod/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/othermod/tests/__pycache__/test_beta.cpython-312.pyc,,
statsmodels/othermod/tests/results/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/othermod/tests/results/__pycache__/__init__.cpython-312.pyc,,
statsmodels/othermod/tests/results/__pycache__/results_betareg.cpython-312.pyc,,
statsmodels/othermod/tests/results/foodexpenditure.csv,sha256=TZc4pEyzIWyYf3C6FzF3eIk_97RvszzOkUK6avMCPwY,654
statsmodels/othermod/tests/results/methylation-test.csv,sha256=gxj8Lqg6Cxre_Ol2OC7cOv7fZvLSp2qBlNpfWc1WiXY,2957
statsmodels/othermod/tests/results/resid_methylation.csv,sha256=plTrT_8cwK2Q_x694hc1Cy6yF6YA9zhmWicAU9oyVeo,4801
statsmodels/othermod/tests/results/results_betareg.py,sha256=8uWs-D294nK_qVGUtLWTHGke-ZUH1TBFeuACfeLD0lU,3481
statsmodels/othermod/tests/test_beta.py,sha256=qxNQeJdF-AqmlOvVn63q65vtSMiIYEH2BCjYJwQdHDk,16472
statsmodels/regression/__init__.py,sha256=tUhLJqoqvuK8MlchwowhPlFGKzPxQM_kZbCRThWvH-Q,160
statsmodels/regression/__pycache__/__init__.cpython-312.pyc,,
statsmodels/regression/__pycache__/_prediction.cpython-312.pyc,,
statsmodels/regression/__pycache__/_tools.cpython-312.pyc,,
statsmodels/regression/__pycache__/dimred.cpython-312.pyc,,
statsmodels/regression/__pycache__/feasible_gls.cpython-312.pyc,,
statsmodels/regression/__pycache__/linear_model.cpython-312.pyc,,
statsmodels/regression/__pycache__/mixed_linear_model.cpython-312.pyc,,
statsmodels/regression/__pycache__/process_regression.cpython-312.pyc,,
statsmodels/regression/__pycache__/quantile_regression.cpython-312.pyc,,
statsmodels/regression/__pycache__/recursive_ls.cpython-312.pyc,,
statsmodels/regression/__pycache__/rolling.cpython-312.pyc,,
statsmodels/regression/_prediction.py,sha256=pE8eKHoFcuRP2uJ2U16FJHpuRAXAl40WlMvLr1SkeTg,7246
statsmodels/regression/_tools.py,sha256=dCoTEwMrt5lyTFlvjyMm6E13I1FlrBfLsW1PCJwgr4Q,4458
statsmodels/regression/dimred.py,sha256=_vA0t5ke7P6gvoSXZy_8hldmXRa0TY-3nAuf1flBeu0,20969
statsmodels/regression/feasible_gls.py,sha256=H4-vizSgxu9PqKDEvciDc9Uua43no1DCI_3T8iIeAXo,7216
statsmodels/regression/linear_model.py,sha256=Sby62RHEkbqKzfDUOkJT3uLLgej4srLrm4GxEd-wYPE,121699
statsmodels/regression/mixed_linear_model.py,sha256=37psVinMb129HxVnvZ5AgbQ7O_XKfh-dsOtRTOS-NHY,107286
statsmodels/regression/process_regression.py,sha256=wK4z7O56J1MBjznLwHoErX1SMdO0HaN9-40xDcI49cE,29490
statsmodels/regression/quantile_regression.py,sha256=3PpQawn1Eoe5niGDXx0YyBZiPdu24ni5BudyL7_-pVk,14504
statsmodels/regression/recursive_ls.py,sha256=cOt5TshBFC1D-7Z2Mf_j0dRO4pY7QQT3q85CGDfzPks,33293
statsmodels/regression/rolling.py,sha256=1gBxo45n4b-nZg3wi_R8VdvwjingWrqp8baC4KPZqEw,29378
statsmodels/regression/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/regression/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/regression/tests/__pycache__/test_cov.cpython-312.pyc,,
statsmodels/regression/tests/__pycache__/test_dimred.cpython-312.pyc,,
statsmodels/regression/tests/__pycache__/test_glsar_gretl.cpython-312.pyc,,
statsmodels/regression/tests/__pycache__/test_glsar_stata.cpython-312.pyc,,
statsmodels/regression/tests/__pycache__/test_lme.cpython-312.pyc,,
statsmodels/regression/tests/__pycache__/test_predict.cpython-312.pyc,,
statsmodels/regression/tests/__pycache__/test_processreg.cpython-312.pyc,,
statsmodels/regression/tests/__pycache__/test_quantile_regression.cpython-312.pyc,,
statsmodels/regression/tests/__pycache__/test_recursive_ls.cpython-312.pyc,,
statsmodels/regression/tests/__pycache__/test_regression.cpython-312.pyc,,
statsmodels/regression/tests/__pycache__/test_robustcov.cpython-312.pyc,,
statsmodels/regression/tests/__pycache__/test_rolling.cpython-312.pyc,,
statsmodels/regression/tests/__pycache__/test_theil.cpython-312.pyc,,
statsmodels/regression/tests/__pycache__/test_tools.cpython-312.pyc,,
statsmodels/regression/tests/results/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/regression/tests/results/__pycache__/__init__.cpython-312.pyc,,
statsmodels/regression/tests/results/__pycache__/generate_lasso.cpython-312.pyc,,
statsmodels/regression/tests/results/__pycache__/generate_lme.cpython-312.pyc,,
statsmodels/regression/tests/results/__pycache__/glmnet_r_results.cpython-312.pyc,,
statsmodels/regression/tests/results/__pycache__/lme_r_results.cpython-312.pyc,,
statsmodels/regression/tests/results/__pycache__/macro_gr_corc_stata.cpython-312.pyc,,
statsmodels/regression/tests/results/__pycache__/results_grunfeld_ols_robust_cluster.cpython-312.pyc,,
statsmodels/regression/tests/results/__pycache__/results_macro_ols_robust.cpython-312.pyc,,
statsmodels/regression/tests/results/__pycache__/results_quantile_regression.cpython-312.pyc,,
statsmodels/regression/tests/results/__pycache__/results_regression.cpython-312.pyc,,
statsmodels/regression/tests/results/__pycache__/results_theil_textile.cpython-312.pyc,,
statsmodels/regression/tests/results/dietox.csv,sha256=0EGpvlLCdp9NCGL2TmcSI6EAxgijtmQ1QjiRcJV6zQA,30718
statsmodels/regression/tests/results/generate_lasso.py,sha256=L85SgUmDU5dxNPHcQTeLNJRr14BGqIe80m_3MYe6NSM,659
statsmodels/regression/tests/results/generate_lme.py,sha256=EMEgBXD6q_oI80Y5NgmmoXRUXxfGEFpmv3w20ADTEYQ,2105
statsmodels/regression/tests/results/glmnet_r_results.py,sha256=fqX5Zb-2lyImkklRfrK0yMZREf8ycp05rAK8UcW2s84,7113
statsmodels/regression/tests/results/lasso_data.csv,sha256=M6cb1Cvxxv_rdx3labq-tBxuQuCHCrOPNEvY9oCIk0o,7134
statsmodels/regression/tests/results/leverage_influence_ols_nostars.txt,sha256=yo_AN_PG5GAyrS2AZXeWDFeVBaa74-Iqvyho1h7ZVMo,14752
statsmodels/regression/tests/results/lme00.csv,sha256=Cetlt111r4Ug35eSXCNiHR1slTNiwIz0Q6oAuVGERZE,6501
statsmodels/regression/tests/results/lme01.csv,sha256=5GloHhcbyxBKgptphugIQSlpZwQLbY32agQnNoD2VX4,6865
statsmodels/regression/tests/results/lme02.csv,sha256=x-7P91n3HcUv36MWGferXPCoQRyOPHpIB3E0B95LaJ8,7890
statsmodels/regression/tests/results/lme03.csv,sha256=A6tHE_192m4ztUK0ATAIzmQfz7_dJW4nx0vCvvLigVA,7691
statsmodels/regression/tests/results/lme04.csv,sha256=Ub03b2buhGrshLPgzsn6L54_l7HLIpPi8eTWYUEAkYc,10122
statsmodels/regression/tests/results/lme05.csv,sha256=kWyWazZuQS1StTk2HbBIobqGrgaQG_zYy64cd5uBcno,10743
statsmodels/regression/tests/results/lme06.csv,sha256=PITyGI7SZyqLy7KRf2qm6907hqvwqABuzexJ_BnRNfs,8729
statsmodels/regression/tests/results/lme07.csv,sha256=3qIOsAjryV7pZfN7LzoCC3Tconl8f1X324TtYDFL4og,8764
statsmodels/regression/tests/results/lme08.csv,sha256=F4eUUUKWhZSW7duXp2ISFYWXwMt_AC77wyV36mAnkCQ,10003
statsmodels/regression/tests/results/lme09.csv,sha256=xyD5vedRQv_v_WQfAGqowQ20GxBYIBuWiPZbqpbOKac,10246
statsmodels/regression/tests/results/lme10.csv,sha256=ZmmGD5BzuWaFEK-hkRnzQ5tEf4zsv_6gTwU5kgT0BW0,12246
statsmodels/regression/tests/results/lme11.csv,sha256=Gad8ameoimy5R2XfhcLDRftOxuBTrgvN15oDCMh_mkI,12168
statsmodels/regression/tests/results/lme_r_results.py,sha256=nHO5py-F5rqcZOz_niVRmchLCL6dP_3PC8JUWhG2Wvo,16227
statsmodels/regression/tests/results/macro_gr_corc_stata.py,sha256=bs7TtXXRjTiwzkMCCyCjfYl47Wscegs-hgFHtHH-E58,11614
statsmodels/regression/tests/results/pastes.csv,sha256=_vbwm92z74mcXfzJPYFhx1JGJVwmgk73kzGdKFqXQu4,1518
statsmodels/regression/tests/results/results_grunfeld_ols_robust_cluster.py,sha256=rdqBgToQI-_Q2xLkjtCdXPt7a5a8LKBefOVIadF40Hk,24369
statsmodels/regression/tests/results/results_macro_ols_robust.py,sha256=PkbgzHW9Ug1TzM5mWgaxPLWauLZsRkxsi4gSk1DYnYQ,13407
statsmodels/regression/tests/results/results_quantile_regression.py,sha256=M-gta7D7ZQfEwzlb7Pm4RVPgI2Lfw5qThtPqyY92zJ0,28283
statsmodels/regression/tests/results/results_regression.py,sha256=SOhoukuw175kWZTjjjMdAHtZ6SR-zdE92C25QuPfZT8,10010
statsmodels/regression/tests/results/results_rls_R.csv,sha256=T_eUARSthwqc3SNNjxZR-sxncKCgttLPztL2hFppm6Q,14488
statsmodels/regression/tests/results/results_rls_stata.csv,sha256=Eb3g-tyfJlp071B-wojlODwKs-EVEZ4lZ8qtMNQzEIo,13312
statsmodels/regression/tests/results/results_theil_textile.py,sha256=XW3mqHp_kslNVuHlsY76VErK757neN1cqV0B10p9MPY,2268
statsmodels/regression/tests/results/theil_textile_predict.csv,sha256=BheM-60faNyIkCVdweH7ZUrHjOCUBtTTFblNy2xQt-Y,383
statsmodels/regression/tests/test_cov.py,sha256=sQr0VunVZ8azfENt7ugwAvlnM-CXs51D2INlmzraQp4,1223
statsmodels/regression/tests/test_dimred.py,sha256=eOnnz4JrmOB4VMvkbLjUTG3Wc15dekPxwQ5deY_l5SU,6899
statsmodels/regression/tests/test_glsar_gretl.py,sha256=NlGwsK790dZlADudO1ntEnZ4zBNLOJMvBmQb_KTX3zU,26569
statsmodels/regression/tests/test_glsar_stata.py,sha256=2sL4OESP92NyfWSPiATaxNmp6uoMQnh1jrYayKVzmwY,3512
statsmodels/regression/tests/test_lme.py,sha256=LMWdSNm6f_Np4nM4DN3GexqNeKU8hxZ3uW6s8-2UF3Q,47223
statsmodels/regression/tests/test_predict.py,sha256=yyu2uwAE4VfmMvLoIoJykcomtrUsd3KSq4fDB2hjUQw,10426
statsmodels/regression/tests/test_processreg.py,sha256=VHtp0vIGZZ0BTNnyZHE57Lpz3T8b1g5FIX_Oy0zIqh4,6162
statsmodels/regression/tests/test_quantile_regression.py,sha256=3Rb-CbJ1vL8dkCC_crDTZPW8wnFSWRPSp0m8T0Fkr3Q,11434
statsmodels/regression/tests/test_recursive_ls.py,sha256=mQemfA_OxSNGvtluCOh37HHlmOisBjO46Lbm1j8W70E,19546
statsmodels/regression/tests/test_regression.py,sha256=dagYUBY6LhXFNWhnq9oBB76ZR5B8rJ7rHGyAihK6BDA,54514
statsmodels/regression/tests/test_robustcov.py,sha256=jtqf3qe64vuGpIfvg0a6soVwB1DKK98IOyxyG3YQg-4,33491
statsmodels/regression/tests/test_rolling.py,sha256=1ITp1DUhpYQ0y4gHzbXfz3EMdC_sdVLk6vWJ9WkLnGI,10828
statsmodels/regression/tests/test_theil.py,sha256=zTXS9XkLZy2yEhp1aT81LHi-Fon4UHHxzA9x7b9VAPI,13818
statsmodels/regression/tests/test_tools.py,sha256=POd_ZV2cgbjcIjJkkY1YU8VVodHzO0BfsgHiqLlOI7Q,2730
statsmodels/robust/__init__.py,sha256=m11gGNEmQSi_JKXRFcRcAEMIDkyEVP8m4-Qr63y2KN4,274
statsmodels/robust/__pycache__/__init__.cpython-312.pyc,,
statsmodels/robust/__pycache__/norms.cpython-312.pyc,,
statsmodels/robust/__pycache__/robust_linear_model.cpython-312.pyc,,
statsmodels/robust/__pycache__/scale.cpython-312.pyc,,
statsmodels/robust/_qn.cp312-win_amd64.pyd,sha256=r6txuZ--40ClV-faTFoK5ciCYw1IOjUCIcEWsKJ81ek,177152
statsmodels/robust/norms.py,sha256=x75RWC-fkwnO37fT-HyJrqsd17RJtxUbi9G1fWEKROs,26046
statsmodels/robust/robust_linear_model.py,sha256=aFDukQNfufMlmt5lGjTRGcKb58kNWgVFX15Pd7O4Rho,22911
statsmodels/robust/scale.py,sha256=bDrs-6pP6deL5Tp_6gsKkBAiqjNQkYdUm__t3sJMzmU,12846
statsmodels/robust/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/robust/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/robust/tests/__pycache__/test_mquantiles.cpython-312.pyc,,
statsmodels/robust/tests/__pycache__/test_norms.cpython-312.pyc,,
statsmodels/robust/tests/__pycache__/test_rlm.cpython-312.pyc,,
statsmodels/robust/tests/__pycache__/test_scale.cpython-312.pyc,,
statsmodels/robust/tests/results/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/robust/tests/results/__pycache__/__init__.cpython-312.pyc,,
statsmodels/robust/tests/results/__pycache__/results_norms.cpython-312.pyc,,
statsmodels/robust/tests/results/__pycache__/results_rlm.cpython-312.pyc,,
statsmodels/robust/tests/results/results_norms.py,sha256=OeAGIYcD8fgq_D5YsCOQqV-dYkdOfFkmWiwEfcEvoYQ,1439
statsmodels/robust/tests/results/results_rlm.py,sha256=1YfRgQUGetShRngG5PdYMVuU82-A7szemNkdj3ZjYqc,19655
statsmodels/robust/tests/test_mquantiles.py,sha256=589LLxPfIRi70YB3gE9BsV5ImOtsiRjL6FMNhwY-i_s,2824
statsmodels/robust/tests/test_norms.py,sha256=nfVjdbZSdmEZor0l3QLTR-J3y5Yq4tcWZVIbYYWJZKM,3267
statsmodels/robust/tests/test_rlm.py,sha256=axkTpSlOjFQ-Fp7zhZci7ij0UT-lL8RYBs0KORFST6o,12919
statsmodels/robust/tests/test_scale.py,sha256=4pH9LVMrP2555WxeUY285VbC2XZRhy6JWBNwxj1uGaQ,8890
statsmodels/sandbox/__init__.py,sha256=i6T2dn1F_AQhJOo0SxGUrzQiaTFrAL7lfiQkLldbFu8,114
statsmodels/sandbox/__pycache__/__init__.cpython-312.pyc,,
statsmodels/sandbox/__pycache__/bspline.cpython-312.pyc,,
statsmodels/sandbox/__pycache__/descstats.cpython-312.pyc,,
statsmodels/sandbox/__pycache__/gam.cpython-312.pyc,,
statsmodels/sandbox/__pycache__/infotheo.cpython-312.pyc,,
statsmodels/sandbox/__pycache__/mle.cpython-312.pyc,,
statsmodels/sandbox/__pycache__/multilinear.cpython-312.pyc,,
statsmodels/sandbox/__pycache__/pca.cpython-312.pyc,,
statsmodels/sandbox/__pycache__/predict_functional.cpython-312.pyc,,
statsmodels/sandbox/__pycache__/rls.cpython-312.pyc,,
statsmodels/sandbox/__pycache__/sysreg.cpython-312.pyc,,
statsmodels/sandbox/archive/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/sandbox/archive/__pycache__/__init__.cpython-312.pyc,,
statsmodels/sandbox/archive/__pycache__/linalg_covmat.cpython-312.pyc,,
statsmodels/sandbox/archive/__pycache__/linalg_decomp_1.cpython-312.pyc,,
statsmodels/sandbox/archive/__pycache__/tsa.cpython-312.pyc,,
statsmodels/sandbox/archive/linalg_covmat.py,sha256=Ywqn6zR7qr_aN0ZjjMO_WAzkiOTkCQY_8BHbjPKNoNc,8745
statsmodels/sandbox/archive/linalg_decomp_1.py,sha256=86_9HQbFTchZOuIhRy2ntn1W9bHAJDJa9-NxzjAzegE,7956
statsmodels/sandbox/archive/tsa.py,sha256=YE1bR9xKDjQqbv7EWkv-n3VsCUwm78W99Tci0VulMCU,1552
statsmodels/sandbox/bspline.py,sha256=ShD-tsgz490Omqj9JBvxQFePrCq7kIx10sPBrvwRgV4,21101
statsmodels/sandbox/datarich/__init__.py,sha256=MUzOOCEy0LTD0U0niZbgEXF6DyG1S3UwurrE_A24BqE,6521
statsmodels/sandbox/datarich/__pycache__/__init__.cpython-312.pyc,,
statsmodels/sandbox/datarich/__pycache__/factormodels.cpython-312.pyc,,
statsmodels/sandbox/datarich/factormodels.py,sha256=xcfNjULvF2JAAK6WHh-IQFoO_ilMXxqFuwPZ1m4yruk,7016
statsmodels/sandbox/descstats.py,sha256=1DU4ltORDTtzlXwxlCHapWMrF4DApMgnFXPIFgBB-fM,6134
statsmodels/sandbox/distributions/__init__.py,sha256=PtH0XIDjfLE06_UUKetf50m4t8z5M9csY-cLzkqlcRY,687
statsmodels/sandbox/distributions/__pycache__/__init__.cpython-312.pyc,,
statsmodels/sandbox/distributions/__pycache__/estimators.cpython-312.pyc,,
statsmodels/sandbox/distributions/__pycache__/extras.cpython-312.pyc,,
statsmodels/sandbox/distributions/__pycache__/genpareto.cpython-312.pyc,,
statsmodels/sandbox/distributions/__pycache__/gof_new.cpython-312.pyc,,
statsmodels/sandbox/distributions/__pycache__/multivariate.cpython-312.pyc,,
statsmodels/sandbox/distributions/__pycache__/mv_measures.cpython-312.pyc,,
statsmodels/sandbox/distributions/__pycache__/mv_normal.cpython-312.pyc,,
statsmodels/sandbox/distributions/__pycache__/otherdist.cpython-312.pyc,,
statsmodels/sandbox/distributions/__pycache__/quantize.cpython-312.pyc,,
statsmodels/sandbox/distributions/__pycache__/sppatch.cpython-312.pyc,,
statsmodels/sandbox/distributions/__pycache__/transform_functions.cpython-312.pyc,,
statsmodels/sandbox/distributions/__pycache__/transformed.cpython-312.pyc,,
statsmodels/sandbox/distributions/__pycache__/try_max.cpython-312.pyc,,
statsmodels/sandbox/distributions/__pycache__/try_pot.cpython-312.pyc,,
statsmodels/sandbox/distributions/estimators.py,sha256=FHS62rVBiLqzK67fB158PJlDoqv3gXhxhrss1LT0SJI,24090
statsmodels/sandbox/distributions/examples/__init__.py,sha256=_6Yl-eMBve0mGQA-aiFg4qvQ7GUCV5eGvIcoRf4mEPw,3
statsmodels/sandbox/distributions/examples/__pycache__/__init__.cpython-312.pyc,,
statsmodels/sandbox/distributions/examples/__pycache__/ex_extras.cpython-312.pyc,,
statsmodels/sandbox/distributions/examples/__pycache__/ex_fitfr.cpython-312.pyc,,
statsmodels/sandbox/distributions/examples/__pycache__/ex_gof.cpython-312.pyc,,
statsmodels/sandbox/distributions/examples/__pycache__/ex_mvelliptical.cpython-312.pyc,,
statsmodels/sandbox/distributions/examples/__pycache__/ex_transf2.cpython-312.pyc,,
statsmodels/sandbox/distributions/examples/__pycache__/matchdist.cpython-312.pyc,,
statsmodels/sandbox/distributions/examples/ex_extras.py,sha256=jcEIqg7wynfSdNoXWkmvcANZ2LC5nrcqzl3xogBo0Eo,4149
statsmodels/sandbox/distributions/examples/ex_fitfr.py,sha256=2_FPfdG2YkmqyC0ojQZq67Sr6bYXMXFFUMCE3Xfmw2o,955
statsmodels/sandbox/distributions/examples/ex_gof.py,sha256=KWzty90AdjBnN8Fbt54b7lBHTUh6_AbhoLUhs9dr3WY,403
statsmodels/sandbox/distributions/examples/ex_mvelliptical.py,sha256=Lf_wPDQdgWzK_5f22Pbvmr2D1SbVtdVy7gAAJyBliWU,5270
statsmodels/sandbox/distributions/examples/ex_transf2.py,sha256=QcTB8517M5uOlkhgLU--M5gB4SdQSAirXe1apZnlRVo,13772
statsmodels/sandbox/distributions/examples/matchdist.py,sha256=vv_9MvbXcxMDaIWO-sZhBHvEoffk6UdGzS7w06hRuTI,10013
statsmodels/sandbox/distributions/extras.py,sha256=dshG_dYROdtf4W5xwu0LV7JXFsU-hd057cm04uqz5Cc,41196
statsmodels/sandbox/distributions/genpareto.py,sha256=nSxWe2EoZAHnn7ARhmopgaESNLOkGOb2qKenOzM4U-I,10665
statsmodels/sandbox/distributions/gof_new.py,sha256=GOTYa6POXOVHfAL4VX9dDHguiegjljRnL31bBp0pNsY,22047
statsmodels/sandbox/distributions/multivariate.py,sha256=-1JwOfbkDIsnSgfCiK5qh5MesP6Ox7zu5SIdcWvah4Q,5018
statsmodels/sandbox/distributions/mv_measures.py,sha256=QG9M1UXq1l9VUrvLK_rD_GEUuq39qG99EwuII0BNiVo,6451
statsmodels/sandbox/distributions/mv_normal.py,sha256=z_Z28n36QOIANvwbOftGblb4LSf3xsFJoC7wREZTWx0,39890
statsmodels/sandbox/distributions/otherdist.py,sha256=5oY6E-EApnWEsZ7X0Td70IhdzfiE5aTTpH7RMa_c0iY,10386
statsmodels/sandbox/distributions/quantize.py,sha256=tgg6j9HIlmRZu7IvBR9B-zCbRHJ5OoaAbEf8Xp2-DG8,4366
statsmodels/sandbox/distributions/sppatch.py,sha256=Swigdc80Npew1pxv8-jN7cFwLm-gK1OjQpxA0we6rUw,24743
statsmodels/sandbox/distributions/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/sandbox/distributions/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/sandbox/distributions/tests/__pycache__/_est_fit.cpython-312.pyc,,
statsmodels/sandbox/distributions/tests/__pycache__/check_moments.cpython-312.pyc,,
statsmodels/sandbox/distributions/tests/__pycache__/distparams.cpython-312.pyc,,
statsmodels/sandbox/distributions/tests/__pycache__/test_extras.cpython-312.pyc,,
statsmodels/sandbox/distributions/tests/__pycache__/test_gof_new.cpython-312.pyc,,
statsmodels/sandbox/distributions/tests/__pycache__/test_multivariate.cpython-312.pyc,,
statsmodels/sandbox/distributions/tests/__pycache__/test_norm_expan.cpython-312.pyc,,
statsmodels/sandbox/distributions/tests/__pycache__/test_transf.cpython-312.pyc,,
statsmodels/sandbox/distributions/tests/_est_fit.py,sha256=OOuSdTBEXBni8FV_3VkYtllq6pHxap5x5XJZWrnGLSc,2628
statsmodels/sandbox/distributions/tests/check_moments.py,sha256=C8JcTIK_o00x2Z9gDAr85zFKzby498H1ijCfgEicv_E,5547
statsmodels/sandbox/distributions/tests/distparams.py,sha256=klbu0kk4n5q3_JaA1lw2tMdZBWHn3q3XHs09yNqXxQs,5247
statsmodels/sandbox/distributions/tests/test_extras.py,sha256=YpWaIXnFuxYu8BqYM31vpp1GMPpjCmyodGnfbpRLb1o,4869
statsmodels/sandbox/distributions/tests/test_gof_new.py,sha256=3YFVxz-1c56dtUupt7_ojeJ5FZUmT9ytEP6jfXqJj6I,989
statsmodels/sandbox/distributions/tests/test_multivariate.py,sha256=HoMat37Vo5uiq81z0v0K-EEMP0gdoXVtcpT5Prd_x00,6457
statsmodels/sandbox/distributions/tests/test_norm_expan.py,sha256=rb43GnF0otrlL1wcrOc35ZIeginHct_xxYWyVO8rvLA,2861
statsmodels/sandbox/distributions/tests/test_transf.py,sha256=A6QGv0Xqw-y6GPLsf-0b_zMr_LOIrc8PC2_q6dbjLgI,6745
statsmodels/sandbox/distributions/transform_functions.py,sha256=wDHJlkS0EVVBiIYon9FpeARk2eq4PxXsyNdu4koBCsI,3810
statsmodels/sandbox/distributions/transformed.py,sha256=wIAemIM-yB5wNV_L4VXOvDdjIz908LBnXU6me-M4TRU,16922
statsmodels/sandbox/distributions/try_max.py,sha256=9JLIsBnMBcn2p56dkAZwRvof2ApBaTK8MvmR7eIYmk4,2422
statsmodels/sandbox/distributions/try_pot.py,sha256=lzzMxR3qgabpfpW_5KjQdDR1a-qwiiO8R21UYUyoCZI,1985
statsmodels/sandbox/gam.py,sha256=2kyugMJlQ03VV2r3NUWt5sZ8nv_TDu3k6Y67TtvhQdA,15792
statsmodels/sandbox/infotheo.py,sha256=Ns9X5XYO4tjaONHfhk2Y09CQxn5D1CrLVVKOMLrhqn0,17058
statsmodels/sandbox/mcevaluate/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/sandbox/mcevaluate/__pycache__/__init__.cpython-312.pyc,,
statsmodels/sandbox/mcevaluate/__pycache__/arma.cpython-312.pyc,,
statsmodels/sandbox/mcevaluate/arma.py,sha256=Crs03b9quZFPpdn4FiTq_NoA1FjSYUPX8W6JOHAJ5QI,4701
statsmodels/sandbox/mle.py,sha256=GT_rHE5K3SAst4HpdjmPKgZX1nFUBDUAu-vX4g8m5fY,1721
statsmodels/sandbox/multilinear.py,sha256=JS6PQ4rO7b8EhrFKa7MZMjiCXTKMaxNSRcxLMkzASh8,14208
statsmodels/sandbox/nonparametric/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/sandbox/nonparametric/__pycache__/__init__.cpython-312.pyc,,
statsmodels/sandbox/nonparametric/__pycache__/densityorthopoly.cpython-312.pyc,,
statsmodels/sandbox/nonparametric/__pycache__/dgp_examples.cpython-312.pyc,,
statsmodels/sandbox/nonparametric/__pycache__/kde2.cpython-312.pyc,,
statsmodels/sandbox/nonparametric/__pycache__/kdecovclass.cpython-312.pyc,,
statsmodels/sandbox/nonparametric/__pycache__/kernel_extras.cpython-312.pyc,,
statsmodels/sandbox/nonparametric/__pycache__/kernels.cpython-312.pyc,,
statsmodels/sandbox/nonparametric/__pycache__/smoothers.cpython-312.pyc,,
statsmodels/sandbox/nonparametric/__pycache__/testdata.cpython-312.pyc,,
statsmodels/sandbox/nonparametric/densityorthopoly.py,sha256=WnlzP1HaZfc2tN9dwKyVL7eZIupx1oNuQXpcqye8Vao,18182
statsmodels/sandbox/nonparametric/dgp_examples.py,sha256=FfSU1MsQrKRSg71998RojFPcxNSUirjmDv5cg3-JUho,6151
statsmodels/sandbox/nonparametric/kde2.py,sha256=e47xcgASQ9BkCI5JmLXT2eSq2YlXwXcT2sndzxrsRHI,3282
statsmodels/sandbox/nonparametric/kdecovclass.py,sha256=oAm115br1rt_baaKGWjQ6Q0De4nRtMsT7MKZxr9xw2I,5833
statsmodels/sandbox/nonparametric/kernel_extras.py,sha256=SAjUjLnFtrpr1CNIQ6PNS0vt4MXImkPmqLclcw3JvvY,14677
statsmodels/sandbox/nonparametric/kernels.py,sha256=ZCAVrNDPH9RUU1No7nx-OjWhxuayrIt2H-jEjYC1aKs,20327
statsmodels/sandbox/nonparametric/smoothers.py,sha256=OiGs_Ade7qchFxaw70lEOHxTZEfwGxjVAslCX2JvWBI,12681
statsmodels/sandbox/nonparametric/testdata.py,sha256=wQ4nKzZBn31jz8AE1LmpOxRVoXKTAstrD0zBqoQLDmI,3565
statsmodels/sandbox/nonparametric/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/sandbox/nonparametric/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/sandbox/nonparametric/tests/__pycache__/ex_gam_am_new.cpython-312.pyc,,
statsmodels/sandbox/nonparametric/tests/__pycache__/ex_gam_new.cpython-312.pyc,,
statsmodels/sandbox/nonparametric/tests/__pycache__/ex_smoothers.cpython-312.pyc,,
statsmodels/sandbox/nonparametric/tests/__pycache__/test_kernel_extras.cpython-312.pyc,,
statsmodels/sandbox/nonparametric/tests/__pycache__/test_smoothers.cpython-312.pyc,,
statsmodels/sandbox/nonparametric/tests/ex_gam_am_new.py,sha256=Xo4xvXCpUHGn3Euk-Pf3vAp3uJL4EQCd3IZvhUzxjF0,2400
statsmodels/sandbox/nonparametric/tests/ex_gam_new.py,sha256=k_IbyfFKyeUo1GNaKYfg3OdyyRoQVUF9r3yQWe_6ZXQ,3771
statsmodels/sandbox/nonparametric/tests/ex_smoothers.py,sha256=wCD4zaLiZVb1G9rrE_qJuX1sgzLtB7nU4LXHUHDpPCU,1343
statsmodels/sandbox/nonparametric/tests/test_kernel_extras.py,sha256=sa0eaZbAzLj-65jC8zMHXTc8xlR9APeD_VvZS_oA7LM,3531
statsmodels/sandbox/nonparametric/tests/test_smoothers.py,sha256=IXN7ZAKuEAPlCknSOqhT956iJGwt3Zd8iishQ0_ZDEw,3074
statsmodels/sandbox/panel/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/sandbox/panel/__pycache__/__init__.cpython-312.pyc,,
statsmodels/sandbox/panel/__pycache__/correlation_structures.cpython-312.pyc,,
statsmodels/sandbox/panel/__pycache__/mixed.cpython-312.pyc,,
statsmodels/sandbox/panel/__pycache__/panel_short.cpython-312.pyc,,
statsmodels/sandbox/panel/__pycache__/panelmod.cpython-312.pyc,,
statsmodels/sandbox/panel/__pycache__/random_panel.cpython-312.pyc,,
statsmodels/sandbox/panel/__pycache__/sandwich_covariance_generic.cpython-312.pyc,,
statsmodels/sandbox/panel/correlation_structures.py,sha256=XPqZm0WXb-7E32E-IH7OA4jYVYZeHgVIjX4JnCi3aag,5308
statsmodels/sandbox/panel/mixed.py,sha256=_68mZ8f8afm7WLED6za36muG0BXJbmVUia8Yhs33GYc,21548
statsmodels/sandbox/panel/panel_short.py,sha256=HyKU3rqZbdmpa0qQ37tqUXltxZOMvsSEU-M51mGW0wM,8302
statsmodels/sandbox/panel/panelmod.py,sha256=tLFUtTG9EQXZDW0TKt7obBZ7m2kZ6pMMg8djrg_nFO8,15004
statsmodels/sandbox/panel/random_panel.py,sha256=zxLXsdZpLczZYsdBwHdmUB-8GL0lIHfDnFCw7aixZck,5064
statsmodels/sandbox/panel/sandwich_covariance_generic.py,sha256=icbvcECEFmfNTVLQ1aCOL1hFk_L7nq-k-PbGagigm6o,3887
statsmodels/sandbox/panel/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/sandbox/panel/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/sandbox/panel/tests/__pycache__/test_random_panel.cpython-312.pyc,,
statsmodels/sandbox/panel/tests/test_random_panel.py,sha256=qaFvSz6fmh6wCwvv7As9Qlb3CUZM5qeQB1-WW5S4GjU,5558
statsmodels/sandbox/pca.py,sha256=zOjjQOimLXi2E-EPJFFZ1MNqxZtaot7U4jxeIz9nkHE,7260
statsmodels/sandbox/predict_functional.py,sha256=p0-BSD3XGuYTCAYgRB5LOSi9XA8l2o9fvf2Z02URwU4,16277
statsmodels/sandbox/regression/__init__.py,sha256=ylMkJJ_DAWUyXCwNqcalaiqzfMNfysMNc89ohNHv4CQ,99
statsmodels/sandbox/regression/__pycache__/__init__.cpython-312.pyc,,
statsmodels/sandbox/regression/__pycache__/anova_nistcertified.cpython-312.pyc,,
statsmodels/sandbox/regression/__pycache__/ar_panel.cpython-312.pyc,,
statsmodels/sandbox/regression/__pycache__/example_kernridge.cpython-312.pyc,,
statsmodels/sandbox/regression/__pycache__/gmm.cpython-312.pyc,,
statsmodels/sandbox/regression/__pycache__/kernridgeregress_class.cpython-312.pyc,,
statsmodels/sandbox/regression/__pycache__/ols_anova_original.cpython-312.pyc,,
statsmodels/sandbox/regression/__pycache__/onewaygls.cpython-312.pyc,,
statsmodels/sandbox/regression/__pycache__/penalized.cpython-312.pyc,,
statsmodels/sandbox/regression/__pycache__/predstd.cpython-312.pyc,,
statsmodels/sandbox/regression/__pycache__/runmnl.cpython-312.pyc,,
statsmodels/sandbox/regression/__pycache__/sympy_diff.cpython-312.pyc,,
statsmodels/sandbox/regression/__pycache__/tools.cpython-312.pyc,,
statsmodels/sandbox/regression/__pycache__/treewalkerclass.cpython-312.pyc,,
statsmodels/sandbox/regression/__pycache__/try_catdata.cpython-312.pyc,,
statsmodels/sandbox/regression/__pycache__/try_ols_anova.cpython-312.pyc,,
statsmodels/sandbox/regression/__pycache__/try_treewalker.cpython-312.pyc,,
statsmodels/sandbox/regression/anova_nistcertified.py,sha256=O_T_KXlwKDMakeRtZrzsfjlCjSIgEScRGJjEGYG-ySs,4122
statsmodels/sandbox/regression/ar_panel.py,sha256=-ljL8W9heppr2aHe2Hx3SDLR0y2zWBQmxKDpQ_OGsBs,3656
statsmodels/sandbox/regression/example_kernridge.py,sha256=TD1Bfszxj0WXQ4TQpjxLnaw7-b9FDZKy48CEGENt8b4,1262
statsmodels/sandbox/regression/gmm.py,sha256=J6VR3h81Gcgtfg8uEeK5CtTYhGcqZlYiv0wm2Qqtzj4,63811
statsmodels/sandbox/regression/kernridgeregress_class.py,sha256=NEuyfrTDDRLsjAXQg0iVhj3OvpJ3o9qubIvpQ57KgRg,7925
statsmodels/sandbox/regression/ols_anova_original.py,sha256=BWXERMaVUnVoIEDmEpqxK1ksIbfDxYu3allmNbcr0b0,11095
statsmodels/sandbox/regression/onewaygls.py,sha256=bpW9hpuKqeaekXvhzxRr7rPuxL5kPdWu_zN11c2xA0A,15477
statsmodels/sandbox/regression/penalized.py,sha256=e6M7ccY0qNu1bt_G1SjzdJbhGoMUY4mDCiO4SQY2NtE,17863
statsmodels/sandbox/regression/predstd.py,sha256=uSuyJuca9sYaqkjJuEs-ZK3UlZReR8ei64ZjjL_6PCY,3102
statsmodels/sandbox/regression/runmnl.py,sha256=FR57Ui30B_yM4FkHyIcObu5xx1c4sB_CprHDLG_iQGE,12327
statsmodels/sandbox/regression/sympy_diff.py,sha256=wten0Y6XNahNyECJMfBewTCGYQ9qc2ODTEjLdDMnYHw,1778
statsmodels/sandbox/regression/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/sandbox/regression/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/sandbox/regression/tests/__pycache__/results_gmm_griliches.cpython-312.pyc,,
statsmodels/sandbox/regression/tests/__pycache__/results_gmm_griliches_iter.cpython-312.pyc,,
statsmodels/sandbox/regression/tests/__pycache__/results_gmm_poisson.cpython-312.pyc,,
statsmodels/sandbox/regression/tests/__pycache__/results_ivreg2_griliches.cpython-312.pyc,,
statsmodels/sandbox/regression/tests/__pycache__/test_gmm.cpython-312.pyc,,
statsmodels/sandbox/regression/tests/__pycache__/test_gmm_poisson.cpython-312.pyc,,
statsmodels/sandbox/regression/tests/griliches76.dta,sha256=qixgQlaOXPQoEyj2zwbBL63XjTdI8fO1sJ3siF7DvwI,64736
statsmodels/sandbox/regression/tests/racd10data_with_transformed.csv,sha256=M_nDyru9SG1DTxVEZ3j8XVCIcCJ0xclQSvgWDp8JrKs,324886
statsmodels/sandbox/regression/tests/results_gmm_griliches.py,sha256=fn2CDsMwPYI0O8cYroEcczYe3c8wYQM7whKVE_7WPh0,14587
statsmodels/sandbox/regression/tests/results_gmm_griliches_iter.py,sha256=12Ju86odiUcWyaWUjxnFd41F11ONVM98GKaSVlJ4mJY,7278
statsmodels/sandbox/regression/tests/results_gmm_poisson.py,sha256=Xe-BB1gN2KDC9ebtwu5ok60x-oJwe1ZPTOGyEkSM3IY,25900
statsmodels/sandbox/regression/tests/results_ivreg2_griliches.py,sha256=TKtCJSvLvJzXVDE21FlSLSeewLVwmZkffEZbMJS-Hi0,37473
statsmodels/sandbox/regression/tests/test_gmm.py,sha256=IT8ZtrJMEUhQ4ZWdKDphrvI-tyyngqRN5jVlB9rcMUA,32247
statsmodels/sandbox/regression/tests/test_gmm_poisson.py,sha256=i_HLM5pMa6NiWp_9siKgCmVX7OS7TKaMXm3X0RTkaa8,13712
statsmodels/sandbox/regression/tools.py,sha256=qG2o7j0u0us7VOyGxkTMOhYaowlgdtPjMw9SYtfuhso,13187
statsmodels/sandbox/regression/treewalkerclass.py,sha256=oqvl7pG-rdhc9DRQAaBIp_7bfLwjmXvS3l7T5hG75Io,21945
statsmodels/sandbox/regression/try_catdata.py,sha256=ugnO0k7VwhthCsPhwST0T5eGzb9WgbKcsYL32XtrTRs,4830
statsmodels/sandbox/regression/try_ols_anova.py,sha256=5-aTg_ADv8cu1INIJ__XNtbyglxTiULSH9nwR5nT0uw,9496
statsmodels/sandbox/regression/try_treewalker.py,sha256=-i2njSe1Vp8y8_FJLu3Rs7961_IqzXjyt33du0-cORg,4502
statsmodels/sandbox/rls.py,sha256=lqN2Gy1oW8g7eyMpBevWGBkBqGteNkY4b939KgwxJx0,5286
statsmodels/sandbox/stats/__init__.py,sha256=PtH0XIDjfLE06_UUKetf50m4t8z5M9csY-cLzkqlcRY,687
statsmodels/sandbox/stats/__pycache__/__init__.cpython-312.pyc,,
statsmodels/sandbox/stats/__pycache__/contrast_tools.cpython-312.pyc,,
statsmodels/sandbox/stats/__pycache__/diagnostic.cpython-312.pyc,,
statsmodels/sandbox/stats/__pycache__/ex_newtests.cpython-312.pyc,,
statsmodels/sandbox/stats/__pycache__/multicomp.cpython-312.pyc,,
statsmodels/sandbox/stats/__pycache__/runs.cpython-312.pyc,,
statsmodels/sandbox/stats/__pycache__/stats_dhuard.cpython-312.pyc,,
statsmodels/sandbox/stats/__pycache__/stats_mstats_short.cpython-312.pyc,,
statsmodels/sandbox/stats/contrast_tools.py,sha256=HncjaKjYqbsgJQ4JydoC8h-30ZyW0eUnEmyrOkisv-0,29695
statsmodels/sandbox/stats/diagnostic.py,sha256=_EWlm77jMqnzZu-fA1X1FnP0z1OzaXw827Iizkdt5Os,1055
statsmodels/sandbox/stats/ex_newtests.py,sha256=MfoDJ0mJNoLepNAbOb5P4WkcnYOCD39zA6zQ0n9of6c,848
statsmodels/sandbox/stats/multicomp.py,sha256=lpRmMwqC0NgDmHrfWVmeVIn9TIoPW3U2Ri9aB49R8Hs,71060
statsmodels/sandbox/stats/runs.py,sha256=n3N4HZBht6esW1w3otIny9OsoZJxxt34DKVuX-62doQ,20959
statsmodels/sandbox/stats/stats_dhuard.py,sha256=BMHSczAANc9TObDSufANvzJIZ2F9EdYdUNKghV-LEbw,10421
statsmodels/sandbox/stats/stats_mstats_short.py,sha256=MPKphT52JczVkC0-w-mFMSgWDMOchVs5-0oYCWbt7cc,15251
statsmodels/sandbox/stats/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/sandbox/stats/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/sandbox/stats/tests/__pycache__/test_multicomp.cpython-312.pyc,,
statsmodels/sandbox/stats/tests/__pycache__/test_runs.cpython-312.pyc,,
statsmodels/sandbox/stats/tests/test_multicomp.py,sha256=6hDOuH5zv8LLJeh-LSPvD00L222Lxv3s2A3FcRP00K4,452
statsmodels/sandbox/stats/tests/test_runs.py,sha256=UoZlr4koEuvk3uJvIFlRurQauc9RmOURatw2bb4bsSY,932
statsmodels/sandbox/sysreg.py,sha256=uBZ71_lr9Des6T6K2x7WwZdKIrM5_2BLFfbHlSBs6uE,14979
statsmodels/sandbox/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/sandbox/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/sandbox/tests/__pycache__/maketests_mlabwrap.cpython-312.pyc,,
statsmodels/sandbox/tests/__pycache__/savervs.cpython-312.pyc,,
statsmodels/sandbox/tests/__pycache__/test_gam.cpython-312.pyc,,
statsmodels/sandbox/tests/__pycache__/test_pca.cpython-312.pyc,,
statsmodels/sandbox/tests/__pycache__/test_predict_functional.cpython-312.pyc,,
statsmodels/sandbox/tests/maketests_mlabwrap.py,sha256=CFguEdYr3B6Ea5JBaw43M7Atw_JgG1086zpqfL6zRug,9251
statsmodels/sandbox/tests/savervs.py,sha256=aKnAuD3fNwB0N5PzX6eysnKC9ZGdY-H-C4mGp8LpMLU,1200
statsmodels/sandbox/tests/test_gam.py,sha256=Fid13ZH32XwrZ54JpRrNwPtV3OE5xPN9koV45doUVfg,11247
statsmodels/sandbox/tests/test_pca.py,sha256=5rzF1gdSbv_-uEv0dmMJbYMr-hiHn49PoN_KeBboYOw,2550
statsmodels/sandbox/tests/test_predict_functional.py,sha256=OUTDZDvKd0DVYJfEEwh_nzYIKKXywDC9Q-FQHkf8HHg,13146
statsmodels/sandbox/tools/__init__.py,sha256=G08XS6cx2stf_yy0yMyxWylgnvTUCGxEmr3ZHV-8RmI,225
statsmodels/sandbox/tools/__pycache__/__init__.cpython-312.pyc,,
statsmodels/sandbox/tools/__pycache__/cross_val.cpython-312.pyc,,
statsmodels/sandbox/tools/__pycache__/mctools.cpython-312.pyc,,
statsmodels/sandbox/tools/__pycache__/tools_pca.cpython-312.pyc,,
statsmodels/sandbox/tools/__pycache__/try_mctools.cpython-312.pyc,,
statsmodels/sandbox/tools/cross_val.py,sha256=IgdcJYIzeNacdFozfsj68ogCG_mZsuo-Far41r_k0uY,12174
statsmodels/sandbox/tools/mctools.py,sha256=VpDmk8MovGRgrzxhXfaBf4S-fg2kjDO7MZGD1emgQJw,17396
statsmodels/sandbox/tools/tools_pca.py,sha256=0hvkzLYL8u8vw7X16iYQjqSg_yHTm2bVwTXj7iy1mH8,4252
statsmodels/sandbox/tools/try_mctools.py,sha256=cBBSQ6PhAL46WQuRZD-4KQuEXm-GibVN4ZHiZTOF5-c,1987
statsmodels/sandbox/tsa/__init__.py,sha256=rzo7Pr_p_RdU6pVST-NetsK8lv7Kg449cn4fcaHEPSg,892
statsmodels/sandbox/tsa/__pycache__/__init__.cpython-312.pyc,,
statsmodels/sandbox/tsa/__pycache__/diffusion.cpython-312.pyc,,
statsmodels/sandbox/tsa/__pycache__/diffusion2.cpython-312.pyc,,
statsmodels/sandbox/tsa/__pycache__/example_arma.cpython-312.pyc,,
statsmodels/sandbox/tsa/__pycache__/fftarma.cpython-312.pyc,,
statsmodels/sandbox/tsa/__pycache__/movstat.cpython-312.pyc,,
statsmodels/sandbox/tsa/__pycache__/try_arma_more.cpython-312.pyc,,
statsmodels/sandbox/tsa/__pycache__/try_fi.cpython-312.pyc,,
statsmodels/sandbox/tsa/__pycache__/try_var_convolve.cpython-312.pyc,,
statsmodels/sandbox/tsa/__pycache__/varma.cpython-312.pyc,,
statsmodels/sandbox/tsa/diffusion.py,sha256=FUP-yxKJNvqQN0nHEk5xzR98L9awurJaoCe4nVf4Bj4,19318
statsmodels/sandbox/tsa/diffusion2.py,sha256=hKdPtQKUroqYjfQ1FO0TAqm2-WCs1WfJGE5jrFqdyrE,13817
statsmodels/sandbox/tsa/example_arma.py,sha256=De_OG7Ri558eBP-wW1sI3QyailSNUeUh6cmqSER06t4,11892
statsmodels/sandbox/tsa/fftarma.py,sha256=yPwgxxhQTe8QixIEnFk-m9lN6-NzgmBqJ8v2AHraIhE,16783
statsmodels/sandbox/tsa/movstat.py,sha256=pVCKrEzeXdj6uyY271YnLjhjbHgm4ylfNBTxTOoXtOE,15222
statsmodels/sandbox/tsa/try_arma_more.py,sha256=qVcRryqllkYlPHTMSGxu-f5bLW1GZyEsMLLvAXqt_cg,2916
statsmodels/sandbox/tsa/try_fi.py,sha256=D2BMWzVo_aiDX6BjqeT52OJbliS6YpnbaePa0MR_8Bw,2614
statsmodels/sandbox/tsa/try_var_convolve.py,sha256=iS9kHEJSIh9Zg0Ej7KdZSjDNyywhDLoAHDt00psUXVg,9456
statsmodels/sandbox/tsa/varma.py,sha256=wbNc3evZku3cO46n7tmamTRQdokDeoZpTF9gG4jJR7c,5066
statsmodels/setup.cfg,sha256=uGr5uyUtaG2ZI3QleWo86C_d0G0V6SZbNvBjr6t2Y3A,12410
statsmodels/src/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/src/__pycache__/__init__.cpython-312.pyc,,
statsmodels/stats/__init__.py,sha256=FNIIWqMJDn_RaWzLhb1qyMHw6Oh_msp2OmLojqBG2_M,82
statsmodels/stats/__pycache__/__init__.cpython-312.pyc,,
statsmodels/stats/__pycache__/_adnorm.cpython-312.pyc,,
statsmodels/stats/__pycache__/_delta_method.cpython-312.pyc,,
statsmodels/stats/__pycache__/_diagnostic_other.cpython-312.pyc,,
statsmodels/stats/__pycache__/_inference_tools.cpython-312.pyc,,
statsmodels/stats/__pycache__/_knockoff.cpython-312.pyc,,
statsmodels/stats/__pycache__/_lilliefors.cpython-312.pyc,,
statsmodels/stats/__pycache__/_lilliefors_critical_values.cpython-312.pyc,,
statsmodels/stats/__pycache__/anova.cpython-312.pyc,,
statsmodels/stats/__pycache__/api.cpython-312.pyc,,
statsmodels/stats/__pycache__/base.cpython-312.pyc,,
statsmodels/stats/__pycache__/contingency_tables.cpython-312.pyc,,
statsmodels/stats/__pycache__/contrast.cpython-312.pyc,,
statsmodels/stats/__pycache__/correlation_tools.cpython-312.pyc,,
statsmodels/stats/__pycache__/descriptivestats.cpython-312.pyc,,
statsmodels/stats/__pycache__/diagnostic.cpython-312.pyc,,
statsmodels/stats/__pycache__/diagnostic_gen.cpython-312.pyc,,
statsmodels/stats/__pycache__/dist_dependence_measures.cpython-312.pyc,,
statsmodels/stats/__pycache__/effect_size.cpython-312.pyc,,
statsmodels/stats/__pycache__/gof.cpython-312.pyc,,
statsmodels/stats/__pycache__/inter_rater.cpython-312.pyc,,
statsmodels/stats/__pycache__/knockoff_regeffects.cpython-312.pyc,,
statsmodels/stats/__pycache__/mediation.cpython-312.pyc,,
statsmodels/stats/__pycache__/meta_analysis.cpython-312.pyc,,
statsmodels/stats/__pycache__/moment_helpers.cpython-312.pyc,,
statsmodels/stats/__pycache__/multicomp.cpython-312.pyc,,
statsmodels/stats/__pycache__/multitest.cpython-312.pyc,,
statsmodels/stats/__pycache__/multivariate.cpython-312.pyc,,
statsmodels/stats/__pycache__/multivariate_tools.cpython-312.pyc,,
statsmodels/stats/__pycache__/nonparametric.cpython-312.pyc,,
statsmodels/stats/__pycache__/oaxaca.cpython-312.pyc,,
statsmodels/stats/__pycache__/oneway.cpython-312.pyc,,
statsmodels/stats/__pycache__/outliers_influence.cpython-312.pyc,,
statsmodels/stats/__pycache__/power.cpython-312.pyc,,
statsmodels/stats/__pycache__/proportion.cpython-312.pyc,,
statsmodels/stats/__pycache__/rates.cpython-312.pyc,,
statsmodels/stats/__pycache__/regularized_covariance.cpython-312.pyc,,
statsmodels/stats/__pycache__/robust_compare.cpython-312.pyc,,
statsmodels/stats/__pycache__/sandwich_covariance.cpython-312.pyc,,
statsmodels/stats/__pycache__/stattools.cpython-312.pyc,,
statsmodels/stats/__pycache__/tabledist.cpython-312.pyc,,
statsmodels/stats/__pycache__/weightstats.cpython-312.pyc,,
statsmodels/stats/_adnorm.py,sha256=jtdx-UVuTXQXbpY-BMMhEE3X7MHCMlh2jX-4VbcnaME,4533
statsmodels/stats/_delta_method.py,sha256=Fu3vkN49BSJvgMWiEKBQ-ewe5JbyQ-A5H_hv9fpkegk,10300
statsmodels/stats/_diagnostic_other.py,sha256=-2pfhSLsvlmG_q9KzR3eFKYVK0dHcjz7qtkDXNn0BJ8,41077
statsmodels/stats/_inference_tools.py,sha256=kpB6csyEfsicWBEJ-pKpfY7zfps0KwQkhyLDNYqM4e0,2839
statsmodels/stats/_knockoff.py,sha256=Z-k-t2dCg6OoYTFlCMwtFWFqKpmqPBbneViQSRy69EA,7635
statsmodels/stats/_lilliefors.py,sha256=fyP-Dcp-MnKz51GgH5HFZIl2sIJbF8MppSf12hhZ6kE,10170
statsmodels/stats/_lilliefors_critical_values.py,sha256=r9Aj7B4iuK2rdPEyMW9bliwYPPWGOHQu2A81e-Xq6P4,14300
statsmodels/stats/anova.py,sha256=N0e_G6rfxjXDfra3iqSY8_Cl0PhZ_4AaWQsuKIXy5z4,23608
statsmodels/stats/api.py,sha256=K3qFOrZJRFixQZ249dDqIrdr9o-Kwb4Z_eKhzBT--fw,7968
statsmodels/stats/base.py,sha256=twR4JlG9GWwOFnprp1ya0vd8eC8c3i7e9Fy-ZfgWLbE,4239
statsmodels/stats/contingency_tables.py,sha256=B0MpmNnqhyi_RX1OL2UiL0OikkJqD9o0DzkuIRRKYiM,45660
statsmodels/stats/contrast.py,sha256=7iDxbodMZ1zIevoKBxVuFgqLuOzbyIDqlPGWp-8Xwn0,28513
statsmodels/stats/correlation_tools.py,sha256=nRVE0o59i3NSkuwi-LdDOA5PqrmC5Xi00cqHqfUTF9Q,33301
statsmodels/stats/descriptivestats.py,sha256=yNbB2sPF98dVIFW5XFrHLGFXdFevOy64v6NJrLjYi5g,22321
statsmodels/stats/diagnostic.py,sha256=vAAccFhH0kBUhziC90OreURM8J21vasaYWWK2HtjT98,61586
statsmodels/stats/diagnostic_gen.py,sha256=4P-PgV2aNR912C9Q_MqKBWxDXiiusDEtC1V14HjySYo,8411
statsmodels/stats/dist_dependence_measures.py,sha256=9tWU6SrCu2yKzbnLVLKscshOZInyJp6dqno8l27twWA,18159
statsmodels/stats/effect_size.py,sha256=VYSw8MEmG6Evj7Et6CjhOf9cMV8W-vDhWA1DpT5OHng,5079
statsmodels/stats/gof.py,sha256=38d9dKKBgq0_uMPfVWoesIqhREOJFGkssW9RaWYwzc4,17138
statsmodels/stats/inter_rater.py,sha256=teXoYSRYKNKHJLuyFG6gjdNxfQ05IXqR36PxFJWQNWk,19462
statsmodels/stats/knockoff_regeffects.py,sha256=s5MxFYCWzPJUdfI0wyHRd451UEYvFXZWIZ41LPVHKa8,5503
statsmodels/stats/libqsturng/CH.r,sha256=9UQC2KbYyuoaaEpm3Q0QGJIjXT1tBFEMtTXUov6TbS4,2530
statsmodels/stats/libqsturng/LICENSE.txt,sha256=lzyD3g6B6fai1LwWsxADKxjKy-e_KxYhlAaclLFcwM0,1595
statsmodels/stats/libqsturng/__init__.py,sha256=xLljJqhvbl7xe6CPO8pvJu0Koy8u5oG59s0GtGKZsHk,204
statsmodels/stats/libqsturng/__pycache__/__init__.cpython-312.pyc,,
statsmodels/stats/libqsturng/__pycache__/make_tbls.cpython-312.pyc,,
statsmodels/stats/libqsturng/__pycache__/qsturng_.cpython-312.pyc,,
statsmodels/stats/libqsturng/make_tbls.py,sha256=Eo2npTvAXH0oiSLB8fWfWQk7O75pUDuaVenxG2yyWbM,63951
statsmodels/stats/libqsturng/qsturng_.py,sha256=Wnp7dcsIFY0VqvsRicTP6cW_MG5dKaHblpga2VSXNg4,54241
statsmodels/stats/libqsturng/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/stats/libqsturng/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/stats/libqsturng/tests/__pycache__/test_qsturng.cpython-312.pyc,,
statsmodels/stats/libqsturng/tests/bootleg.dat,sha256=90ITItfE--Q33SUPMCtCBj1MUmZ1NvuZh7z1mzfwOag,292257
statsmodels/stats/libqsturng/tests/test_qsturng.py,sha256=Og5iYC6GeKHP-UfRE4LeDY8Izl_LjI19TgD2gwryX9s,9146
statsmodels/stats/mediation.py,sha256=BhcJfPm0GhyON3r9zCuHZYZ8USYvdMudmjcoAwW1f5U,16810
statsmodels/stats/meta_analysis.py,sha256=X03PDGv64V2kXAPDyFjKhZD2i7IRt7ZrtU7nTIqD4RM,27311
statsmodels/stats/moment_helpers.py,sha256=3dHKft2_OlpimwYc-ofrLOTCO4meIl-OOTgRonEV62A,8949
statsmodels/stats/multicomp.py,sha256=hgEY1wsAHpfWtZelFffcFoIU-XjR1iKEXnC3IVmhFJQ,1066
statsmodels/stats/multitest.py,sha256=vlg37PK8FglPhFpnspy8GoxNQx-VckDEH5pSm4owIl0,28276
statsmodels/stats/multivariate.py,sha256=6tn0qoKkPVWF5F2-MH_fTIksC61eNz7jyyHilgdMubY,19820
statsmodels/stats/multivariate_tools.py,sha256=KTVoactUdUusKPjvu_GMKIFwPQRxnzXsm_enYBPcudA,7420
statsmodels/stats/nonparametric.py,sha256=ctmvXESUD9fBHc6nzDVTFUHe5Ff6VMTi1J37nNa06Ns,23948
statsmodels/stats/oaxaca.py,sha256=NaSt7Rg9xxCO8RJ0jdXyyZQMxPg_NjHch6WekYTMpQA,21866
statsmodels/stats/oneway.py,sha256=MyXZPJgvjulQQGfJt16IxOIh5v00jP_w3B45HDjAGts,47006
statsmodels/stats/outliers_influence.py,sha256=esL-DmVOR8Utbg5CiUh1VWjv_oKqtXWe_ZElmxgLZIg,57114
statsmodels/stats/power.py,sha256=9omu-Qkp3TIA-mGf0g0-CoK4t0pmkjLopn1y7QgGxec,65368
statsmodels/stats/proportion.py,sha256=bm-j9mypKRdsQEH5M_6Q0t1Q4cLS5LA2YM5_BnnibTo,87972
statsmodels/stats/rates.py,sha256=OzuMMbzKQbbBfcM1UAVxHkSyKN2NBbZ4pzXHfUYMUlM,79707
statsmodels/stats/regularized_covariance.py,sha256=uPvVaRXfCBjxoEbK_kVgMNWSx_Hak4saLzFnKCShL-g,4974
statsmodels/stats/robust_compare.py,sha256=6tm0KVSba0x3P0gJJefRw8FaSaNuD3NSqFSW1M0T2d0,10157
statsmodels/stats/sandwich_covariance.py,sha256=7tXAHjLufHQWTAPhXIBn0zFZwmw2T6Mq_sWe4C8YFdk,28772
statsmodels/stats/stattools.py,sha256=cKJZ2mxnAWz6O9U8fTjW-tcyFz2wMxTakzCg2opi80o,14682
statsmodels/stats/tabledist.py,sha256=RiSMz_5sAJ2ORCXYp5XWFTNWxDpsMeq5uoKAiAlq3bs,10005
statsmodels/stats/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/stats/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_anova.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_anova_rm.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_base.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_contingency_tables.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_contrast.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_correlation.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_corrpsd.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_deltacov.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_descriptivestats.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_diagnostic.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_diagnostic_other.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_dist_dependant_measures.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_effectsize.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_gof.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_groups_sw.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_influence.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_inter_rater.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_knockoff.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_lilliefors.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_mediation.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_meta.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_moment_helpers.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_multi.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_multivariate.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_nonparametric.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_oaxaca.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_oneway.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_outliers_influence.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_pairwise.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_panel_robustcov.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_power.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_proportion.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_qsturng.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_rates_poisson.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_regularized_covariance.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_robust_compare.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_sandwich.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_statstools.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_tabledist.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_tost.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_weightstats.cpython-312.pyc,,
statsmodels/stats/tests/results/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/stats/tests/results/__pycache__/__init__.cpython-312.pyc,,
statsmodels/stats/tests/results/__pycache__/lilliefors_critical_value_simulation.cpython-312.pyc,,
statsmodels/stats/tests/results/__pycache__/results_meta.cpython-312.pyc,,
statsmodels/stats/tests/results/__pycache__/results_multinomial_proportions.cpython-312.pyc,,
statsmodels/stats/tests/results/__pycache__/results_panelrobust.cpython-312.pyc,,
statsmodels/stats/tests/results/__pycache__/results_power.cpython-312.pyc,,
statsmodels/stats/tests/results/__pycache__/results_proportion.cpython-312.pyc,,
statsmodels/stats/tests/results/__pycache__/results_rates.cpython-312.pyc,,
statsmodels/stats/tests/results/binary_constrict.csv,sha256=8Ze25lby3H24-DjSV-Kpu2GrM1LfLng4L_9QprW6JxY,2767
statsmodels/stats/tests/results/bootleg.csv,sha256=90ITItfE--Q33SUPMCtCBj1MUmZ1NvuZh7z1mzfwOag,292257
statsmodels/stats/tests/results/contingency_table_r_results.csv,sha256=MgEWH0ST7EPCoL0ZYvM_aW8CEUiMxUC3-FL-jlRZaBg,865
statsmodels/stats/tests/results/data.dat,sha256=oiPnaUFYjxoQRDkbqgbrrWLEGXDs-Pw_80Gbv27niqo,1830
statsmodels/stats/tests/results/framing.csv,sha256=MAg7_Mb1Rg6R64hBaM5LgttH9n7jVVPPupSPxfcjdcE,24839
statsmodels/stats/tests/results/influence_lsdiag_R.json,sha256=j6XAc5nnLQaFIJaSKV0Vktessx-iBsPPvyeTbP9M0iU,19806
statsmodels/stats/tests/results/influence_measures_R.csv,sha256=xnEIi0pqheJfW3IxAztchd-vrm11UZ3lUaO8BRAoNFE,28619
statsmodels/stats/tests/results/influence_measures_bool_R.csv,sha256=VDvj8QWY6VS4NUsXFpVUvRPZVN8uOxjUZsOco28_HT8,9825
statsmodels/stats/tests/results/lilliefors_critical_value_simulation.py,sha256=cLY5ihivIIu1TjisEdlOJpuIVC_uonosfeUZCFDPsDU,5443
statsmodels/stats/tests/results/results_influence_logit.csv,sha256=pxlSWxDuA-iudJ9CTSh7kDjGYWRLS4IsjzK56DrKBY0,3741
statsmodels/stats/tests/results/results_meta.py,sha256=-RO4HU5vPPm6pEyxb5L3GXXfE0S6ExMfVfQRsJUoIdA,18978
statsmodels/stats/tests/results/results_multinomial_proportions.py,sha256=lM9PmSTPHmQwJ9Unu2w-qE_gKfPEw7z4WsfZHQ_8RIs,2736
statsmodels/stats/tests/results/results_panelrobust.py,sha256=CT69A5FahCP9jPO5rULKaV8VMCeedKBJQWdVCEKXzfc,2071
statsmodels/stats/tests/results/results_power.py,sha256=qPiHHT4VjCP5L9unRjOGNrP11EigJcfUWI_HrbSMHa0,4651
statsmodels/stats/tests/results/results_proportion.py,sha256=TbFJSM9CqBfbnKamdSaqtKXk9Qev6Cd8c4662-92TNU,5163
statsmodels/stats/tests/results/results_rates.py,sha256=nWNjRA3tp3P1hRVWZ8XrkuGc4sPziboQH1YLFu90fDY,1349
statsmodels/stats/tests/results/wspec1.csv,sha256=nKZvB3nb02xCEp_e2XxlqxFqQD3tlMY4CXvWLmm7YnU,835
statsmodels/stats/tests/results/wspec2.csv,sha256=kp2tgPBx5dElaV2AhlsTjkAsJo971bLU3G4XkASC5IM,1043
statsmodels/stats/tests/results/wspec3.csv,sha256=IphKF6gTwu63jW9PVJPHn8pePZcpyxLGN2DOxpW7790,941
statsmodels/stats/tests/results/wspec4.csv,sha256=7RBPoXLbnw60PyLG5s9iQ1G9EcNzoAqA0YQXz8e28NM,1257
statsmodels/stats/tests/test_anova.py,sha256=OTEzkVfY8pNzGNttRTrZQhRGD_kLyL6Vs5csyB4QZRk,19402
statsmodels/stats/tests/test_anova_rm.py,sha256=QYfn1MKUidn0Ii8uN4U_igd4xPjeDtG0J_xzcxtnVxg,7728
statsmodels/stats/tests/test_base.py,sha256=cGFMth5FqoIR5L2iQ-hJQBqXu6182YGK3gxmWcqLCzs,1761
statsmodels/stats/tests/test_contingency_tables.py,sha256=7GcFeakrCzfpjBDe5bSrIfkZYgaMMiLUsD919o04ns0,20928
statsmodels/stats/tests/test_contrast.py,sha256=puBULtFKOdDAIrt5qZ7t9D2xCQ4OqzaRQJYAiTwkaLQ,2497
statsmodels/stats/tests/test_correlation.py,sha256=0RURPaQDzpxhwyU6-_2R4MCEZcye-RmMDQ1SSf4FSik,1782
statsmodels/stats/tests/test_corrpsd.py,sha256=CnNR9t1jl5L7tCtK-8yJ5mscM4Mq7PUjkSs_wphiNVk,16866
statsmodels/stats/tests/test_data.txt,sha256=lciL1EWV3ZvKPUWo7ZRX-vW95vpU8s2-Z4woS4fDX0Q,220000
statsmodels/stats/tests/test_deltacov.py,sha256=bgJN-aYNJlGGLC46urBeZb5Ujt1eIUOkNzdkdIZlSCg,4118
statsmodels/stats/tests/test_descriptivestats.py,sha256=dhECZxjkE5SOWaWpvJfyR_23ld_dFpN0oWLtDEuPmqU,5830
statsmodels/stats/tests/test_diagnostic.py,sha256=WWx2Uvdb8FfJrAQTlNpDnnqOhouFrVhRtcl2OZ6grko,63613
statsmodels/stats/tests/test_diagnostic_other.py,sha256=KHG3bmiwnZkVNE9onceyvqd2PFuJPRqq62_eR4kXydE,9317
statsmodels/stats/tests/test_dist_dependant_measures.py,sha256=Mb71M76T_DRnVH-FQwBTtAxtod3YYNdM-DtzASKHSlE,7555
statsmodels/stats/tests/test_effectsize.py,sha256=18qhVFi8qi8qeqABc57-wjIWFdwxMTArkA5oKZ-KM0k,2334
statsmodels/stats/tests/test_gof.py,sha256=gcg5KZzMppqFeqQc1dWHsrVCBG_gm8DATM3tFSenB18,3503
statsmodels/stats/tests/test_groups_sw.py,sha256=tXywYed10fJUUes7xqIQv6SRgF-taAZmryYdOcYCLHg,2704
statsmodels/stats/tests/test_influence.py,sha256=Oug-Su6bmjDWpAA3blsL4RboZpt1i4aOt0y_tKdB6bI,12002
statsmodels/stats/tests/test_inter_rater.py,sha256=KyFWwzlEqmlBWjhmHa9B_b4hdiYzUCfqydYxTKtxOq4,13133
statsmodels/stats/tests/test_knockoff.py,sha256=p2X-ZoLcWimuM2LP_F2eKmg5fXwPuBn2WyJdT-Vx4_g,4525
statsmodels/stats/tests/test_lilliefors.py,sha256=mCssfH1eLFJMPApchDwTrHRg7wJqLHamxP2v0Y86hGQ,5255
statsmodels/stats/tests/test_mediation.py,sha256=gUJ8LlmiQAszU-Y01_vNd4BYBwyz2okneJFvxiD4WU0,12569
statsmodels/stats/tests/test_meta.py,sha256=VB42lpsRT5vZl1vjewHOYsHjST3g0nqDMLmwcDlhESE,15189
statsmodels/stats/tests/test_moment_helpers.py,sha256=7gBs62MnPVP45w38yKnA5qLX66i384icT8ii_db7yxc,4822
statsmodels/stats/tests/test_multi.py,sha256=NxjozuriwTvuhqufpySUx8Ql3lLBtqpqFDByG-qMVOY,24801
statsmodels/stats/tests/test_multivariate.py,sha256=bGJu273I17Kxj0xNQ2svPj2nSLltJEINTvVxoh4UTDY,12042
statsmodels/stats/tests/test_nonparametric.py,sha256=4CcdME5kaI7KK6XOjah5C89W-jawxnLv3KU6yGVrN9g,19094
statsmodels/stats/tests/test_oaxaca.py,sha256=AXPm3kl4vmYpLzREq5LAlOcdLLKjrPh-Zaz0vBHi7FA,12849
statsmodels/stats/tests/test_oneway.py,sha256=PMd9twM7K44I4BtiM-0jiVPtEH5I_Uom9ZFsL6sc5h8,22645
statsmodels/stats/tests/test_outliers_influence.py,sha256=CdKzoqSTSXepMdJuGQwjTKQhDpkloW8C7Fi5k8mll2k,986
statsmodels/stats/tests/test_pairwise.py,sha256=DayOtozN08-1j0ZVSi1YY5R-rvUZfZmTjyvJwV0mY6E,13256
statsmodels/stats/tests/test_panel_robustcov.py,sha256=l4VOEc3CXbq5YT_vcFRO3ZuXMpyofV-CCGr-iDy9uk8,2784
statsmodels/stats/tests/test_power.py,sha256=wj-I54TbTe77fj_85_8NpImJFVE3CZYHHbsZlJ5-F4s,29405
statsmodels/stats/tests/test_proportion.py,sha256=pcMWqz7-VTZ_dHtj4Gfipe9IbxZyenK2SEnGf19pHW4,44071
statsmodels/stats/tests/test_qsturng.py,sha256=mBgUCcozDAzPGGblDivnwi2r395U4oGdzwkmEadjJq0,848
statsmodels/stats/tests/test_rates_poisson.py,sha256=FxgADnKx9BRNL9OWYgyUiv0vahxtHVhVHMbZhmZtRCw,44464
statsmodels/stats/tests/test_regularized_covariance.py,sha256=OxxxWEABaLeDjpfaJ62c2M_GTw1bDlZt3dxF-BndrtY,1569
statsmodels/stats/tests/test_robust_compare.py,sha256=6znnqN-M_EUix--WQSCCaSVAYdNvFkeOvH_BZzfEprU,11304
statsmodels/stats/tests/test_sandwich.py,sha256=qK5CqZS7t9sjRROvM_2jDAhCIdTquuNlhQCnW9xQzeQ,3884
statsmodels/stats/tests/test_statstools.py,sha256=6cTV97xPCJoHtCd9IDVidWmXsrf9dxeTLMskEvAlEBA,11755
statsmodels/stats/tests/test_tabledist.py,sha256=Ps5ZxouFWM6P8ucnR4DLY6tj8AizeTt3eyf_LzL5R-U,4371
statsmodels/stats/tests/test_tost.py,sha256=iCMnwIrc78EZWV6eiy6Wzjt4G5yv6AvQsr7y_eA0A0E,24760
statsmodels/stats/tests/test_weightstats.py,sha256=7FZGT69GtznulA0Nm7nO-jn83rWyqRfEZS86_LseSYE,31041
statsmodels/stats/weightstats.py,sha256=MWucWvnhpD8-8F3Cp4PQcJjRaytRMhDpM5S9I9a_ts0,57066
statsmodels/tests/__init__.py,sha256=cOHpb4aBMaxo_VBd760yq7PnCu5P-uRVDv_C-S8s5KA,49
statsmodels/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tests/__pycache__/test_package.cpython-312.pyc,,
statsmodels/tests/__pycache__/test_x13.cpython-312.pyc,,
statsmodels/tests/test_package.py,sha256=TCCe9fnHHIWX6D2XgGQUBzwDa1MGn_ywZmCzJ9sBk44,1152
statsmodels/tests/test_x13.py,sha256=YA2UAycIo6dDbGB9YiBZ1YjFChC6uBNJ3r1wicQZ2Z4,290
statsmodels/tools/__init__.py,sha256=eLKsAO1zBZDVA2HMCqaYztkURnGX7fO1t8zEoZyVYLA,181
statsmodels/tools/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tools/__pycache__/_test_runner.cpython-312.pyc,,
statsmodels/tools/__pycache__/_testing.cpython-312.pyc,,
statsmodels/tools/__pycache__/catadd.cpython-312.pyc,,
statsmodels/tools/__pycache__/data.cpython-312.pyc,,
statsmodels/tools/__pycache__/decorators.cpython-312.pyc,,
statsmodels/tools/__pycache__/docstring.cpython-312.pyc,,
statsmodels/tools/__pycache__/eval_measures.cpython-312.pyc,,
statsmodels/tools/__pycache__/grouputils.cpython-312.pyc,,
statsmodels/tools/__pycache__/linalg.cpython-312.pyc,,
statsmodels/tools/__pycache__/numdiff.cpython-312.pyc,,
statsmodels/tools/__pycache__/parallel.cpython-312.pyc,,
statsmodels/tools/__pycache__/print_version.cpython-312.pyc,,
statsmodels/tools/__pycache__/rng_qrng.cpython-312.pyc,,
statsmodels/tools/__pycache__/rootfinding.cpython-312.pyc,,
statsmodels/tools/__pycache__/sequences.cpython-312.pyc,,
statsmodels/tools/__pycache__/sm_exceptions.cpython-312.pyc,,
statsmodels/tools/__pycache__/testing.cpython-312.pyc,,
statsmodels/tools/__pycache__/tools.cpython-312.pyc,,
statsmodels/tools/__pycache__/transform_model.cpython-312.pyc,,
statsmodels/tools/__pycache__/typing.cpython-312.pyc,,
statsmodels/tools/__pycache__/web.cpython-312.pyc,,
statsmodels/tools/_test_runner.py,sha256=TwoLXAP0yD8fHm0JNqIE1FlHkpxNxeAOQJLHB5cWUng,947
statsmodels/tools/_testing.py,sha256=z5vt8SHcYI7gcNGCdX1ThzEKVJRpDCUol7iyuhcogLI,5724
statsmodels/tools/catadd.py,sha256=vr8ny3yveVrZTMLZrfxaskAdp5u-DOcIIYTG1euUeNI,1047
statsmodels/tools/data.py,sha256=ov41UDlx0Lbk_AFDqCLbK7KPZmOb82B_ND6guwy-yf0,3633
statsmodels/tools/decorators.py,sha256=E9jrRcx-eKwrC4gp2hN-NG7iXz_v-EoZzM_xM7v8am0,4476
statsmodels/tools/docstring.py,sha256=BlXwH9w9Du1OZtQuT10j_oQ0W2XH-8jaAmNKpiSgZSQ,22686
statsmodels/tools/eval_measures.py,sha256=sLiWdNElxtnu3YYAgGZXhqfyj1GMvC9iHsnElTyucCE,16398
statsmodels/tools/grouputils.py,sha256=ZmSY-jop4vy3wKZ4LKLVeeqaVkHGTr47Ukqi5tKPcfI,18823
statsmodels/tools/linalg.py,sha256=H3vcpwqETKyLoVL9L9aciWZljsqBp_DzlrgJmHovMKw,4979
statsmodels/tools/numdiff.py,sha256=fxhiR72iREf7MRtXi2EXX0WItc_LIPbCMBlPhsGH2YI,15672
statsmodels/tools/parallel.py,sha256=Mbf9vtdK6yzaJGL3bLvFyhF32k6cofO0DXcgmKdX4VE,2223
statsmodels/tools/print_version.py,sha256=wpAHY3v1dwqOouu5az8CtHrrP0cM4Fx1JfIOaRXn_ZI,8871
statsmodels/tools/rng_qrng.py,sha256=6s1ms8GlHOUnZWQ8_sK97QteYrIS_popOade8SFRF5Q,1910
statsmodels/tools/rootfinding.py,sha256=9OVTnd8wRTWTiBn2OBzjM2-eFFOkuui69_PY33KtFLg,7924
statsmodels/tools/sequences.py,sha256=P-Z9ZhS6s2zSrLWYeu5bdaXFKpCHROclMgScISVHdWA,6937
statsmodels/tools/sm_exceptions.py,sha256=95IzYU_65h0GM3RdxBpndMqq8yx02JXmnlXf9ZReyDY,4569
statsmodels/tools/testing.py,sha256=UkukGAjYK7NYT5N00q7F-B-U0ie9bJ_li1A4vE27HEk,2503
statsmodels/tools/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tools/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tools/tests/__pycache__/test_catadd.cpython-312.pyc,,
statsmodels/tools/tests/__pycache__/test_data.cpython-312.pyc,,
statsmodels/tools/tests/__pycache__/test_decorators.cpython-312.pyc,,
statsmodels/tools/tests/__pycache__/test_docstring.cpython-312.pyc,,
statsmodels/tools/tests/__pycache__/test_eval_measures.cpython-312.pyc,,
statsmodels/tools/tests/__pycache__/test_grouputils.cpython-312.pyc,,
statsmodels/tools/tests/__pycache__/test_linalg.cpython-312.pyc,,
statsmodels/tools/tests/__pycache__/test_numdiff.cpython-312.pyc,,
statsmodels/tools/tests/__pycache__/test_parallel.cpython-312.pyc,,
statsmodels/tools/tests/__pycache__/test_rootfinding.cpython-312.pyc,,
statsmodels/tools/tests/__pycache__/test_sequences.cpython-312.pyc,,
statsmodels/tools/tests/__pycache__/test_testing.cpython-312.pyc,,
statsmodels/tools/tests/__pycache__/test_tools.cpython-312.pyc,,
statsmodels/tools/tests/__pycache__/test_transform_model.cpython-312.pyc,,
statsmodels/tools/tests/__pycache__/test_web.cpython-312.pyc,,
statsmodels/tools/tests/test_catadd.py,sha256=XUPLj3b_Guqn6Mbx4AjKkkd0DLOZ4jzTrROL_CoYtQ8,690
statsmodels/tools/tests/test_data.py,sha256=vZpRC3evmcAdsD866w1sk5hSmh6Eh7bv2iCKPLOhpO8,1017
statsmodels/tools/tests/test_decorators.py,sha256=ffwkWmAj3kOLvVIG1MJmAZYTa7-sxI1m_NP-h3Phbqs,1989
statsmodels/tools/tests/test_docstring.py,sha256=poc1AmpQthx3m8_sWvzB7QAp41gVzjXsQ62wRjsdxho,3426
statsmodels/tools/tests/test_eval_measures.py,sha256=FhEoHkUGkLbekAYbyjKVFTWYPeP2hWYoS0cjM5Fi6lM,4212
statsmodels/tools/tests/test_grouputils.py,sha256=5FD-UgTRz07FlcN6lssuk5e2ck25jT7ZvXHhIwAr9oA,14878
statsmodels/tools/tests/test_linalg.py,sha256=tVb1StD3ao16wKOOVUWaa_ETskVnEDrznytXqF2cdrQ,755
statsmodels/tools/tests/test_numdiff.py,sha256=hGIr8BbmtaeKBomhH3RtkxqPgFYnlfLpQhhPU_uuCxU,15372
statsmodels/tools/tests/test_parallel.py,sha256=s1sqB-ismWhJIv9okWARe1WTFgvDVq9PLA2kzwxPyTA,416
statsmodels/tools/tests/test_rootfinding.py,sha256=VnpPHjQBXJWiE-DQDMN5EUWuUpKHuiLMtJbo_QDCfRo,2951
statsmodels/tools/tests/test_sequences.py,sha256=3ju3BbSKmb-qeW_CgXmcRFuwVqQeLijA9Z3sjZsMUFE,1681
statsmodels/tools/tests/test_testing.py,sha256=-yOmGy9S6Eej2FkvslZN7MIpJqEkw7YlT_i-IhPU_ms,796
statsmodels/tools/tests/test_tools.py,sha256=mCXIIZm2jp9fOMJL-Yb04SFKXLP5k4hzZZL8MEEwa-Y,11161
statsmodels/tools/tests/test_transform_model.py,sha256=6p1KH9bqMs1PWZAZnob5jQ4Gr1585fUWJQ_ilMDHIxs,1825
statsmodels/tools/tests/test_web.py,sha256=7WoRqrbx1OSdNYm_OSlNZr3LBevf-sRTYFi6fJdJey4,1677
statsmodels/tools/tools.py,sha256=dJeCGIq5Ws0TCBqgNOdmmymCEgdHhta9vAXMl6UDGxw,15967
statsmodels/tools/transform_model.py,sha256=bF303IeDFvXAaxU2r6Zd8a9qRCKQORFingw0hzikEYA,2893
statsmodels/tools/typing.py,sha256=AjBeo3s31z_OzbBl8IkYmpX9YTgz4Kqm956WG4c774c,1234
statsmodels/tools/validation/__init__.py,sha256=rirup0r64oNOx1Q5KHyROePWTvuWXWsmEwet-xUTBLY,352
statsmodels/tools/validation/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tools/validation/__pycache__/decorators.cpython-312.pyc,,
statsmodels/tools/validation/__pycache__/validation.cpython-312.pyc,,
statsmodels/tools/validation/decorators.py,sha256=bcESQE-AVlenTida5WepTsdshxfTo-66tDmsKv-gEmg,1026
statsmodels/tools/validation/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tools/validation/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tools/validation/tests/__pycache__/test_validation.cpython-312.pyc,,
statsmodels/tools/validation/tests/test_validation.py,sha256=393Ngh3DT0whFc8ysKeQDvB4k3P6l0VRsNZuo9v7m_Q,12975
statsmodels/tools/validation/validation.py,sha256=mjTkrnpP-4LhV2UsttPvznfM4SuIbgGQMh0E4XxZBOI,14932
statsmodels/tools/web.py,sha256=WahEY7Nwj_77bg0l68vQX9rVrJAAk_JRDUnCLXt8Ugo,2338
statsmodels/treatment/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/treatment/__pycache__/__init__.cpython-312.pyc,,
statsmodels/treatment/__pycache__/treatment_effects.cpython-312.pyc,,
statsmodels/treatment/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/treatment/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/treatment/tests/__pycache__/test_teffects.cpython-312.pyc,,
statsmodels/treatment/tests/results/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/treatment/tests/results/__pycache__/__init__.cpython-312.pyc,,
statsmodels/treatment/tests/results/__pycache__/results_teffects.cpython-312.pyc,,
statsmodels/treatment/tests/results/cataneo2.csv,sha256=c-VgaXyB0g8fMew1F2ALDD3l7XB-_NQRmSxVrhSVS4o,434160
statsmodels/treatment/tests/results/results_teffects.py,sha256=AgFHkJLYTzRzYa-OTjl3KTfVzdG2vVnzsoB8AaVPs4g,21740
statsmodels/treatment/tests/test_teffects.py,sha256=KdtOnz33_PKmLc4SfUrseN33TlEhevHwJ8lTm9xfrR8,4801
statsmodels/treatment/treatment_effects.py,sha256=QhEQi4A4DcsJb0for77i-w3-VsXSIHyqjtN0IGffnFw,33826
statsmodels/tsa/__init__.py,sha256=FNIIWqMJDn_RaWzLhb1qyMHw6Oh_msp2OmLojqBG2_M,82
statsmodels/tsa/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/__pycache__/_bds.cpython-312.pyc,,
statsmodels/tsa/__pycache__/adfvalues.cpython-312.pyc,,
statsmodels/tsa/__pycache__/api.cpython-312.pyc,,
statsmodels/tsa/__pycache__/ar_model.cpython-312.pyc,,
statsmodels/tsa/__pycache__/arima_model.cpython-312.pyc,,
statsmodels/tsa/__pycache__/arima_process.cpython-312.pyc,,
statsmodels/tsa/__pycache__/arma_mle.cpython-312.pyc,,
statsmodels/tsa/__pycache__/coint_tables.cpython-312.pyc,,
statsmodels/tsa/__pycache__/descriptivestats.cpython-312.pyc,,
statsmodels/tsa/__pycache__/deterministic.cpython-312.pyc,,
statsmodels/tsa/__pycache__/mlemodel.cpython-312.pyc,,
statsmodels/tsa/__pycache__/seasonal.cpython-312.pyc,,
statsmodels/tsa/__pycache__/stattools.cpython-312.pyc,,
statsmodels/tsa/__pycache__/tsatools.cpython-312.pyc,,
statsmodels/tsa/__pycache__/varma_process.cpython-312.pyc,,
statsmodels/tsa/__pycache__/x13.cpython-312.pyc,,
statsmodels/tsa/_bds.py,sha256=MBYe7EZaKp9PSxnvq5KT5KZG1p3_K_Zyq605tdZ_67M,7644
statsmodels/tsa/_innovations.cp312-win_amd64.pyd,sha256=DmtIzjfe-kSjTw0Hi9PBAj9roJfaDotV9xNSbwk8GLM,173056
statsmodels/tsa/adfvalues.py,sha256=PNWWfc_yJNM-hh_nk23hpOojqOL6ugcVhN6myzuT66M,16722
statsmodels/tsa/api.py,sha256=bEqJTYLqnUVMn3KFOWtC2Shh529GDCzJP01QJ5iLlKM,2787
statsmodels/tsa/ar_model.py,sha256=9p_qSjLxC1ME5V5U9z6-bbMQVizKa_ykIM5UTfj9KMg,82013
statsmodels/tsa/ardl/__init__.py,sha256=X_DDoLYk5CLIwLHP5z1ikzUIEkVMyvxw6qqNgzSA19I,351
statsmodels/tsa/ardl/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/ardl/__pycache__/model.cpython-312.pyc,,
statsmodels/tsa/ardl/__pycache__/pss_critical_values.cpython-312.pyc,,
statsmodels/tsa/ardl/_pss_critical_values/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/ardl/_pss_critical_values/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/ardl/_pss_critical_values/__pycache__/pss-process.cpython-312.pyc,,
statsmodels/tsa/ardl/_pss_critical_values/__pycache__/pss.cpython-312.pyc,,
statsmodels/tsa/ardl/_pss_critical_values/pss-process.py,sha256=GxNJ8eZYwg_wXBWV7Tq1BMPM2CvE_-HgRBCV1294i4c,5748
statsmodels/tsa/ardl/_pss_critical_values/pss.py,sha256=FuQ3IGdSwlLhQ4Jtp1ytS59INi63VZ5KzI-QKbjXnUU,2729
statsmodels/tsa/ardl/model.py,sha256=J27czJMSmN8WCCQZeq1Ff6HIOIMD_hud1cxMlkz8jO4,99236
statsmodels/tsa/ardl/pss_critical_values.py,sha256=vVPgwV3tgjOZ1EBR0RK4lHSuZd3H7ZqmpxtOCFRRz-k,22880
statsmodels/tsa/ardl/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/ardl/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/ardl/tests/__pycache__/test_ardl.cpython-312.pyc,,
statsmodels/tsa/ardl/tests/test_ardl.py,sha256=o8ZX5gbKhUqiz33xHR4k2iwoULMP_sTi2_Y5ezNHvM4,25132
statsmodels/tsa/arima/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/arima/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/arima/__pycache__/api.cpython-312.pyc,,
statsmodels/tsa/arima/__pycache__/model.cpython-312.pyc,,
statsmodels/tsa/arima/__pycache__/params.cpython-312.pyc,,
statsmodels/tsa/arima/__pycache__/specification.cpython-312.pyc,,
statsmodels/tsa/arima/__pycache__/tools.cpython-312.pyc,,
statsmodels/tsa/arima/api.py,sha256=9Km5Ci5Y4Umw_g5GfWocXXskdmFxU3KwKapxSe2Jigo,70
statsmodels/tsa/arima/datasets/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/arima/datasets/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/arima/datasets/brockwell_davis_2002/__init__.py,sha256=Aac-n1UmX7cTDAdj1BQUxwSdq5pep_A09DNyNlBlfEc,380
statsmodels/tsa/arima/datasets/brockwell_davis_2002/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/arima/datasets/brockwell_davis_2002/data/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/arima/datasets/brockwell_davis_2002/data/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/arima/datasets/brockwell_davis_2002/data/__pycache__/dowj.cpython-312.pyc,,
statsmodels/tsa/arima/datasets/brockwell_davis_2002/data/__pycache__/lake.cpython-312.pyc,,
statsmodels/tsa/arima/datasets/brockwell_davis_2002/data/__pycache__/oshorts.cpython-312.pyc,,
statsmodels/tsa/arima/datasets/brockwell_davis_2002/data/__pycache__/sbl.cpython-312.pyc,,
statsmodels/tsa/arima/datasets/brockwell_davis_2002/data/dowj.py,sha256=xvlaAyZLzzmUxS6xudcqZmwp6bFIu9gFMhVVZWXnk-w,1401
statsmodels/tsa/arima/datasets/brockwell_davis_2002/data/lake.py,sha256=SPM2HggY3S3wFac16SltnqKI5ByGM0ufIfZRCWLj_yE,1247
statsmodels/tsa/arima/datasets/brockwell_davis_2002/data/oshorts.py,sha256=9QqKNypmPdBY-FobCO1DDM6mZR5mEo7j8xEA1bwiwFY,841
statsmodels/tsa/arima/datasets/brockwell_davis_2002/data/sbl.py,sha256=vQ8sgV28ln9jCJo-HHHUzjhpbABziVIQip249LJ6Mhs,1428
statsmodels/tsa/arima/estimators/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/arima/estimators/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/arima/estimators/__pycache__/burg.cpython-312.pyc,,
statsmodels/tsa/arima/estimators/__pycache__/durbin_levinson.cpython-312.pyc,,
statsmodels/tsa/arima/estimators/__pycache__/gls.cpython-312.pyc,,
statsmodels/tsa/arima/estimators/__pycache__/hannan_rissanen.cpython-312.pyc,,
statsmodels/tsa/arima/estimators/__pycache__/innovations.cpython-312.pyc,,
statsmodels/tsa/arima/estimators/__pycache__/statespace.cpython-312.pyc,,
statsmodels/tsa/arima/estimators/__pycache__/yule_walker.cpython-312.pyc,,
statsmodels/tsa/arima/estimators/burg.py,sha256=KAF8s8mB6zXTX_bPjj6rUyWSWLi7S-5bml41u37mQHA,2364
statsmodels/tsa/arima/estimators/durbin_levinson.py,sha256=Lx57t8lnpWpBp8jaQdvdvDsKGnzdwbNh2KGr1AxngE8,3687
statsmodels/tsa/arima/estimators/gls.py,sha256=D1bl-oV-N8Z40g2fUTelLE0FSW_fC2lsxLFxXsmWKDU,13679
statsmodels/tsa/arima/estimators/hannan_rissanen.py,sha256=85HlCDcDFgS22sKUDl7CYnQEWZEtBg0S-P_XW598Kbw,17544
statsmodels/tsa/arima/estimators/innovations.py,sha256=syIoXiwaGF8IRMwVD09l_XRYnzmnCVuyupcPmwsGqu0,9917
statsmodels/tsa/arima/estimators/statespace.py,sha256=tKfLq6lH54_VDVrmvH5OkfJa7WjIhSFzx-0Zq-7icEI,5072
statsmodels/tsa/arima/estimators/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/arima/estimators/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/arima/estimators/tests/__pycache__/test_burg.cpython-312.pyc,,
statsmodels/tsa/arima/estimators/tests/__pycache__/test_durbin_levinson.cpython-312.pyc,,
statsmodels/tsa/arima/estimators/tests/__pycache__/test_gls.cpython-312.pyc,,
statsmodels/tsa/arima/estimators/tests/__pycache__/test_hannan_rissanen.cpython-312.pyc,,
statsmodels/tsa/arima/estimators/tests/__pycache__/test_innovations.cpython-312.pyc,,
statsmodels/tsa/arima/estimators/tests/__pycache__/test_statespace.cpython-312.pyc,,
statsmodels/tsa/arima/estimators/tests/__pycache__/test_yule_walker.cpython-312.pyc,,
statsmodels/tsa/arima/estimators/tests/test_burg.py,sha256=konoxeWGKxgZUC3PnNbYmlm8rthLRdVpGCsQiGIVXRI,4081
statsmodels/tsa/arima/estimators/tests/test_durbin_levinson.py,sha256=FKfe-HreqrLYAPmoMRrJwzjdbo1SZoSGufexjA5rvIM,4140
statsmodels/tsa/arima/estimators/tests/test_gls.py,sha256=ELnwUnzHP9BJUGnup7Rjyh1Cli_vXoMp9PtUOj1m2wQ,8601
statsmodels/tsa/arima/estimators/tests/test_hannan_rissanen.py,sha256=hBtfGePvzj-nuwtbYYwZN91J0G4LlL7kwozCkxELKDY,14569
statsmodels/tsa/arima/estimators/tests/test_innovations.py,sha256=kaXewc7Yjby1l34vHoNhRiKLKmqaYIZyKNhDAgmH3is,13349
statsmodels/tsa/arima/estimators/tests/test_statespace.py,sha256=ggL_Moi3HkioZ_I24Z5kcSK0CL8GMHwrA7VapaIGn8E,2255
statsmodels/tsa/arima/estimators/tests/test_yule_walker.py,sha256=aeTaDbEFF-bkLqXcNmg7bpiGkQ-hql4XRBd36JMCOho,3064
statsmodels/tsa/arima/estimators/yule_walker.py,sha256=xgCPy2N3c4Pw9e9YLTeviAndN8BkszD2WFWtebboDyw,2593
statsmodels/tsa/arima/model.py,sha256=5JRba8SsDunHxJgHs_jKfTxShEbxLmv0XEiqfIBFknE,26076
statsmodels/tsa/arima/params.py,sha256=9bxbs0_wor1_l1bbMweSgIyxnUoWNnfcU3TN9KXEqfw,15513
statsmodels/tsa/arima/specification.py,sha256=zirolWje8k08pJm9Mtj21WKhSenwS-ybhbjQxdqmAsU,47399
statsmodels/tsa/arima/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/arima/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/arima/tests/__pycache__/test_model.cpython-312.pyc,,
statsmodels/tsa/arima/tests/__pycache__/test_params.cpython-312.pyc,,
statsmodels/tsa/arima/tests/__pycache__/test_specification.cpython-312.pyc,,
statsmodels/tsa/arima/tests/__pycache__/test_tools.cpython-312.pyc,,
statsmodels/tsa/arima/tests/test_model.py,sha256=CBs2j5d4ld6yOUOvbKBrB2m-ZosbYJV1ut_CWB-NC_g,16064
statsmodels/tsa/arima/tests/test_params.py,sha256=FiXMQNNtqyYS0-29unk_nvUN0HcfIE-LL6YFlJvCjCY,23009
statsmodels/tsa/arima/tests/test_specification.py,sha256=WtGsiacufPl7zauAhTc7Jxgaw8jN7uT_zQdsv7CyGRA,25077
statsmodels/tsa/arima/tests/test_tools.py,sha256=vKqu-YB3bGviVXnIF5ZxpaIevQlsQ1PV9cUoxXqTZJE,3468
statsmodels/tsa/arima/tools.py,sha256=u7KsZg1eZUOjZZMP6uig9heIOiojfk2nXNBl6MiqYrg,5646
statsmodels/tsa/arima_model.py,sha256=ho_sNx-l2DxLoJlr0bnm9Lrw_RH-tezTBFyA07bVO6I,1873
statsmodels/tsa/arima_process.py,sha256=lRK3r9aJH9WVU-Lbd5kNtAOBYuSkP66FHQn9N0lzcWY,31787
statsmodels/tsa/arma_mle.py,sha256=QodqiF13LdrN9OIu8BQqxCS9KRDK6a8gnoAJ2dLCddg,564
statsmodels/tsa/base/__init__.py,sha256=FNIIWqMJDn_RaWzLhb1qyMHw6Oh_msp2OmLojqBG2_M,82
statsmodels/tsa/base/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/base/__pycache__/datetools.cpython-312.pyc,,
statsmodels/tsa/base/__pycache__/prediction.cpython-312.pyc,,
statsmodels/tsa/base/__pycache__/tsa_model.cpython-312.pyc,,
statsmodels/tsa/base/datetools.py,sha256=hkhmdjp_YE170sV7CgDNY2EtLK9HyTZvWqlFS8lf_6o,5982
statsmodels/tsa/base/prediction.py,sha256=ryGdS_6LXUjwaxu1ciIqJmxg40OAuSr9nmumUCqxn28,6344
statsmodels/tsa/base/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/base/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/base/tests/__pycache__/test_base.cpython-312.pyc,,
statsmodels/tsa/base/tests/__pycache__/test_datetools.cpython-312.pyc,,
statsmodels/tsa/base/tests/__pycache__/test_prediction.cpython-312.pyc,,
statsmodels/tsa/base/tests/__pycache__/test_tsa_indexes.cpython-312.pyc,,
statsmodels/tsa/base/tests/test_base.py,sha256=hHR-iHeNR8kX9oJoJyoqO6Z8XO9u-5IDjOaapV7O0SE,4007
statsmodels/tsa/base/tests/test_datetools.py,sha256=BXezK78xS0WegQHj9Vsou5_5nhBYAR9Iehjf8ufW-wk,3261
statsmodels/tsa/base/tests/test_prediction.py,sha256=DZmR6DxrIn3eHErvhcubroYTsruGmDBz8LQpwXvvuVw,1581
statsmodels/tsa/base/tests/test_tsa_indexes.py,sha256=lCSlVVbmblQVVG9Q_hdHlEjhiMJDJSMZBpaikiuR9Qw,47284
statsmodels/tsa/base/tsa_model.py,sha256=Y7KOQV1BHAUZAEaJ_lQ2hWKPMBAf2w6PWecSEoK-WZw,35991
statsmodels/tsa/coint_tables.py,sha256=jQbC6MAW-HMM_u0bxVH8sPpYLgJY6U-H-08gXaxeoQU,7543
statsmodels/tsa/descriptivestats.py,sha256=A16bBfl3RBkKazLExlizteBb4FLvEbBxNHBQjk7q81o,2350
statsmodels/tsa/deterministic.py,sha256=xrtMuj6Wzk80bQb8FKzj9QA6Fpdeqg4pITLoW88j4bs,53740
statsmodels/tsa/exponential_smoothing/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/exponential_smoothing/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/exponential_smoothing/__pycache__/base.cpython-312.pyc,,
statsmodels/tsa/exponential_smoothing/__pycache__/ets.cpython-312.pyc,,
statsmodels/tsa/exponential_smoothing/__pycache__/initialization.cpython-312.pyc,,
statsmodels/tsa/exponential_smoothing/_ets_smooth.cp312-win_amd64.pyd,sha256=YNXC9M1utQBeC8MFEZK8a-19D_HeY4XJefTc9p3tU74,450048
statsmodels/tsa/exponential_smoothing/base.py,sha256=twCoLlpxMprUBYSeDY6cTuLrCjDP7vdR-SjU2_VA6Nk,37449
statsmodels/tsa/exponential_smoothing/ets.py,sha256=xjubbX6aFlidAnCVD5N9VHjt-d8MdOuJRvBcjmbaJOo,91070
statsmodels/tsa/exponential_smoothing/initialization.py,sha256=NC5ON-ERu2qBSVfF8tARWimEvCpRTsXxrJWv3NehOB8,4603
statsmodels/tsa/filters/__init__.py,sha256=FNIIWqMJDn_RaWzLhb1qyMHw6Oh_msp2OmLojqBG2_M,82
statsmodels/tsa/filters/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/filters/__pycache__/_utils.cpython-312.pyc,,
statsmodels/tsa/filters/__pycache__/api.cpython-312.pyc,,
statsmodels/tsa/filters/__pycache__/bk_filter.cpython-312.pyc,,
statsmodels/tsa/filters/__pycache__/cf_filter.cpython-312.pyc,,
statsmodels/tsa/filters/__pycache__/filtertools.cpython-312.pyc,,
statsmodels/tsa/filters/__pycache__/hp_filter.cpython-312.pyc,,
statsmodels/tsa/filters/_utils.py,sha256=AroYbWIDm3XPghuq3UAcoThuKSENI3Sx-Txj1EmZbdo,3067
statsmodels/tsa/filters/api.py,sha256=gr2oqrx5Z3tN4prMW1F__xkJf-9x2QC2IMOWpiCD_Jc,294
statsmodels/tsa/filters/bk_filter.py,sha256=170PS1AOCDFZCcWdW3zQl_GICoEbqQ7vJ-hmk30Q_yM,3671
statsmodels/tsa/filters/cf_filter.py,sha256=N5EZAkf604C63Xdb_HqIKKSHIPokoScTpKVxS2MGGR8,3960
statsmodels/tsa/filters/filtertools.py,sha256=8mpZ_YoGwQG2qJQS2AVWITFfmH0URIO3v1j-Lcx4fHg,12489
statsmodels/tsa/filters/hp_filter.py,sha256=JfO-N4QVRHIlJ0JwdjXjBjUXgpyOsyLF44dIInvgoZs,3460
statsmodels/tsa/filters/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/filters/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/filters/tests/__pycache__/test_filters.cpython-312.pyc,,
statsmodels/tsa/filters/tests/results/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/filters/tests/results/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/filters/tests/results/__pycache__/filter_results.cpython-312.pyc,,
statsmodels/tsa/filters/tests/results/filter_results.py,sha256=lrlVuEz6XpNrh5BTgRfY_cDXdJWJoQZ541MjXjD9mHI,3945
statsmodels/tsa/filters/tests/test_filters.py,sha256=HTh2Hy8d0vLueNThuZ4KQpxgpzqubfFljjktnHTyW9Q,40167
statsmodels/tsa/forecasting/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/forecasting/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/forecasting/__pycache__/stl.cpython-312.pyc,,
statsmodels/tsa/forecasting/__pycache__/theta.cpython-312.pyc,,
statsmodels/tsa/forecasting/stl.py,sha256=TFkNxI8I8LDwwMcFBV26kKAUBXaxY7N6XC7aRU2vLNs,19043
statsmodels/tsa/forecasting/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/forecasting/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/forecasting/tests/__pycache__/test_stl.cpython-312.pyc,,
statsmodels/tsa/forecasting/tests/__pycache__/test_theta.cpython-312.pyc,,
statsmodels/tsa/forecasting/tests/test_stl.py,sha256=LRLAvBYGU6qQFFJZ5bXusUklyS-20TswabeQO7rPFzY,6821
statsmodels/tsa/forecasting/tests/test_theta.py,sha256=MfbQc3dZ8KjnsL2nCrviqJ3s4VN6nfkFqV4cZ-xVP_A,5467
statsmodels/tsa/forecasting/theta.py,sha256=zYRj1IUrfsiUZjRGHioRPj2tl6GNy1p-4Fa7SChSpFQ,23744
statsmodels/tsa/holtwinters/__init__.py,sha256=NmyiYCZe7KKMOUdTb9xMQ2zkYLvpMuNgeVwYrlqYmik,371
statsmodels/tsa/holtwinters/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/holtwinters/__pycache__/_smoothers.cpython-312.pyc,,
statsmodels/tsa/holtwinters/__pycache__/model.cpython-312.pyc,,
statsmodels/tsa/holtwinters/__pycache__/results.cpython-312.pyc,,
statsmodels/tsa/holtwinters/_exponential_smoothers.cp312-win_amd64.pyd,sha256=_6h4RACEAdt3eUTvlN8IU-eVrnbNYNVp0d_K92BCCcU,276992
statsmodels/tsa/holtwinters/_smoothers.py,sha256=9XEhGQtDtHFV7GhMoVmSskaNx1_Kpmf9YOLfILc1DkI,10546
statsmodels/tsa/holtwinters/model.py,sha256=LPHegZpDmx6rq99iu60s5Q_ccknZm08r2XF5RcTcYPg,70046
statsmodels/tsa/holtwinters/results.py,sha256=lX2R4igJPLL2YUhCqDZrXl0a6Jey3pWyp0-OoHoe9Ms,26752
statsmodels/tsa/holtwinters/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/holtwinters/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/holtwinters/tests/__pycache__/test_holtwinters.cpython-312.pyc,,
statsmodels/tsa/holtwinters/tests/results/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/holtwinters/tests/results/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/holtwinters/tests/results/housing-data.csv,sha256=0dGzsxOoAEMh7OHE-jGn-ujIOEzFPJjDH6EnIQT7COg,12633
statsmodels/tsa/holtwinters/tests/test_holtwinters.py,sha256=UNtUCjSjSUgZfVNOx58P18T0uUCd4STm4xierDBl0KY,70417
statsmodels/tsa/innovations/__init__.py,sha256=FNIIWqMJDn_RaWzLhb1qyMHw6Oh_msp2OmLojqBG2_M,82
statsmodels/tsa/innovations/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/innovations/__pycache__/api.cpython-312.pyc,,
statsmodels/tsa/innovations/__pycache__/arma_innovations.cpython-312.pyc,,
statsmodels/tsa/innovations/_arma_innovations.cp312-win_amd64.pyd,sha256=9zHiDtFI0QUeIKpgeNbVHOLXEZK0yHsboeK8A_Zp-Po,376832
statsmodels/tsa/innovations/api.py,sha256=KM_61uTP2VkKvgad4Rf4WysBqn_vu1StFc_PPdUnLOg,280
statsmodels/tsa/innovations/arma_innovations.py,sha256=x_TXaFogX0JCMTvaVESsg0Wd7a89BiTxzb_7FZ1kg8A,10084
statsmodels/tsa/innovations/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/innovations/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/innovations/tests/__pycache__/test_arma_innovations.cpython-312.pyc,,
statsmodels/tsa/innovations/tests/__pycache__/test_cython_arma_innovations_fast.cpython-312.pyc,,
statsmodels/tsa/innovations/tests/test_arma_innovations.py,sha256=-JONgPKZD17JaZlQVUHT9QQBYgtbz67GpFASJyUue1I,2190
statsmodels/tsa/innovations/tests/test_cython_arma_innovations_fast.py,sha256=XdB-curc71UyGT4RayvDCX9fTukyN7xhJJQwkoSTdKg,14236
statsmodels/tsa/interp/__init__.py,sha256=6y1v-MDXsFPDCsbMWp0yjcF1LQIZELNge23L7C8ZdDM,142
statsmodels/tsa/interp/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/interp/__pycache__/denton.cpython-312.pyc,,
statsmodels/tsa/interp/denton.py,sha256=Frx9aBQMX7SUrjoy_0ohkHvD3yiFgiE_Sk_7ccphuSg,11126
statsmodels/tsa/interp/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/interp/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/interp/tests/__pycache__/test_denton.cpython-312.pyc,,
statsmodels/tsa/interp/tests/test_denton.py,sha256=9PdOKdRSkfGfaTj8xAJVENd53-yVkZlmaCNMlprfjsA,1262
statsmodels/tsa/mlemodel.py,sha256=bBKoEI69kQrUAvaZDgcchNNDEuKzr3sIImWoU57Rc2E,2235
statsmodels/tsa/regime_switching/__init__.py,sha256=FNIIWqMJDn_RaWzLhb1qyMHw6Oh_msp2OmLojqBG2_M,82
statsmodels/tsa/regime_switching/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/regime_switching/__pycache__/markov_autoregression.cpython-312.pyc,,
statsmodels/tsa/regime_switching/__pycache__/markov_regression.cpython-312.pyc,,
statsmodels/tsa/regime_switching/__pycache__/markov_switching.cpython-312.pyc,,
statsmodels/tsa/regime_switching/_hamilton_filter.cp312-win_amd64.pyd,sha256=UXvNW3u-NGo9zvUXJt-GDvRFc6m919i2arKM0sT6imo,223744
statsmodels/tsa/regime_switching/_kim_smoother.cp312-win_amd64.pyd,sha256=VeL1pwaK84V8Mhy4K1Mwgq2ARQSFvFuPhZDXau2v6KE,212992
statsmodels/tsa/regime_switching/markov_autoregression.py,sha256=Ucbdq_o9h2_3426EecQbgZ0cOdJ_HeScwRIfOSqR5vk,18564
statsmodels/tsa/regime_switching/markov_regression.py,sha256=0rfh45-YHmIRur6-jPaUq45ntmiLPsrVU56gOt3rzbk,17012
statsmodels/tsa/regime_switching/markov_switching.py,sha256=h3mOYP0H2If-gy6aVM0XbYJBH5ZcOdqQ28Mxl4RF1iU,85069
statsmodels/tsa/regime_switching/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/regime_switching/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/regime_switching/tests/__pycache__/test_markov_autoregression.cpython-312.pyc,,
statsmodels/tsa/regime_switching/tests/__pycache__/test_markov_regression.cpython-312.pyc,,
statsmodels/tsa/regime_switching/tests/__pycache__/test_markov_switching.cpython-312.pyc,,
statsmodels/tsa/regime_switching/tests/results/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/regime_switching/tests/results/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/regime_switching/tests/results/mar_filardo.csv,sha256=692YPaqk4_51Zm9nP51BoYQYgNWvAPU8OByoR7UVuPo,39966
statsmodels/tsa/regime_switching/tests/results/results_predict_fedfunds.csv,sha256=r-6HFESRDF5TOD0Ba1YXK8-Q9GPZS2pq5nUwJGa0ONY,28228
statsmodels/tsa/regime_switching/tests/results/results_predict_rgnp.csv,sha256=-G8bwv7efg7P7DfyNNIPcsuUWUesvJ86BOV3UZgmJ1A,13403
statsmodels/tsa/regime_switching/tests/test_markov_autoregression.py,sha256=lHnb2MPEX84PC54yWgB62RwONBsSMj_El58sYxGQF38,42043
statsmodels/tsa/regime_switching/tests/test_markov_regression.py,sha256=CioSRJj-EVwtMW7L14pirQ2UqilSK_yEHvs3NqHN7Dw,71050
statsmodels/tsa/regime_switching/tests/test_markov_switching.py,sha256=o0Z1JQXsmEVK9j9GBP6_UhaK1zwE2cVix_grAM7HLSo,12560
statsmodels/tsa/seasonal.py,sha256=bSkBQbIh_KLXZtgRk58IOkM-ALK6VTM_8W1IMdLG2MA,11711
statsmodels/tsa/statespace/__init__.py,sha256=FNIIWqMJDn_RaWzLhb1qyMHw6Oh_msp2OmLojqBG2_M,82
statsmodels/tsa/statespace/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/statespace/__pycache__/_pykalman_smoother.cpython-312.pyc,,
statsmodels/tsa/statespace/__pycache__/_quarterly_ar1.cpython-312.pyc,,
statsmodels/tsa/statespace/__pycache__/api.cpython-312.pyc,,
statsmodels/tsa/statespace/__pycache__/cfa_simulation_smoother.cpython-312.pyc,,
statsmodels/tsa/statespace/__pycache__/dynamic_factor.cpython-312.pyc,,
statsmodels/tsa/statespace/__pycache__/dynamic_factor_mq.cpython-312.pyc,,
statsmodels/tsa/statespace/__pycache__/exponential_smoothing.cpython-312.pyc,,
statsmodels/tsa/statespace/__pycache__/initialization.cpython-312.pyc,,
statsmodels/tsa/statespace/__pycache__/kalman_filter.cpython-312.pyc,,
statsmodels/tsa/statespace/__pycache__/kalman_smoother.cpython-312.pyc,,
statsmodels/tsa/statespace/__pycache__/mlemodel.cpython-312.pyc,,
statsmodels/tsa/statespace/__pycache__/news.cpython-312.pyc,,
statsmodels/tsa/statespace/__pycache__/representation.cpython-312.pyc,,
statsmodels/tsa/statespace/__pycache__/sarimax.cpython-312.pyc,,
statsmodels/tsa/statespace/__pycache__/simulation_smoother.cpython-312.pyc,,
statsmodels/tsa/statespace/__pycache__/structural.cpython-312.pyc,,
statsmodels/tsa/statespace/__pycache__/tools.cpython-312.pyc,,
statsmodels/tsa/statespace/__pycache__/varmax.cpython-312.pyc,,
statsmodels/tsa/statespace/_cfa_simulation_smoother.cp312-win_amd64.pyd,sha256=HbLBS71bPoho33cqwK-a4_gLhD4_sP1MJb2rDha6FeY,381952
statsmodels/tsa/statespace/_filters/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/statespace/_filters/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/statespace/_filters/_conventional.cp312-win_amd64.pyd,sha256=Xf_GJQoDcPQSZK4Pz-uwnQWkNg00E2oDPtSkxPPsO4U,166912
statsmodels/tsa/statespace/_filters/_inversions.cp312-win_amd64.pyd,sha256=PduKIUQ9n1fYxyxy5hO4yJqZbahW5VHjZ3lf2BjBvT8,196608
statsmodels/tsa/statespace/_filters/_univariate.cp312-win_amd64.pyd,sha256=tIPX6Wf0se_uhTC-VMlUkydOXFlniMUWu3IoKfe0JTQ,195584
statsmodels/tsa/statespace/_filters/_univariate_diffuse.cp312-win_amd64.pyd,sha256=Oi9S6kkLGkOWByaAq4c_re46EQ1hlkoC0cjE8C9_BQw,165376
statsmodels/tsa/statespace/_initialization.cp312-win_amd64.pyd,sha256=qHO7n8x_7tCfp4xPYKxWlkQYE6yKStaacihQ8ga7B8s,280576
statsmodels/tsa/statespace/_kalman_filter.cp312-win_amd64.pyd,sha256=J4VgYT7wrEK7yitX2Do52kVfHvukCV4DauliFtJesP4,714240
statsmodels/tsa/statespace/_kalman_smoother.cp312-win_amd64.pyd,sha256=x-jSRcJp5XE5-O8sQ4OGkwSlx5124SIwpiXE_qusniU,465408
statsmodels/tsa/statespace/_pykalman_smoother.py,sha256=nX4fWSRcYilhnh3edT0k1gmGBvpNToraVm8cGgYEW1M,11292
statsmodels/tsa/statespace/_quarterly_ar1.py,sha256=_8ZSZd-kESLXH1ja-LfouLEMqILr9teGrUTVUFuNpis,7662
statsmodels/tsa/statespace/_representation.cp312-win_amd64.pyd,sha256=04jRmeN1Gvp-R8m1V__ECSN2MSDaq6anw4gmSfszWvc,682496
statsmodels/tsa/statespace/_simulation_smoother.cp312-win_amd64.pyd,sha256=avixh4Em450bhXE2qHIsxSGjkC17Bo8Enpw0_Gdtpbo,510976
statsmodels/tsa/statespace/_smoothers/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/statespace/_smoothers/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/statespace/_smoothers/_alternative.cp312-win_amd64.pyd,sha256=r4IU5mnf1JW4-4l_p1PGAVhKWwRwATi_WdTdKKvJCvA,158720
statsmodels/tsa/statespace/_smoothers/_classical.cp312-win_amd64.pyd,sha256=kJBhIbFFJIOKv4JqU6uQO_J0byk7JIsZCeLPs_J02II,159744
statsmodels/tsa/statespace/_smoothers/_conventional.cp312-win_amd64.pyd,sha256=dKEgOl8y3wpB_Pk4_0mbg3ixmsMYW-tjt3CUsOgxiKg,160768
statsmodels/tsa/statespace/_smoothers/_univariate.cp312-win_amd64.pyd,sha256=Lc2EBTq961CAd1lHgysdPnERnsCNRPAnIuQbCGGOrWI,175104
statsmodels/tsa/statespace/_smoothers/_univariate_diffuse.cp312-win_amd64.pyd,sha256=f0gcNUXvYGbRrwwvXcZWIwW3omZ_mGbXosRTnCTQLR8,195584
statsmodels/tsa/statespace/_tools.cp312-win_amd64.pyd,sha256=AO0I6Z5YFkSPb-QlJAfqKpa5EBgSm1QLekueOLCpUNU,579584
statsmodels/tsa/statespace/api.py,sha256=4wE6i4yO33HwWH3XHjRoKeTHegUiXb5PT4Na5dhkeFw,308
statsmodels/tsa/statespace/cfa_simulation_smoother.py,sha256=sYv0gFr84tculXE2LEWUC8F3NzDOYMXzBEcDx5pxSFo,10530
statsmodels/tsa/statespace/dynamic_factor.py,sha256=t7fiiJMV7A5qUCrgRuFeaYdVq-n7w5X56t9y2gQcwWg,55606
statsmodels/tsa/statespace/dynamic_factor_mq.py,sha256=t-vnvFCa_QqKDRm6-450OoeuLm0Q-wOVRj2cc-jpVoU,201632
statsmodels/tsa/statespace/exponential_smoothing.py,sha256=oOdFQBO3rdFzJA7m43bxJQvZMXO5N4k90Tw5yb-6SJE,31398
statsmodels/tsa/statespace/initialization.py,sha256=dqVw8LK9Tvlw0YSgsvRy28LxskZGFjexAY2e3cgB8Qw,34272
statsmodels/tsa/statespace/kalman_filter.py,sha256=E7YMegU4mOt98x-kGukufQ8udmB-qAeE-5PJHgwZf2Q,108616
statsmodels/tsa/statespace/kalman_smoother.py,sha256=-qMdnaWKQ3cHqn9HZc1_TX-EpFq6x847vTTzIKlAtC8,85215
statsmodels/tsa/statespace/mlemodel.py,sha256=44pXMqV5M2yCScDK1pk9nRYULteEelP88kB9dXXE6CY,223773
statsmodels/tsa/statespace/news.py,sha256=F0kSVLru9F22k6W58uupM2AxSG0byUlvN6QEaEcxWQQ,73252
statsmodels/tsa/statespace/representation.py,sha256=kBegZp-bW3jkAMwK6MIvEQrFvXwz_sdpyBA2sSFPyuc,49907
statsmodels/tsa/statespace/sarimax.py,sha256=g_SPc3pXEPXFtBU8Jn-yk2Z1Ud1YXKQJQaks2fC-dbs,86413
statsmodels/tsa/statespace/simulation_smoother.py,sha256=cAQEyzpMo6qAc_x1NYhU02zBaloPg6ZaTUWzWP5RmeQ,31670
statsmodels/tsa/statespace/structural.py,sha256=Ui1GIHa3PCjKZ9-XUUXOQoTsRFkVAhikCIzojsRfa1U,80853
statsmodels/tsa/statespace/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/statespace/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/kfas_helpers.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_cfa_simulation_smoothing.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_cfa_tvpvar.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_chandrasekhar.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_collapsed.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_concentrated.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_conserve_memory.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_decompose.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_dynamic_factor.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_dynamic_factor_mq.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_dynamic_factor_mq_frbny_nowcast.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_dynamic_factor_mq_monte_carlo.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_exact_diffuse_filtering.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_exponential_smoothing.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_fixed_params.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_forecasting.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_impulse_responses.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_initialization.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_kalman.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_mlemodel.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_models.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_multivariate_switch_univariate.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_news.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_options.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_pickle.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_prediction.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_representation.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_sarimax.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_save.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_simulate.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_simulation_smoothing.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_smoothing.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_structural.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_tools.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_univariate.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_var.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_varmax.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_weights.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/kfas_helpers.py,sha256=GvqHDnQRUM6VYRHM_onoASWXhRHFxo_igshps8WZnLo,3775
statsmodels/tsa/statespace/tests/results/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/statespace/tests/results/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/results/__pycache__/results_dynamic_factor.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/results/__pycache__/results_kalman_filter.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/results/__pycache__/results_sarimax.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/results/__pycache__/results_structural.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/results/__pycache__/results_var_R.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/results/__pycache__/results_var_misc.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/results/__pycache__/results_varmax.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/results/cfa_tvpvar_Omega_11.csv,sha256=krYOFtYUQI8p5osj1t2DZ0ILts9Ms-6OLa0Yt29nDhc,96
statsmodels/tsa/statespace/tests/results/cfa_tvpvar_Omega_22.csv,sha256=2x_4ro_tOgvK2x9O2NJOyU4bJxOwNRe-1PIMRmNtM1E,184
statsmodels/tsa/statespace/tests/results/cfa_tvpvar_S10.csv,sha256=5FONDvQX8c-6bCbLnWKXf5eD7fR6c1YWpRDtdnHuAIk,97
statsmodels/tsa/statespace/tests/results/cfa_tvpvar_Si0.csv,sha256=CacybcXOm8izp39R0UwJU4rO41kxsbNM_7pzfSrrdQM,172
statsmodels/tsa/statespace/tests/results/cfa_tvpvar_beta.csv,sha256=vsS8kd5r3Bq6vTG6CFWpICzpDi2AXPkYtVjnRLskKNo,1408
statsmodels/tsa/statespace/tests/results/cfa_tvpvar_invP.csv,sha256=VgFtc4_r0tSKzvPNZYUNZRxxgtlyDvaTdjSfL_QRTIk,24167
statsmodels/tsa/statespace/tests/results/cfa_tvpvar_posterior_mean.csv,sha256=zfMY0tOv-eHnQGrDfzdCB_7S1B4JmYDCj-lMksdJp38,1392
statsmodels/tsa/statespace/tests/results/cfa_tvpvar_state_variates.csv,sha256=Boj0nh4QYbAMxu2Re7Edy-45ASiS4CdGZrM0ZOKSdxY,1415
statsmodels/tsa/statespace/tests/results/cfa_tvpvar_v10.csv,sha256=Tkwx19TUSJaNhJYPmupMcZG20ZEPEgQtR0q1xYZSbMo,7
statsmodels/tsa/statespace/tests/results/cfa_tvpvar_vi0.csv,sha256=SQqM5FMTDF2NSS-d7hRjCuwH5qokRHYpyr8HqwCIqFA,5
statsmodels/tsa/statespace/tests/results/clark1989.csv,sha256=QywqQ17X8DLThJA3-D4aPKVW7-bHIg1nxKtLAe7s-9Q,4628
statsmodels/tsa/statespace/tests/results/exponential_smoothing_params.csv,sha256=EsXRnCHqe0KtOEzHzlRBlfYNzfC3QqNPYQugXWZ9Ads,1789
statsmodels/tsa/statespace/tests/results/exponential_smoothing_predict.csv,sha256=ls8-wbKGIaeIdbJll8nGBO-sXUH8tX2jx0fsQLeRoR4,11592
statsmodels/tsa/statespace/tests/results/exponential_smoothing_states.csv,sha256=rsCN9RDWffiVTsmeJPVMNhr95o_j6HdTZU7rUNBVGpA,13376
statsmodels/tsa/statespace/tests/results/frbny_nowcast/Nowcasting/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/statespace/tests/results/frbny_nowcast/Nowcasting/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/results/frbny_nowcast/Nowcasting/data/US/2016-06-29.csv,sha256=c523_hY5tRGhegT26ubqJr2b24frsE0rhKls-Y6YPuE,59068
statsmodels/tsa/statespace/tests/results/frbny_nowcast/Nowcasting/data/US/2016-07-29.csv,sha256=U9ghj_oNqlZPJOMDNkUzvpII_NhMpGeBYY0NeCGamD8,59228
statsmodels/tsa/statespace/tests/results/frbny_nowcast/Nowcasting/data/US/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/statespace/tests/results/frbny_nowcast/Nowcasting/data/US/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/results/frbny_nowcast/Nowcasting/data/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/statespace/tests/results/frbny_nowcast/Nowcasting/data/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/results/frbny_nowcast/Nowcasting/functions/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/statespace/tests/results/frbny_nowcast/Nowcasting/functions/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/results/frbny_nowcast/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/statespace/tests/results/frbny_nowcast/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/results/frbny_nowcast/test_dfm_111.mat,sha256=bbmFp738mAr1QikZson0ZvGgXdkInk4nrqHDmKxshco,11477
statsmodels/tsa/statespace/tests/results/frbny_nowcast/test_dfm_112.mat,sha256=LucPsDOBuWcBubngpobaxtT0PjVtgRcPBnJf7YjwHSw,11486
statsmodels/tsa/statespace/tests/results/frbny_nowcast/test_dfm_11F.mat,sha256=l4N0TD_yuZwnaJMwsnsEZEefVnCMOaOdUQF7kYg66Gg,11522
statsmodels/tsa/statespace/tests/results/frbny_nowcast/test_dfm_221.mat,sha256=qqTCHR36F6tijaimRNBAnEc4zJ2TlruxET8G347-nWw,12548
statsmodels/tsa/statespace/tests/results/frbny_nowcast/test_dfm_222.mat,sha256=vZlZ4YT4GUSQcpWT3XQTSikPwGHSycHVDE8GCBJHDZM,12557
statsmodels/tsa/statespace/tests/results/frbny_nowcast/test_dfm_22F.mat,sha256=Fo_4bmGM0pnHF5fRyO_OsqvsTQ74f1QDrG-NgvCsZQw,12685
statsmodels/tsa/statespace/tests/results/frbny_nowcast/test_dfm_blocks_111.mat,sha256=POTDaVOOhGQc0DX4qDUBAPP2OF4oX8KxUDeqRshMb9c,20685
statsmodels/tsa/statespace/tests/results/frbny_nowcast/test_dfm_blocks_112.mat,sha256=5MTbAcUVk9ZR2EPwQwvuOlSA7mOJ8la6w2O5SLgyMmg,20700
statsmodels/tsa/statespace/tests/results/frbny_nowcast/test_dfm_blocks_221.mat,sha256=FYyqZPADI6KMKjAvDis9XMQq6U0fBLVp5HLXd1-vWC4,30747
statsmodels/tsa/statespace/tests/results/frbny_nowcast/test_dfm_blocks_222.mat,sha256=0i8PXJ_4dqWIoTm2vqzOr1YT67Tb9xNT-qtVNkk4SBQ,30790
statsmodels/tsa/statespace/tests/results/frbny_nowcast/test_news_112.mat,sha256=TKZKit__SN-9FCdYxT4mkV4qdkBUd9wjoABEBinHoIY,11723
statsmodels/tsa/statespace/tests/results/frbny_nowcast/test_news_222.mat,sha256=ItqDdynAQwBhJQTtRRiGjZusbcp1xxEqYiLHX2F1FCs,12782
statsmodels/tsa/statespace/tests/results/frbny_nowcast/test_news_blocks_112.mat,sha256=f8Z8OQASO6b0vl5nbumBRkP37_-Ed1XZSuF7H51_k-c,20915
statsmodels/tsa/statespace/tests/results/frbny_nowcast/test_news_blocks_222.mat,sha256=W9GuQ3hefgj3PCZjhR1rywdNY_lgz1cbCDrdyXW6Y7o,31025
statsmodels/tsa/statespace/tests/results/manufac.dta,sha256=1e6VE2SMYdontWCwqua_Ipr1R5vb1i6Sa63jS6vtPwU,19146
statsmodels/tsa/statespace/tests/results/results_clark1989_R.csv,sha256=ImNQNsKpziZTY6iyNIct32KQsp7qEuH7V3YlDnvTtwE,25812
statsmodels/tsa/statespace/tests/results/results_dynamic_factor.py,sha256=dXV840AfiEFwdNN0zBS9HPKfVw25jSSMRDTWaY64Nsc,6983
statsmodels/tsa/statespace/tests/results/results_dynamic_factor_stata.csv,sha256=Ujz1hwEYfU1yB9_7obEqXU0xnxiK_8vlJVGcFr9igSs,43359
statsmodels/tsa/statespace/tests/results/results_exact_initial_common_level_R.csv,sha256=EV2uGTPFyUsEOYb4aF9MjC3bH9qh5ewrITa_R_I17zY,5313
statsmodels/tsa/statespace/tests/results/results_exact_initial_common_level_restricted_R.csv,sha256=s4sCAimLMt5OTVbEHPZLdtVpRYaoy9frphr6Kf1ltyE,4723
statsmodels/tsa/statespace/tests/results/results_exact_initial_dfm_R.csv,sha256=ZF2h9cN88xu4lTiUpkdv7q3n3m2diugQQNZQ79UYnx4,17462
statsmodels/tsa/statespace/tests/results/results_exact_initial_local_level_R.csv,sha256=b6qBMyesakEcPdA_G69iZf2ApGEjyPV5beQFyt90A34,3473
statsmodels/tsa/statespace/tests/results/results_exact_initial_local_linear_trend_R.csv,sha256=_Png0Mc4JeJHHSgkYKbrPHAK0wF7odfIQDNisN2pv9Y,4308
statsmodels/tsa/statespace/tests/results/results_exact_initial_local_linear_trend_missing_R.csv,sha256=2MSVHXouWZFRTWHdPDa1De8MeAOi1ZOMoqnJUYyu8Ic,4262
statsmodels/tsa/statespace/tests/results/results_exact_initial_var1_R.csv,sha256=ujGX1sFSun2wbyRcV8uavL_O2mRMCbIo1ik1L_uTM5A,9665
statsmodels/tsa/statespace/tests/results/results_exact_initial_var1_measurement_error_R.csv,sha256=Iksva7w8vwIzh_gvbfBRJ-20KQjUvZufArx7Wiw4JNQ,19115
statsmodels/tsa/statespace/tests/results/results_exact_initial_var1_missing_R.csv,sha256=810CoEXhhWFyPBvD4eHWpzUbhOpEE3xv7f24DDwtTvU,12785
statsmodels/tsa/statespace/tests/results/results_exact_initial_var1_mixed_R.csv,sha256=andsAXJggKy4GDv-zozBz-Q3A8lgbu_NEXNJNhgTxYk,9743
statsmodels/tsa/statespace/tests/results/results_intercepts_R.csv,sha256=-rD4iUDoJ3JNcmI_4DE0kF5xNjTYJ9JMA5yr0-J7KVE,120264
statsmodels/tsa/statespace/tests/results/results_kalman_filter.py,sha256=ajS5EKLtQWbdyMhfxzirrrZOxLmrAd_xhyOLmPka_xg,42307
statsmodels/tsa/statespace/tests/results/results_realgdpar_stata.csv,sha256=5naq8Nelru9TDgnD-wouY8PQl-3NHGfSSbG24Og1W1M,62891
statsmodels/tsa/statespace/tests/results/results_sarimax.py,sha256=1HW28RxVsEV-7fiEpeI6IgYLMNrnhfhnmmm8vYcVnNc,14099
statsmodels/tsa/statespace/tests/results/results_sarimax_coverage.csv,sha256=dLfERFAt0WKdtP9QmeIjCZAnt75t0kkGBHoLsCC1LUs,6321
statsmodels/tsa/statespace/tests/results/results_simulation_smoothing0.csv,sha256=fw81vBuHc_plx8x_gJG1TBfNH8Y4zvl9dJwPZ7IdJBU,2205
statsmodels/tsa/statespace/tests/results/results_simulation_smoothing1.csv,sha256=PlSC27yb7MvHzFBZbM4hhte0V4z3ex7dwP5LZL6HV6E,2220
statsmodels/tsa/statespace/tests/results/results_simulation_smoothing2.csv,sha256=sOQs9nAGislak-hPwh23Atst9vqYOdHiZTgB8SuZl-Q,2252
statsmodels/tsa/statespace/tests/results/results_simulation_smoothing3.csv,sha256=zblvsKfiy8P7KSPN-KEALC_UH7c3gdDmzwww535F2ys,48473
statsmodels/tsa/statespace/tests/results/results_simulation_smoothing3_variates.csv,sha256=YK7qNESf3xNKfI9eoP5xbnWuEnhG_6sBDywYE514oqs,23338
statsmodels/tsa/statespace/tests/results/results_simulation_smoothing4.csv,sha256=Tz0cvIjvWHSDkmpN8v4heg7KC0JxoorjA3ie-3pOzpo,48675
statsmodels/tsa/statespace/tests/results/results_simulation_smoothing5.csv,sha256=9FzfXLYCQb9l0IwFBO4KAeJC8N3pasjnWrzjedy8Bvw,47553
statsmodels/tsa/statespace/tests/results/results_simulation_smoothing6.csv,sha256=Vsgl8qxj_6xpXCKVDMYoZb6WFRpG7tdouDksghul1tA,44720
statsmodels/tsa/statespace/tests/results/results_smoothing2_R.csv,sha256=QnE-sjtJ6qhyCtEPf4QmjLWHs-S3rYik4qzcZJh22ro,144969
statsmodels/tsa/statespace/tests/results/results_smoothing3_R.csv,sha256=Y-EruUcs_F18UwCh2URs-cAtamKXJDHiKMP1TjeqJtk,32653
statsmodels/tsa/statespace/tests/results/results_smoothing_R.csv,sha256=khE35d0yiQ6zQSmbRFksqv5XofhgqSbQsuh5c4BNT8Y,117428
statsmodels/tsa/statespace/tests/results/results_smoothing_generalobscov_R.csv,sha256=5Q8r2UBEyc7kdl3kLOxr-T1kMIRKUvR1L6Ru1OC0pkg,121746
statsmodels/tsa/statespace/tests/results/results_structural.py,sha256=sKy5GHNZqFWVGsClofC74J-EQh1BgjOHUJ1Nq7vdjaM,9635
statsmodels/tsa/statespace/tests/results/results_var_R.py,sha256=OhOzbfw6vsWd2jdN6u8JHjkWY7jRXGbSQ4AKFKkE2QM,5737
statsmodels/tsa/statespace/tests/results/results_var_R_output.csv,sha256=wweWQOj8R8xayJuE-uUzTmVZIKBBDW-1jcpJqpPAVn8,68693
statsmodels/tsa/statespace/tests/results/results_var_misc.py,sha256=VNTDif5OCMyCmAEequC0LO_C4Ou7jkK1FkRX8K9KTJY,5822
statsmodels/tsa/statespace/tests/results/results_var_stata.csv,sha256=0PAKTerTbdJpoZtLTL-CzSlrDQ0ZSZfJBVjhBraICoc,24685
statsmodels/tsa/statespace/tests/results/results_varmax.py,sha256=wO0qvTiv9jjgJURSU0ZAAu2xQUwzOOYhKOxyNtIBPnw,8943
statsmodels/tsa/statespace/tests/results/results_varmax_stata.csv,sha256=M9hL-qtxbK0rhEErW5bwsu_h-PIA3s0KO_73skYrqVg,26514
statsmodels/tsa/statespace/tests/results/results_wpi1_ar3_matlab_ssm.csv,sha256=9fBOnozx9oKmDjv6kLzq5aDbzDv7JxLIAXS3Cco4uHE,6376
statsmodels/tsa/statespace/tests/results/results_wpi1_ar3_stata.csv,sha256=TlsIVJciqo29HYnCNNtS6s_IXjs-xcm6L-lTvQq-5Oo,14470
statsmodels/tsa/statespace/tests/results/results_wpi1_missing_ar3_matlab_ssm.csv,sha256=1n5u0t04sCcVxZlRfNClHg3bPZbXFf6jdVvwb6ScSVc,6722
statsmodels/tsa/statespace/tests/results/sm-0.9-sarimax.pkl,sha256=ZSQYFs-9nCBal_wiIsTbdlKsm76nQhkGsPwujyT2t8Q,6653
statsmodels/tsa/statespace/tests/test_cfa_simulation_smoothing.py,sha256=Qds8rd5WZI-6rcbWh5AV9MjzDC4EMZKX1YCaSrh-6nk,7039
statsmodels/tsa/statespace/tests/test_cfa_tvpvar.py,sha256=DJj6GpnmhOBZo8J-m_QHJmgQR-su1TJyglzJQT6VTHA,7754
statsmodels/tsa/statespace/tests/test_chandrasekhar.py,sha256=-_LYrp43-3Db3Z6fvvPYYGLKoQZeIKvLL83UD2Ghx_E,9119
statsmodels/tsa/statespace/tests/test_collapsed.py,sha256=9sMxYyzW8GytBFOtmQygQE1Wp0RWDNHMnBP-OvZm5W0,28239
statsmodels/tsa/statespace/tests/test_concentrated.py,sha256=qBxbDE2dEoWHCtAAX0ugg7iW_Tcr9Th8tqSODXHGyFg,12129
statsmodels/tsa/statespace/tests/test_conserve_memory.py,sha256=lU9CrfTd1K6yjsW-h2xR9ei2vab6NRxq6MDFetW3vi0,13922
statsmodels/tsa/statespace/tests/test_decompose.py,sha256=Ieb36UhR_MgnglTtK84RZkDocFQhN0wB4KhwXeT9_vU,12330
statsmodels/tsa/statespace/tests/test_dynamic_factor.py,sha256=sUwj0V3L6mh9ImYP0Vm0fP1Bae9Vq2iam81Ohn1JjKA,39487
statsmodels/tsa/statespace/tests/test_dynamic_factor_mq.py,sha256=RRDFNLUJN8VAAul-rASrLILP--F4FhX0tKEJbgZs0q0,85514
statsmodels/tsa/statespace/tests/test_dynamic_factor_mq_frbny_nowcast.py,sha256=29GBhSBQSR0vAtz0G-fBcUMI-s3QuwzxuW23sNgrC5s,19438
statsmodels/tsa/statespace/tests/test_dynamic_factor_mq_monte_carlo.py,sha256=sO_J0YRx87jPkrsXHAtgGuVeAjTNmyiOlzc-1z-Sj6Q,25207
statsmodels/tsa/statespace/tests/test_exact_diffuse_filtering.py,sha256=Bkkwu_t0HeuaQnthDy8iaunodV8fvq9ybklLpknGG4A,41100
statsmodels/tsa/statespace/tests/test_exponential_smoothing.py,sha256=2e3VvT1Fqp1AFBQFWxJLeehmfq-DjhDVB--Pqsgtkk8,36790
statsmodels/tsa/statespace/tests/test_fixed_params.py,sha256=Ax6oTCozAm-mjrfH1lr7JOnJiUVVBn6UvNnihlyNYIE,29807
statsmodels/tsa/statespace/tests/test_forecasting.py,sha256=MF2Cv0jlMSNITGKFbvqV2NBYkPS4BNHJJLHD7ENxAoo,1626
statsmodels/tsa/statespace/tests/test_impulse_responses.py,sha256=j4PNRj01oydHzT6sB1XhmNDd2WU461SHCvCbX_uZ1jg,27805
statsmodels/tsa/statespace/tests/test_initialization.py,sha256=rW3bro2J3GyjeYqG7OqUQl0AegcSEk23D5ePoewQtcQ,14212
statsmodels/tsa/statespace/tests/test_kalman.py,sha256=pcB0WKTcMhkdB2g8HcOiCwz0fcTV6Bp8WNhXsVODPZk,27661
statsmodels/tsa/statespace/tests/test_mlemodel.py,sha256=48DAZ6Kfi9_knSsBxCxeRxovlnp51J_q5rT6JUU4MV8,48874
statsmodels/tsa/statespace/tests/test_models.py,sha256=CAavmRk19g7hbK7cRKRK3iGD13TCwCQfNfdbigq2cJA,11060
statsmodels/tsa/statespace/tests/test_multivariate_switch_univariate.py,sha256=WXwa444JlsPnwBFi5sQjNQMB0iImkuNlwFwSGbTd6j0,19464
statsmodels/tsa/statespace/tests/test_news.py,sha256=WLyj2M0KiaP8MmE3-pk3Nc6EMqhqNT0fCMqVhI4nTMc,55742
statsmodels/tsa/statespace/tests/test_options.py,sha256=XF0fmnNLVNwSibTT8MEMm2cH7GQZoY0Tz0PadBmIXKs,9647
statsmodels/tsa/statespace/tests/test_pickle.py,sha256=QsXoU9dk7hA1ZzEGX1FXQqMXKCwXZ65TYbVegjxYEXI,5261
statsmodels/tsa/statespace/tests/test_prediction.py,sha256=q61AKt5e9oLzv3UX4zpak3HcpC7DVV3s1q-t4Ps6IPA,24704
statsmodels/tsa/statespace/tests/test_representation.py,sha256=G6bDZvV7UX56cEIzFKml93hpYRXBctneNlbI_oP3rZA,48081
statsmodels/tsa/statespace/tests/test_sarimax.py,sha256=02ltp6V8ZjyYwdA3tninoPkccHhO2NIC-_4vbfpCvTA,106668
statsmodels/tsa/statespace/tests/test_save.py,sha256=bDd0i5iKE6FFEi4vcAvkGdyKTI--DrwKz2sH_hxyybA,5043
statsmodels/tsa/statespace/tests/test_simulate.py,sha256=5fbRz-zq2_7eJicE33VC-PTrFBF7Xb8cZK68gE7zju4,73325
statsmodels/tsa/statespace/tests/test_simulation_smoothing.py,sha256=I98S524ECcGn2n_zfpzTwARogF16FARO3_Gq5V1ai2Y,34428
statsmodels/tsa/statespace/tests/test_smoothing.py,sha256=MdQe8QcbTR_6if40-stjboe9magkzJ2rtt46n3LSfqU,62934
statsmodels/tsa/statespace/tests/test_structural.py,sha256=l9l37tjeM0lmvZ1D5aDnQGihn0N3M_CFvKzHdf3eM1E,26737
statsmodels/tsa/statespace/tests/test_tools.py,sha256=Spn-g7n5W1s7jIUIPeMfx6NqVbLaOBti9yl0_iM7rdo,31308
statsmodels/tsa/statespace/tests/test_univariate.py,sha256=XP6nVQonsEOqxae807pChcbVBYJVHYxKMQxrXkCwqZ0,27618
statsmodels/tsa/statespace/tests/test_var.py,sha256=j8_tTZ0004BHw7qthC0JU6ObkuQoafCb3X26MusNbOQ,8680
statsmodels/tsa/statespace/tests/test_varmax.py,sha256=FRvX5stqZM9GLJjw3967ebp08hjuBYjv_q1lxahCPw4,50382
statsmodels/tsa/statespace/tests/test_weights.py,sha256=a_8H9ZrMOPiRDrGwY7n0MvX0XHIeoqlB8esqtBintmg,21882
statsmodels/tsa/statespace/tools.py,sha256=i1h5r8ul0kQz4Gpjj1EtzTumKNnSxxR7_H4nc3mflw0,84271
statsmodels/tsa/statespace/varmax.py,sha256=Vgw8sjJguMa_yhNpqmFC0nTvRiIwKXDobmINVby1dSQ,52064
statsmodels/tsa/stattools.py,sha256=X7NwLeO4nhbWrou15hByesp4-LRBlUZ6iWIY7Unip9Y,99241
statsmodels/tsa/stl/__init__.py,sha256=FNIIWqMJDn_RaWzLhb1qyMHw6Oh_msp2OmLojqBG2_M,82
statsmodels/tsa/stl/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/stl/__pycache__/mstl.cpython-312.pyc,,
statsmodels/tsa/stl/_stl.cp312-win_amd64.pyd,sha256=ZZe1k2IKbXWKWHmIY2E79Zv9CqPZtxIrkhL09I-ZojQ,215552
statsmodels/tsa/stl/mstl.py,sha256=Oonfu5nV8xRrW-1L20Ru7SJk6MeE2NYLAxzIgtFQ3Rw,10338
statsmodels/tsa/stl/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/stl/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/stl/tests/__pycache__/test_mstl.cpython-312.pyc,,
statsmodels/tsa/stl/tests/__pycache__/test_stl.cpython-312.pyc,,
statsmodels/tsa/stl/tests/results/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/stl/tests/results/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/stl/tests/results/mstl_elec_vic.csv,sha256=TXWwuaTG8-N9IBodF-ReX1ZGCFqNIHy-w2gUpI7ljtg,125118
statsmodels/tsa/stl/tests/results/mstl_test_results.csv,sha256=ZfbJcO2MH2Xrotr0ZxjKWLvU2ZsdHmo0Yq_J-2Yf0as,295424
statsmodels/tsa/stl/tests/results/stl_co2.csv,sha256=fwaR1_GXdhLziqvKGIqw0mqzC3ETv93hXa0TdM7OJmI,2745
statsmodels/tsa/stl/tests/results/stl_test_results.csv,sha256=KwMrMLIAEIhPEgH8dpxSibn70hgV-_yJ1rplu1E9uLw,265450
statsmodels/tsa/stl/tests/test_mstl.py,sha256=5HaIYdXCpy-8iEJIltVKs_NopC1mrv2LHSvFl6WQqek,5317
statsmodels/tsa/stl/tests/test_stl.py,sha256=O_Yqy7gX2a4sZIaW04R8fRVWLFtGHhkipg-Be9LvCQo,11780
statsmodels/tsa/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/tests/__pycache__/test_adfuller_lag.cpython-312.pyc,,
statsmodels/tsa/tests/__pycache__/test_ar.cpython-312.pyc,,
statsmodels/tsa/tests/__pycache__/test_arima_process.cpython-312.pyc,,
statsmodels/tsa/tests/__pycache__/test_bds.cpython-312.pyc,,
statsmodels/tsa/tests/__pycache__/test_deterministic.cpython-312.pyc,,
statsmodels/tsa/tests/__pycache__/test_exponential_smoothing.cpython-312.pyc,,
statsmodels/tsa/tests/__pycache__/test_seasonal.cpython-312.pyc,,
statsmodels/tsa/tests/__pycache__/test_stattools.cpython-312.pyc,,
statsmodels/tsa/tests/__pycache__/test_tsa_tools.cpython-312.pyc,,
statsmodels/tsa/tests/__pycache__/test_x13.cpython-312.pyc,,
statsmodels/tsa/tests/results/ARMLEConstantPredict.csv,sha256=WZLhLTFLZK2xSwyLGmr97N6B1grYP10X0flr1H1Rdpk,5029
statsmodels/tsa/tests/results/AROLSConstantPredict.csv,sha256=2fHR3OKOKSzfJm16472ohF4lb2OzAVtyiJ-WwCc7zPI,22084
statsmodels/tsa/tests/results/AROLSNoConstantPredict.csv,sha256=YgwS8YLhBK_iKZswezQpE5ZN4A6N689L-R_pAUlVSf0,21383
statsmodels/tsa/tests/results/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/tests/results/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/tests/results/__pycache__/arima111_css_results.cpython-312.pyc,,
statsmodels/tsa/tests/results/__pycache__/arima111_results.cpython-312.pyc,,
statsmodels/tsa/tests/results/__pycache__/arima111nc_css_results.cpython-312.pyc,,
statsmodels/tsa/tests/results/__pycache__/arima111nc_results.cpython-312.pyc,,
statsmodels/tsa/tests/results/__pycache__/arima112_css_results.cpython-312.pyc,,
statsmodels/tsa/tests/results/__pycache__/arima112_results.cpython-312.pyc,,
statsmodels/tsa/tests/results/__pycache__/arima112nc_css_results.cpython-312.pyc,,
statsmodels/tsa/tests/results/__pycache__/arima112nc_results.cpython-312.pyc,,
statsmodels/tsa/tests/results/__pycache__/arima211_css_results.cpython-312.pyc,,
statsmodels/tsa/tests/results/__pycache__/arima211_results.cpython-312.pyc,,
statsmodels/tsa/tests/results/__pycache__/arima211nc_css_results.cpython-312.pyc,,
statsmodels/tsa/tests/results/__pycache__/arima211nc_results.cpython-312.pyc,,
statsmodels/tsa/tests/results/__pycache__/datamlw_tls.cpython-312.pyc,,
statsmodels/tsa/tests/results/__pycache__/make_arma.cpython-312.pyc,,
statsmodels/tsa/tests/results/__pycache__/results_ar.cpython-312.pyc,,
statsmodels/tsa/tests/results/__pycache__/results_arima.cpython-312.pyc,,
statsmodels/tsa/tests/results/__pycache__/results_arma.cpython-312.pyc,,
statsmodels/tsa/tests/results/__pycache__/results_arma_acf.cpython-312.pyc,,
statsmodels/tsa/tests/results/__pycache__/results_process.cpython-312.pyc,,
statsmodels/tsa/tests/results/__pycache__/savedrvs.cpython-312.pyc,,
statsmodels/tsa/tests/results/arima111_css_results.py,sha256=MXWzTEWLnhv4kuzE3zpejCnctZCWDe9RVjPu1NVaLKU,28278
statsmodels/tsa/tests/results/arima111_forecasts.csv,sha256=42iWT-ZPRwTKbDuJuVLIV8-GQeXg0Yih8xAK9-u1nWs,6809
statsmodels/tsa/tests/results/arima111_results.py,sha256=janGR1SP8ncl-2pcwTybWJHD-FxBi1Z7NeiatmEKu5g,28268
statsmodels/tsa/tests/results/arima111nc_css_results.py,sha256=cc1WF_BXPfxiV5dePRS5gvwxgMt5xamo95EqZDeO2bw,27997
statsmodels/tsa/tests/results/arima111nc_results.py,sha256=X1n_tzaudW9NZEPobrtP-ETs0tRkTQDqfjdRVdodklI,28000
statsmodels/tsa/tests/results/arima112_css_results.py,sha256=uYAHNV61GI9y4DzkS7tJ95NfFYiwGYY4ibnJBo4AmBA,28489
statsmodels/tsa/tests/results/arima112_results.py,sha256=MTtzQK6lNOoPEak-920LkbJqYm0inElydBZCdDdPiOk,28491
statsmodels/tsa/tests/results/arima112nc_css_results.py,sha256=omlvyHdWgx051n2KLWil-wnHB0WsdgDY_gA3QJMj7os,28039
statsmodels/tsa/tests/results/arima112nc_results.py,sha256=9GNzjdUCmFS0q2Y0j-i6am43sA_RYU5sa8MwGAEZCJk,23716
statsmodels/tsa/tests/results/arima211_css_results.py,sha256=rXXPAgE278lph3FYYuawt_Bt0srPxgKLKTiXUfXymMw,28479
statsmodels/tsa/tests/results/arima211_results.py,sha256=nGS48GTNSf2nbTpWM1gFdSfYfTxY_ALS_wz4cTCqY28,28472
statsmodels/tsa/tests/results/arima211nc_css_results.py,sha256=zncASuvjAClJErOvGGpKe6bPzFfisHMgI2pbjWzuaOw,28167
statsmodels/tsa/tests/results/arima211nc_results.py,sha256=ptyLQdhEYRKwJPO8Soi2Hq1uIph2lpalK0X81elVxm8,23717
statsmodels/tsa/tests/results/arima212_forecast.csv,sha256=77Z_csY8pFMdFmb9fHpVZmbPpLUQM3hviBcUXyalgpI,6509
statsmodels/tsa/tests/results/bds_data.csv,sha256=5FB-UQcCWEZitWROTErLC5Nh7EkSU7RgfHqJInL4Rsc,4236
statsmodels/tsa/tests/results/bds_results.csv,sha256=nWAEMmV4aI3gwLn5BKGA093X9qvtsZdC7WLJ1y75w08,1485
statsmodels/tsa/tests/results/datamlw_tls.py,sha256=9HdxbECGjQODD42JbqYQnDWRHOQ8qxtkZmM35ml3eME,6939
statsmodels/tsa/tests/results/fit_ets_results.json,sha256=HGbP_KjQFUAa6l5s744M-nctD1A-_nyJVRDOEWGpmAc,119955
statsmodels/tsa/tests/results/fit_ets_results_nonseasonal.json,sha256=uO0NZVcjYbUrC-WPvouUi-I_12gF51e2GAWaIm1uRrI,26804
statsmodels/tsa/tests/results/fit_ets_results_seasonal.json,sha256=aeKZ4nTqXtbeJEH-uTxv0QvetLHuOrqaL7iRiaJKdF8,86559
statsmodels/tsa/tests/results/gnpdef.csv,sha256=Og2N6jv1HvyMvFsjcu4eNGQ7rGQNBn3GpAtqBHKDbeE,1069
statsmodels/tsa/tests/results/lutkepohl2.dta,sha256=cBGaDynLKWOUmikCn7bm9egv4yFu7cW8TNQIYUqyTyw,6000
statsmodels/tsa/tests/results/make_arma.py,sha256=Yz6uqEQzUC72CzUmgDOkyLpeVci544u_OG2WnEhDk6w,1923
statsmodels/tsa/tests/results/rand10000.csv,sha256=0ksCLY2N1KOau5nRW8nv3LPPdZ2jozPJslnJ0tLFXmw,133713
statsmodels/tsa/tests/results/resids_css_c.csv,sha256=Ow939hHpod2Ux2Y58MMpok5OoSx-bleeMhQo4K7gOdI,20403
statsmodels/tsa/tests/results/resids_css_nc.csv,sha256=V-09gSJ7j_r1jWyjM9OrHaF3dUUlaG_2sNWeDfPjIUQ,20414
statsmodels/tsa/tests/results/resids_exact_c.csv,sha256=I_WKOWzZqAq3wuRoR_Rvsac3BSpu6LtiYmf_LsyTmDE,20538
statsmodels/tsa/tests/results/resids_exact_nc.csv,sha256=88IOFpDPwPKFhP719aFi3kHPYQP86Lv7vX8ZGIDGYes,20557
statsmodels/tsa/tests/results/results_ar.py,sha256=mLSgzfKY6VIb40Fei1sP2frDIHLommAn_e_d5WqlRuE,9555
statsmodels/tsa/tests/results/results_ar_forecast_mle_dynamic.csv,sha256=yYNmcZWPshp4ChK_b9wveiJkgFwv0gYf2HduLXO5usM,10857
statsmodels/tsa/tests/results/results_arima.py,sha256=Uig2gGW28UNKKcNo7WCeO_O0Cvyx2fvvoT5vRyCEEcc,23808
statsmodels/tsa/tests/results/results_arima_exog_forecasts_css.csv,sha256=hj7tTcOvG36-Sy9bELYzEglrv_zyFD_DZD-eIOmrvcY,4497
statsmodels/tsa/tests/results/results_arima_exog_forecasts_mle.csv,sha256=N6jOruK-wP_ZL7QMtpUdiEtoKQx6XdzTVEZlWlms4IQ,4762
statsmodels/tsa/tests/results/results_arima_forecasts.csv,sha256=N9detebCcxriQPkyqOEwH25Itz-Ni9o6ExOh0EeEjnc,23814
statsmodels/tsa/tests/results/results_arima_forecasts_all_css.csv,sha256=U_eX8Cwpg3w08uh_tnUgmBvtATRBY3TIAkM-dSe2S98,15307
statsmodels/tsa/tests/results/results_arima_forecasts_all_css_diff.csv,sha256=p_NiKxNLWLUQ4TQ-tAQGMhWYkswHhE5fJ2WwzxbvkS8,14456
statsmodels/tsa/tests/results/results_arima_forecasts_all_mle.csv,sha256=prYnw1fRwls0DImt_SGpUIQAL7x0GqukLQdqIxyc1uo,14242
statsmodels/tsa/tests/results/results_arima_forecasts_all_mle_diff.csv,sha256=gXHmDJEDL1MT7iJeqIBOW2fIZ63vX61iPAzoUKG-o1k,15707
statsmodels/tsa/tests/results/results_arma.py,sha256=NAnVagdH63Gea4eVdEjzNf14D95RZR86rfdTjfJYr3E,43235
statsmodels/tsa/tests/results/results_arma_acf.py,sha256=NmMNq3PnIxprmUp6NmupT6g66Ns1LFY-FfDgEkPR5Js,2054
statsmodels/tsa/tests/results/results_arma_forecasts.csv,sha256=XY-6Rf_NsudrTI-xt83y7xGwdHyOE42N44lJD3eNF4c,1325
statsmodels/tsa/tests/results/results_ccf.csv,sha256=a3vPrGJpg16W37Vj-CSMp9ZLcbIKmCVWkvsj1rLOVu0,408
statsmodels/tsa/tests/results/results_corrgram.csv,sha256=rsgGGCQpSAjaH_JOJioJX53NZ9trHvKCqudEVxEC3o8,3104
statsmodels/tsa/tests/results/results_process.py,sha256=RdQl5Pgyb1mXRvrkBSPmra1DX_5rPb3ES3jr0rBJbjs,1431
statsmodels/tsa/tests/results/rgnp.csv,sha256=Dq_YntXyW405DodiIpEYyOiIJ1it4Mz-14h0ZuKYy7g,806
statsmodels/tsa/tests/results/rgnpq.csv,sha256=k3mxXzVV5lXX5EA2hNR0e1xk2zVmGGSKxkL6GYPujsI,2063
statsmodels/tsa/tests/results/savedrvs.py,sha256=a_au9Xw7HO5rlT9i1xuAQFf1hZWL_dUQk2bSxGBjwns,19619
statsmodels/tsa/tests/results/stkprc.csv,sha256=7fCknv1gbYU-Rx0Sxf3-pzTFmj6szfAosw2GXtTvn6o,1308
statsmodels/tsa/tests/results/y_arma_data.csv,sha256=Q4FCU2ExuyL7rA4qCIBVp5bao8kjhSId1Q1LKVC7Y6E,76402
statsmodels/tsa/tests/results/yhat_css_c.csv,sha256=G51I8w7f0wrd7ULn8vZX8j1AJbguZvsq9VAoN_cWpSQ,19817
statsmodels/tsa/tests/results/yhat_css_nc.csv,sha256=bAQsH6FIVYGipe0_V23S7P8D2ozKRfjNPmP2x8zQvx8,20524
statsmodels/tsa/tests/results/yhat_exact_c.csv,sha256=fQOqyoJlwMzUqNqkxOImAnYca-V_psERnWQZwgQss1o,19946
statsmodels/tsa/tests/results/yhat_exact_nc.csv,sha256=yAfIBBAoUV1c6wxk2frhYIWcB8GtqOCCYccezhGmqG0,20664
statsmodels/tsa/tests/test_adfuller_lag.py,sha256=I6JeEWSIiLQkVgrum8No-pJf8GZhM7icwEH09oTS1Ck,1847
statsmodels/tsa/tests/test_ar.py,sha256=eFnid5JyxoQc-nJOiWm3MhQ0dkyMgbPW4eQmrAbZFu0,45040
statsmodels/tsa/tests/test_arima_process.py,sha256=pTq-7IpohJLVtmwh9olr2L-6D94NRri0M6J7Wp8NLzs,17907
statsmodels/tsa/tests/test_bds.py,sha256=yuE6cgCgzVkfJ3aqGdxMsO0MaCFSHKs36boU-nwIWP4,2816
statsmodels/tsa/tests/test_deterministic.py,sha256=TpCnciXdx7kUpBD5ASvFkmUFImDM911Q2KJ4e_r8OZQ,23848
statsmodels/tsa/tests/test_exponential_smoothing.py,sha256=Qo93ve6NzUs4fOuulgZxNq03En9d8cs_Rg-LHZl4QLI,33566
statsmodels/tsa/tests/test_seasonal.py,sha256=F4tdnPE9dGCyl6ST_jSaTamRupRdqkCzvmmTGvFqsLE,16267
statsmodels/tsa/tests/test_stattools.py,sha256=9sQY6_62atpfKmBcMG_F5jY083tfipO5U7VCkZ6ZNN8,53535
statsmodels/tsa/tests/test_tsa_tools.py,sha256=GfoIRsDXrpM71ci7U7EqOZga9q7n48LVwn6tH5pxMR0,29579
statsmodels/tsa/tests/test_x13.py,sha256=_SmW7AfEQ6q8TtveW6X9yNXY2FT2nGyndOswLS8FqWY,1611
statsmodels/tsa/tsatools.py,sha256=godirIIQJ43OFiZGzxGXm7GSBtPlFvlAUMrdYari3K0,24063
statsmodels/tsa/varma_process.py,sha256=P093dpByvU72mqbzmAZQE3D-w5e2eOi1fCaOIhW_GbE,20410
statsmodels/tsa/vector_ar/__init__.py,sha256=FNIIWqMJDn_RaWzLhb1qyMHw6Oh_msp2OmLojqBG2_M,82
statsmodels/tsa/vector_ar/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/vector_ar/__pycache__/api.cpython-312.pyc,,
statsmodels/tsa/vector_ar/__pycache__/hypothesis_test_results.cpython-312.pyc,,
statsmodels/tsa/vector_ar/__pycache__/irf.cpython-312.pyc,,
statsmodels/tsa/vector_ar/__pycache__/output.cpython-312.pyc,,
statsmodels/tsa/vector_ar/__pycache__/plotting.cpython-312.pyc,,
statsmodels/tsa/vector_ar/__pycache__/svar_model.cpython-312.pyc,,
statsmodels/tsa/vector_ar/__pycache__/util.cpython-312.pyc,,
statsmodels/tsa/vector_ar/__pycache__/var_model.cpython-312.pyc,,
statsmodels/tsa/vector_ar/__pycache__/vecm.cpython-312.pyc,,
statsmodels/tsa/vector_ar/api.py,sha256=Dabns7Ie7TuT6qDP5I7s9_cMSnQOmmYzewGqjIo9WEY,87
statsmodels/tsa/vector_ar/hypothesis_test_results.py,sha256=78iaLjQAJ0BhvLgXZUm5Ip4pLtLEXOeQcUBf14eMjLs,7547
statsmodels/tsa/vector_ar/irf.py,sha256=fNPXtUlJC9qzO5DGvKoMFQhqqN84mZuLvTNuL5mh34Q,25083
statsmodels/tsa/vector_ar/output.py,sha256=8kA9oHKwuZ2jZqys77HIR7w3_yoMkBDKPY3Hf6TyG_M,7185
statsmodels/tsa/vector_ar/plotting.py,sha256=55Y9pR5pc7ZozjOFf9bnfJAesml9_m6rOpljznv7WC0,7760
statsmodels/tsa/vector_ar/svar_model.py,sha256=F8bjp-JEpULyuy4F7zEdFtfKl0J6X8R6kJh5qRURuY8,22918
statsmodels/tsa/vector_ar/tests/JMulTi_results/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/vector_ar/tests/JMulTi_results/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/vector_ar/tests/JMulTi_results/__pycache__/parse_jmulti_var_output.cpython-312.pyc,,
statsmodels/tsa/vector_ar/tests/JMulTi_results/__pycache__/parse_jmulti_vecm_output.cpython-312.pyc,,
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_c.txt,sha256=yLa24_ThJd2YAPkb4XpyHY3738SjKQ7A2kDiziflcyc,3589
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_c_Sigmau.txt,sha256=ia1Lwk7lVEOCcIDwTZQczeUJ0aHqYCA55T1VsnOX4Ls,665
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_c_diag.txt,sha256=rBcWHcuGHU_ilyjeFL-FadlRjwF-DrjwZybvirbQne8,2312
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_c_fc5.txt,sha256=KnpQG_0_d6T7-pi0oygMGdRocra1ahDlFyaCJveVEP0,1523
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_c_granger_causality_realcons.txt,sha256=mHb4lQPIsuw5NyNE4Wkr0r5zgR0cu7ZDjb0_Yuw2mKM,350
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_c_granger_causality_realcons_realgdp.txt,sha256=Jlxa9CW8WL8JTbYutgVD4sKjIJ06F4PMTStIxM2fC9o,350
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_c_granger_causality_realcons_realinv.txt,sha256=gd5uTrc5hftqZXWf3QJXZrgjQg__4Lx94euWLTGJLvw,350
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_c_granger_causality_realgdp.txt,sha256=zPUFlwEAXnH7tSu_0vG9LG3AjIN1tWPL0DKKVs67I_0,349
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_c_granger_causality_realgdp_realinv.txt,sha256=rJfkHuUday016aH-s3hGb_ZXjHvwC_4itBJ_EeASk54,349
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_c_granger_causality_realinv.txt,sha256=0jFVu3i0U9O3NRmRBP22hwYNk6KB7pnLKeWqUQNfEFs,349
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_c_ir.txt,sha256=umN_fDRulQcvsplePC5l0dI2Nu_cgAvHCEajLFMzMX8,4768
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_c_lagorder.txt,sha256=H6Z1ZpF84bWMLBCPwl_Vnabdvb1f96G9F_sGJxzs-hg,457
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_cs.txt,sha256=n_5U41soYc69joWErxWMrU9Rxg98VrTgTARFWAgXFrQ,4102
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_cs_Sigmau.txt,sha256=Yz1P_Lg7tg0AvuF0DD8C_cRyXh7NLcMaUA29SM2DX5E,665
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_cs_diag.txt,sha256=bYPOpo8L54qaRsbUPtorc_h8VBiLv8bokr7jKnfDMdU,2312
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_cs_fc5.txt,sha256=loxjIC6LEPPYRl8qMndrJPGBUHjrrrFKm572IHLAjag,1523
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_cs_granger_causality_realcons.txt,sha256=b_HjLVyH-p4mmFkj1dGO92_6EPKmYcMgb2N3c0go6gE,350
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_cs_granger_causality_realcons_realgdp.txt,sha256=RsyhtWrQn41N1RSlwzB8A1fDJfJcffRqL7JxF-Abg9U,350
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_cs_granger_causality_realcons_realinv.txt,sha256=lIS4nLOCOr_iNrIZNHyfyCsxrtjtPs9_FrHff58YbOw,350
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_cs_granger_causality_realgdp.txt,sha256=RRpbo0jN44SrYjm7zi9MgY5OF4btipCn2oHPK-wTS6A,349
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_cs_granger_causality_realgdp_realinv.txt,sha256=GYaox6cNwPM9pydU_qwl3YX_kdgFVqOaivwbIk2-OwM,349
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_cs_granger_causality_realinv.txt,sha256=gJCKzsBjEHeYW0trWkcO2p5IUmM6GeMJ-e24uNPO0h8,349
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_cs_ir.txt,sha256=Sla8axDYgndQeSFEAHiMZEuzSFg53NMVPlxA67jWHJw,4768
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_cs_lagorder.txt,sha256=7WzaQKpVMqwx6fup6QYwVCu4WHG0WSpFxCqs3g-VVgU,466
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_cst.txt,sha256=PoGg8MyLqpCL9DmzOImgrR6Hfv2jT2eWERBiBBk_v2I,4276
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_cst_Sigmau.txt,sha256=Gkj8b6Fy6ogYQuUMyMY6kRtT0NMCBnW1VB2_2MqAL4E,665
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_cst_diag.txt,sha256=PFBEcIHObQx6bobeeYwoZsGrasFvBya0OZxcD5tbD0A,2312
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_cst_fc5.txt,sha256=NjxCgMhHmg2lIfQWnjcxVt6RsbSY_qBGQUJNW_LfeMg,1523
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_cst_granger_causality_realcons.txt,sha256=QJrCHTKgzI8IWnH2eiygDF0S2GmOvEST9OMG806eMz4,350
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_cst_granger_causality_realcons_realgdp.txt,sha256=q6HYd6SnxvxsvhugQ6Ldr5HLoFyAfnciHgSvfqcUCZI,350
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_cst_granger_causality_realcons_realinv.txt,sha256=oljfFRpbdys5CTutO4Z0rrICP00XVk_R6RhDpgdLTAE,350
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_cst_granger_causality_realgdp.txt,sha256=OWGQiKiKbyoCYYnoMc-H3iAtTQs7aoTFkaLno19nKHM,349
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_cst_granger_causality_realgdp_realinv.txt,sha256=KUnQ9pQu57b3HKeFTH0zv5MMbAqF9qODFH8rChiyU5E,349
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_cst_granger_causality_realinv.txt,sha256=w-vIpdIbsM5ZxStYxTOLNmF8_sYGR_qIGsYSaEZaFYs,349
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_cst_ir.txt,sha256=uDisxFs8tw0TMGgKuCTV3paDREOxdc3xCRD2js8ZWHk,4768
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_cst_lagorder.txt,sha256=HMBLTgMjSwaB7e_kHp_BCg_eSTHRH2fEbmMIsDhNKIg,472
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_ct.txt,sha256=vvQxdwkRCYf9OATHhhoex4D74M4GAtwwnn-52ESpnxE,3763
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_ct_Sigmau.txt,sha256=Qxg6Iz1epfZaogpUwgUNif7tc5-Oc1JD3Uq0bAjMPDU,665
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_ct_diag.txt,sha256=GJuNVWyHhGF4kZwZJy-R4NGqJfirbcydSW829eGC-YY,2312
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_ct_fc5.txt,sha256=-CyPngwxgae4AWOMCubbuz_PhksfwaZipbgXMNhJdxM,1523
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_ct_granger_causality_realcons.txt,sha256=0bREPfZ1XMSh4X4G36uY5x7lttBa4BORW_2HmbAA9O0,350
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_ct_granger_causality_realcons_realgdp.txt,sha256=D43KViQPmAjngnFLXgqB0gEFBd5GIs57xocWUf65GUE,350
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_ct_granger_causality_realcons_realinv.txt,sha256=SWmoKPqnxs61oZrRi5h0ETMQ6KC4pG77KcFq4yUaxAY,350
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_ct_granger_causality_realgdp.txt,sha256=ZxSFYqh2m7KngETmwkaqWLk81c-6X9DYkOy1EFxDoMg,349
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_ct_granger_causality_realgdp_realinv.txt,sha256=hM6lVnWzkV1I12tc83YZwYO65nTy6W998CMy3w1xrQA,349
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_ct_granger_causality_realinv.txt,sha256=WDWa2CoTa3S2OxFlZx_WnHVcFosLko5_B4cLad17FiA,349
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_ct_ir.txt,sha256=d4l3xrVm6ADVkREAXpj2zAZ3ZFeJ8_fZuP_U9oiETow,4768
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_ct_lagorder.txt,sha256=Ly0UI2Nj_2ttXJCqBDt9Nwaek243hblkwCXuP0hBWHg,463
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_nc.txt,sha256=WhmbeqVS2Mm0hN5D2ev66UAWYWeQWGMGbWccU05yK7U,3250
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_nc_Sigmau.txt,sha256=qLsTWox9GEudS0rjQUk5FRDfoYA5MfT0bJ9_hRZd6IM,665
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_nc_diag.txt,sha256=8Xahr1BLGNy34lPX9Dl-8oCH0a4GXI2worTX6rCGodg,2312
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_nc_fc5.txt,sha256=7owxvX0RL_jVZOrE17BGuqiSkaED2mFK6HBEdoXQtk0,1523
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_nc_granger_causality_realcons.txt,sha256=vK6-OhyeWIKK5_DOy1Xt4U3AbgerMVKEaaNb-iPNSj0,350
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_nc_granger_causality_realcons_realgdp.txt,sha256=rio41v2cMdy-0wVakUDFT9MChwMmy2_5C-FWuPZHjjI,350
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_nc_granger_causality_realcons_realinv.txt,sha256=LGgFMukW5rZJDbdnAJy_JD9IUZYqjhs5YCDVOYL5IiQ,350
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_nc_granger_causality_realgdp.txt,sha256=LpHrXLdB3nOizcFY3gfSoCuzWfcuIG2RtMvU0ajbUAo,349
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_nc_granger_causality_realgdp_realinv.txt,sha256=Npk82ivmHR3TRSe8KpaY9pK3kBqjuHzrxv0EefMifD0,349
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_nc_granger_causality_realinv.txt,sha256=jdQAYqatsgE6IeCHLZTrFU4qQBIG7h0ukpCC1mwSydY,349
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_nc_ir.txt,sha256=6zcP7_yF3z7Jlno6YU7zxg_ahq6ShICOVBB6ZWgwc6s,4768
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_nc_lagorder.txt,sha256=oV0VD_XVFiFApx7OuJIGSTiKmk04uqPWVKiIIODjHbk,423
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_ncs.txt,sha256=IvQMo_DuPyByNoJPojl5fieUUgAfXNlTiZLzOFoVjJ0,3928
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_ncs_Sigmau.txt,sha256=iJFA2r8EzzFUrD0MnvSfFioIeRSXc_5v0mtNC2OPPL4,665
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_ncs_diag.txt,sha256=zUVnsiNcJp8blGtPz3wTNvp_sd8sklhLdExFXyRoAf0,2312
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_ncs_fc5.txt,sha256=uvj4XxcRcTjns_eTNutw2TPZ6yJVce_X-MViqF28rio,1523
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_ncs_granger_causality_realcons.txt,sha256=8IqCwWaOjkDE0zUTbNtI4bQmPEb0vDP9CW7cJA9totE,350
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_ncs_granger_causality_realcons_realgdp.txt,sha256=gADV9d6YA7iZaK4ag_Z8cT3k9YNQMos-EliqxgHvcTk,350
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_ncs_granger_causality_realcons_realinv.txt,sha256=jKDUAPgizwmeUQ_RdHXd6v_bTv29FM4bDjK63Fiv-5Y,350
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_ncs_granger_causality_realgdp.txt,sha256=vKLEns_s7OJV1JopTCjKEYHzOYcfOuFRd5rOdG9V5NA,349
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_ncs_granger_causality_realgdp_realinv.txt,sha256=Mxcfv5zLs5R9VWyk1geuhNQspuZjecx3kTnMhVSUS-4,349
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_ncs_granger_causality_realinv.txt,sha256=3NS8Ixhf7dlamPqz9bA8Kwh5JauDdBNJO9TOlo_ha1g,349
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_ncs_ir.txt,sha256=blbujN9DyzOUezjUO6hms9ch5sGp3sj-JYqZtdifetY,4768
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_ncs_lagorder.txt,sha256=zEECiOBfWwmFs0r6s-FSm8IsOBF1Nd8AxX9IoMusoh0,460
statsmodels/tsa/vector_ar/tests/JMulTi_results/parse_jmulti_var_output.py,sha256=-hEjhB7sbhrNvYFncfEF3GgEUX0V_TPViUb4RCSywc4,18402
statsmodels/tsa/vector_ar/tests/JMulTi_results/parse_jmulti_vecm_output.py,sha256=waZS4a-xkLpG-hscZHp-J9mICK8Ocz7yGbiQepWO120,24192
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_ci.txt,sha256=RWkGybNzbP_o43J0ZxxnVCErUU7byEIgSOJmfvCEkd0,3911
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_ci_Sigmau.txt,sha256=ja7Veac8DX1WgtObNSheTudxdZGvIF0FAqTkw7o5iKU,385
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_ci_diag.txt,sha256=ksZwtEn2luE-ouDLEaIGbUKvtDhzJ0dUvJeBOFGOyPo,1879
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_ci_fc5.txt,sha256=Et-0cBtTxxrXAevb7HsW_6UVeRDQLOpTzp2UkxDoWoM,1091
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_ci_granger_causality_dp_r.txt,sha256=WZcUTo4qiMgJ6pXcNKU419EanM9AU91d9KCLOHocn8Y,307
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_ci_granger_causality_r_dp.txt,sha256=a5KXV4lHFlemWLzwplrDqOKPQxnq18Wk38ydLM-UlVQ,307
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_ci_inst_causality_dp_r.txt,sha256=ImJq7vfeTliy8zJ9a9MYeONfk0UeDrI6THmQt5kmUy4,307
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_ci_inst_causality_r_dp.txt,sha256=7Vnyv_XTHxIpxkzKoXymOpvKxu-ehPZ-4uWwWFDSdBA,307
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_ci_ir.txt,sha256=8pSEyBYsL6lhxPWo0vZUGkIlPIWpYVG4K-zT0WzW6rQ,2470
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_ci_lagorder.txt,sha256=6N2FnB2FcePYRaMwtGE81gpUsbnN4kRIBgOvvpw8RDs,445
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cili.txt,sha256=xNmR1EPUNe-LMz8517y6NAMgGZHzD5EVi47LOAeQUAE,4220
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cili_Sigmau.txt,sha256=19OE4wXOXZxrCcMJl3s6BpjzlsBir9N_Rv4d-YWdlko,385
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cili_diag.txt,sha256=v28MYUk0cZbDxNt7zrtn0kB07Zkx5nJMoH2LVHt0zq8,1879
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cili_fc5.txt,sha256=mL9yaomQRYUxeIu-zFw1ne7RVjTJ9Hz8ursaEqihX10,1091
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cili_granger_causality_dp_r.txt,sha256=Xfi9J2DQqFXrDHP-W6_1Bbi2XJ4IztNtMeeaoszTVbo,307
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cili_granger_causality_r_dp.txt,sha256=teglOiEJTEbnD_YkBiwjuBhhTLhf7trYxoZpfq69OWE,307
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cili_inst_causality_dp_r.txt,sha256=EXPXqqDCPNy-jMKzFS0v_ieYMRn8AiLoVIcd_Cma7Lk,307
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cili_inst_causality_r_dp.txt,sha256=5SSP8So0U7v3egO40ONPyXCFkhVRj87EmnjhPy3g8Po,307
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cili_ir.txt,sha256=Bu_TTFGUFw6IylaTQ0P-TVU3BxnpXKcBWl2EX8Eet6A,2470
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cili_lagorder.txt,sha256=VKi5PhuM6L41AdzlGWMiWR9m86alTLRjsBnZmtqGp2s,451
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cis.txt,sha256=MNbsldAA_uGJ7HxEOI93U2ADzWU0aJQrIKAm4NVKsq0,4824
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cis_Sigmau.txt,sha256=_qEOU-IIncsvkUbbRMiFxichIphJ07yriwZ0Xmmemuk,385
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cis_diag.txt,sha256=Akf6YevkN_c2YpW1UsGuJDEtbIQvzIRJOu06hjk18uI,1879
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cis_fc5.txt,sha256=kya1bHi2YXGPx0EyNK1JT_LbyA3WUArmQ8yNpzQ6eJ4,1091
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cis_granger_causality_dp_r.txt,sha256=AjOif1NPCVU7xYP7cm9CJ7QqhX3C4RQBGBqcWuB-J0c,307
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cis_granger_causality_r_dp.txt,sha256=JYhznGd1OB9V4ore3ZUnnj-3m7XX3FJkTx91eTzNQng,307
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cis_inst_causality_dp_r.txt,sha256=xhklo-2wkBVyueOQ_qFXMt74e1zqwEv-9-nqlPmmG8g,307
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cis_inst_causality_r_dp.txt,sha256=_DxA26D1u8iZHOkMsCO3605jIVlYfk_kHY4S15GN9_Y,307
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cis_ir.txt,sha256=-GIBtY2IZphscHtZOzaJST6Mw9J8Y0ImRQlgKvhSCXk,2470
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cis_lagorder.txt,sha256=mF97ZZuzEJU3bcOG5vVTBJI3khCOAGVrGoLSAj71tQ4,454
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cisli.txt,sha256=Hj6o4BU-CbWTuDgqLI922G9SFy5ZDcdXlGWzrGvn2XA,5193
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cisli_Sigmau.txt,sha256=UZ0Ytc9trw13ZKgizzfyD4gtaQVvE82ubJy4YqBNHWo,385
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cisli_diag.txt,sha256=XGWpNvfVaiPkak1w2klnupa59U9ZNp2gG3I4XkerXXM,1879
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cisli_fc5.txt,sha256=EHO6zM2Ynu07h7e4pEOIYc93IMULWJ-S09aLVnsUZnY,1091
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cisli_granger_causality_dp_r.txt,sha256=JJlDVHzolzP9bfccnFeFR913y0ZAabb_omMh5mU4Ank,307
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cisli_granger_causality_r_dp.txt,sha256=rxXy1on5_IytD2homdoEHo5FwcJObdFGKQFJkI3tRgA,307
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cisli_inst_causality_dp_r.txt,sha256=r1VKnIljbAGkGxE7Bvb3sjXDNSDjZI9yLhdVLx-NbcI,307
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cisli_inst_causality_r_dp.txt,sha256=65FD3hk4q2Jkqlrheq5gzh14k4k7Y977rSQdHXIL1nU,307
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cisli_ir.txt,sha256=uP5GZkTc6bg2jISPv8p8keud3LmlzvwSdg3zV_mTvRg,2470
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cisli_lagorder.txt,sha256=2RryGj74HPLEBNyxfjvbrU9w7GKt0A9z51ps4NbZWMI,460
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_co.txt,sha256=l_ffeAMujx673b83tMxPkNdCXkuqzdoAZDVCGC7AmL4,4087
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_co_Sigmau.txt,sha256=kdA_vgy2ESF56UIyondJRLkbBx-DlrWZx5hWR3-VYZE,385
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_co_diag.txt,sha256=Noabfru36zltBmNIQuQ_LUVsWImcX2Kuqmyq8v5Dbzk,1879
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_co_fc5.txt,sha256=kobhbWvGKvnezrJQSP4L4X3uk567wIePW3goZLXQR_U,1091
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_co_granger_causality_dp_r.txt,sha256=T_pFXiVSK2XCO6FSfd4l7J82TTDkE4n3OtOLVLvWe3I,307
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_co_granger_causality_r_dp.txt,sha256=uz8clvUPxDjgXIEcZMjPr1U6FrlmdDAQovjfTiL8gnQ,307
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_co_inst_causality_dp_r.txt,sha256=0s3oshsozwI25ysVFYN4DRT0qxc0LAoBGt4qk3ynXBE,307
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_co_inst_causality_r_dp.txt,sha256=X6613cax6x60GAm2iUrFzYeOpMULi48vbDqb-LhjOJI,307
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_co_ir.txt,sha256=G700_WkxS7MGbfgYovYn1X3asxS438iJd_nxSD7jL6w,2470
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_co_lagorder.txt,sha256=G0Iplw2FWt_SsOmB_gzxGlz2pl3Zn2jGSQY-IMDd-es,445
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_colo.txt,sha256=0PX1rchCo9e1f9SfsiKNZxefNy1w-pfWlj9KWYmJikA,4349
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_colo_Sigmau.txt,sha256=0zpw1LecT99IrGz5HvZnKoMVPPaeWze1XXepYLAUaSE,385
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_colo_diag.txt,sha256=mOS-h5pXJ_dQgk8B5cz_pyxZwdj_fAHGPIgV3AGt-bA,1879
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_colo_fc5.txt,sha256=LhmqNNDodBgHOZHDsxHTm71IboN_VTstuHWWQfbTh6w,1091
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_colo_granger_causality_dp_r.txt,sha256=AaLWyRot1jw_17wqibaxZjohJQoRLNK6F0JIwvvXKIE,307
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_colo_granger_causality_r_dp.txt,sha256=qV79NF-AvALtYAV8svMiGsWxUNWoEsPyfzTXMM-WMFg,307
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_colo_inst_causality_dp_r.txt,sha256=42LZ6nah6o5N3PVAoSXryqHdnCS9Qei7_WjP2yqF_Us,307
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_colo_inst_causality_r_dp.txt,sha256=MTdmHoHZUNeMEMV7b_6-hD6daaUNeVGN6zCFmHvAoQE,307
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_colo_ir.txt,sha256=fiNaRjG9CEIXcryKyPAw3vvvmRvysdwWzvWx2oSFqBo,2470
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_colo_lagorder.txt,sha256=yCoS8NbaaAOJpdfvA5q2LYBPS_PVBx70BXWOkSMI0a4,451
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cos.txt,sha256=VsgfT0e4UTCeVNDdX1_Km4EQc24ipAua-hidD7QtutI,4864
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cos_Sigmau.txt,sha256=etxeHHROoFBN8vLA6KLNoIlWNXTiFN-hkKI2csuL-cI,385
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cos_diag.txt,sha256=boxDooMYD95htaY31RVbYmy8U5_2YXMVeDAvIg3Gevs,1879
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cos_fc5.txt,sha256=Fq0Xxuawku2Jbls0-1TEJe6jkIiswcb0SqGEkhDDSsY,1091
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cos_granger_causality_dp_r.txt,sha256=Uw58uX0hd27Z67Ld3-bcxnNnDMApGoyhM7SxoTyWeUI,307
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cos_granger_causality_r_dp.txt,sha256=s9oOwOlTSyce-YM7aVLC8YZECvIcWr2j-p1pTUdRRvs,307
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cos_inst_causality_dp_r.txt,sha256=CJSaBN2JWVdQL3ATB5IaGbSkr0Ux59n71_WR5G-Ub6o,307
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cos_inst_causality_r_dp.txt,sha256=3WbWa5uc_nHbK7BadFq0eANjjmoiaLnFQX4HSeLOE50,307
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cos_ir.txt,sha256=-DI1wIUdcnrkWMUTzXTeCdKhVz1vNKV1i-zUTrqRLs4,2470
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cos_lagorder.txt,sha256=RbqF1B5lPHYEGFVDrPUjs3MfijroKg31YFflJRlGpbI,454
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_coslo.txt,sha256=y2l-mzWpF3GMbUEZegAiA5e1bKdjHgU7oedfRXaNiDk,5126
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_coslo_Sigmau.txt,sha256=IOQvtlbAnP-zSG0VXH2OhQUi7pMd90vLB-PzFGjHtF8,385
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_coslo_diag.txt,sha256=2c9Wzjjh7zNMr6seaR5I2E1VLb5hsgZq4Np_8ZlQp48,1879
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_coslo_fc5.txt,sha256=RzJyWJEbT4Ea-ubyFZTAf6lT6bgY_6x7wu0Tk-uy1ZY,1091
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_coslo_granger_causality_dp_r.txt,sha256=OlydhQ_PbidovyIiHJfohMNI7DJt91jYrIy5UzRz-J0,307
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_coslo_granger_causality_r_dp.txt,sha256=rP_XellYekcAJBa4qsyp0Lf9speM1fdh76pZkjf6NwM,307
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_coslo_inst_causality_dp_r.txt,sha256=a8rZtJQpfPj9EQOE7ejfnRI2OoJNHN-InvT0NOpyE0E,307
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_coslo_inst_causality_r_dp.txt,sha256=AQyMxD28Kxrawv1jscC241IebkSaUUHmcAq2Qi0hi6w,307
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_coslo_ir.txt,sha256=Kbc5x3odwjcyOXaDBG2u7GEvTejZEV7rMCy7g5rOJD4,2470
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_coslo_lagorder.txt,sha256=glr3bB8CO25EOwrxgLOsWuForfEdSbhfIs4Re25gRKU,460
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_nc.txt,sha256=WoDkDZ2i5_m5Zjlv4Ti6yO3u5HvIqtW0IEIWvnNb10Q,3554
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_nc_Sigmau.txt,sha256=nk52WmE8tz7Nji6w2HkM85FkWZz2wWDl5xNMLfaJqRc,385
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_nc_diag.txt,sha256=0MA1BXVTBtmhqHa1XquTDEwYc0-2ONbOmin8AkWR7LI,1879
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_nc_fc5.txt,sha256=mLqf0slzfJ3WotCINQ_x0wwGA6QrWm5RWmkjWRUUT0Q,1091
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_nc_granger_causality_dp_r.txt,sha256=yRPVXCyoHWkFwVctn53Is8fCSUpVnp2yICYU-PYGXfI,307
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_nc_granger_causality_r_dp.txt,sha256=G4wcqGyHKTD3kR-OmyFD-XIDbNDmm1DOd0wcvfgBFxQ,307
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_nc_inst_causality_dp_r.txt,sha256=kGgEN1xZS6t_NCGL-BG7yGvtCda6V9aXdviNjZuhzT0,307
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_nc_inst_causality_r_dp.txt,sha256=fXTVzWN-LIZTw4p7n_TcXR71QaIp-iHoHP_uCBX0c94,307
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_nc_ir.txt,sha256=Xamq3njXdoaiQYF_Swd8Z77BD0mWu8fD6fA7wSrnk0U,2470
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_nc_lagorder.txt,sha256=h2JDTJrKiBYXJlGOJSRvWIrn1wzSgiYrkNH7NdThaas,411
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_ncs.txt,sha256=Jk2KLN5ha1jscumxdknwqUGOhtcFVBJCYfhiIgS4XIM,4602
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_ncs_Sigmau.txt,sha256=2uWtgbuENTkQRyZ8GAj8kR3q3WAZ5swGm03HPxZCX6s,385
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_ncs_diag.txt,sha256=c4BseRWfEHi_gcADocDSp9YEbCi8UtrDqDAuhmCxO_A,1879
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_ncs_fc5.txt,sha256=IqHQqmD5_M8i1V8MpAHGjv3ncG7lzxGRn1g6tXre5jk,1091
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_ncs_granger_causality_dp_r.txt,sha256=kW2IJSF-4C0EDvChKlNk5N6nY8R5xWoE-wu67Ye88MY,307
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_ncs_granger_causality_r_dp.txt,sha256=DncWnPI4a1O23QDZ-0SNgUOdaelHx7uwoBeMw0wDf6A,307
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_ncs_inst_causality_dp_r.txt,sha256=QgnQVE54yA0NU6D0psWNwNJLO0CXePBwoCTNCG73jn4,307
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_ncs_inst_causality_r_dp.txt,sha256=6OImYBXT-vhVeDU_EeAyz5q09I_HCb5L73AZyKQkFfk,307
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_ncs_ir.txt,sha256=yWFZi5jLDuFyWhmliB1raLJHHxoVTPksuI926G99gFU,2470
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_ncs_lagorder.txt,sha256=ORCSMgvIPDQfBp2z9Mntor5gODH_d4cp4Q7mNXCBaOw,448
statsmodels/tsa/vector_ar/tests/Matlab_results/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/vector_ar/tests/Matlab_results/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/vector_ar/tests/Matlab_results/test_coint.csv,sha256=8XPrHnGSanQd61DzF7h-pMOaoK8Axyeh5cRKob_cuwk,22490
statsmodels/tsa/vector_ar/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/vector_ar/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/vector_ar/tests/__pycache__/example_svar.cpython-312.pyc,,
statsmodels/tsa/vector_ar/tests/__pycache__/test_coint.cpython-312.pyc,,
statsmodels/tsa/vector_ar/tests/__pycache__/test_svar.cpython-312.pyc,,
statsmodels/tsa/vector_ar/tests/__pycache__/test_var.cpython-312.pyc,,
statsmodels/tsa/vector_ar/tests/__pycache__/test_var_jmulti.cpython-312.pyc,,
statsmodels/tsa/vector_ar/tests/__pycache__/test_vecm.cpython-312.pyc,,
statsmodels/tsa/vector_ar/tests/example_svar.py,sha256=6SGjk72Ws5iRm4ux28fBdk127Ew_Ex_EceLqLWGpiWs,1027
statsmodels/tsa/vector_ar/tests/results/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/vector_ar/tests/results/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/vector_ar/tests/results/__pycache__/results_svar.cpython-312.pyc,,
statsmodels/tsa/vector_ar/tests/results/__pycache__/results_svar_st.cpython-312.pyc,,
statsmodels/tsa/vector_ar/tests/results/__pycache__/results_var.cpython-312.pyc,,
statsmodels/tsa/vector_ar/tests/results/__pycache__/results_var_data.cpython-312.pyc,,
statsmodels/tsa/vector_ar/tests/results/e1.dat,sha256=xP3vFJJJzqWWZXxCcTzi9B3xYNI8Ke0bm1ni_xD8DZc,2702
statsmodels/tsa/vector_ar/tests/results/e2.dat,sha256=YinI7RbNNYJdR5WxtrpluJfVqJMrXhzyQulL8Mplacg,2165
statsmodels/tsa/vector_ar/tests/results/e3.dat,sha256=iIWFy9MWJFth7EkF3PkGYnnzHti4PoNzK3UGvoJHBKE,7765
statsmodels/tsa/vector_ar/tests/results/e4.dat,sha256=PM79mrP742gD6kIqgN74lUHZjkBE-zofxLSDEZoOvtI,3366
statsmodels/tsa/vector_ar/tests/results/e5.dat,sha256=E_Ku-OQpdRn-YHfhJptguC5BZkGxSEAAwCuf3MuJ_zk,12017
statsmodels/tsa/vector_ar/tests/results/e6.dat,sha256=Isi8nIOm7FKtnF0-OLgpYLcmRWBVPsRTLkXUK-scrPg,5197
statsmodels/tsa/vector_ar/tests/results/results_svar.py,sha256=wBeh2_Teodn5-w3bKtX1pbebvKbGycP5mX8xZpgF-Rs,402
statsmodels/tsa/vector_ar/tests/results/results_svar_st.py,sha256=qAn9nW7R42iuQK8H5aGmDgTNtJ_43w36jDrFnFT19v4,42738
statsmodels/tsa/vector_ar/tests/results/results_var.py,sha256=niPtMQnqtVjpzPmGCuY6vAxoGa7dnOm_WTBxnEJbx2Q,2714
statsmodels/tsa/vector_ar/tests/results/results_var_data.py,sha256=t6GIgEPblsjcSHShdPd6v1qmCa7IFEzaOSVN_D8oiPA,6910
statsmodels/tsa/vector_ar/tests/results/vars_results.npz,sha256=SAdu-Qivvqq9ULg6iSQ8ohp--uLkIkI-NZ2Bl9HZkw8,5696
statsmodels/tsa/vector_ar/tests/test_coint.py,sha256=-dD1zy4czmMJaNUV2V9igfUrqTtE86zOKgDmp0VEFXw,10065
statsmodels/tsa/vector_ar/tests/test_svar.py,sha256=OefT4laP7EqWy7OnKu6dEUp4t3I8bhMOkQRc2bluxZ8,2954
statsmodels/tsa/vector_ar/tests/test_var.py,sha256=XQ4JX0qMgYrFujkyT0OqaUTPrkcgQZv99Fqez2XlGV4,32002
statsmodels/tsa/vector_ar/tests/test_var_jmulti.py,sha256=PuP2HgznRP3K1ciuPcYxir2LP56z55kBElPRMnrNdH4,26979
statsmodels/tsa/vector_ar/tests/test_vecm.py,sha256=fHCbI2rdfkGoyADG7LlylVBRu4DXWH7VZddw8d2M4M0,79270
statsmodels/tsa/vector_ar/util.py,sha256=bcXfeYg4XtulYV87AoCjLh7vebsTZhD8C_Id3vv54Lw,11550
statsmodels/tsa/vector_ar/var_model.py,sha256=BfjWs87lM5vc1lfGhQbjZxTyQ33OzxCYjHlLZaQIn5k,79320
statsmodels/tsa/vector_ar/vecm.py,sha256=sBVqCtNK568_0-M6OyR_RMqo07pVNPAhBN-6Uhvne9g,96944
statsmodels/tsa/x13.py,sha256=1IaeE7q6YsrFNlgYlEmA7dLIv1UsTaLzgQ9yiEyShtY,23721
