# Threading Building Blocks 2020
[![Stable release](https://img.shields.io/badge/version-2020.3-green.svg)](https://github.com/intel/tbb/releases/tag/v2020.3)
[![Apache License Version 2.0](https://img.shields.io/badge/license-Apache_2.0-green.svg)](LICENSE)

Threading Building Blocks (TBB) lets you easily write parallel C++ programs that take
full advantage of multicore performance, that are portable, composable and have future-proof scalability.

## Release Information
Here are the latest [Changes](CHANGES) and [Release Notes](doc/Release_Notes.txt) (contains system requirements and known issues).

Since [2018 U5](https://github.com/intel/tbb/releases/tag/2018_U5) TBB binary packages include [Parallel STL](https://github.com/intel/parallelstl) as a high-level component.

## Documentation
* TBB [tutorial](https://software.intel.com/en-us/tbb-tutorial)
* TBB general documentation: [stable](https://software.intel.com/en-us/tbb-documentation). For latest documentation please refer to the [latest](https://github.com/intel/tbb/releases/latest) release assets.

## Support
Please report issues and suggestions via
[GitHub issues](https://github.com/intel/tbb/issues) or start a topic on the
[TBB forum](http://software.intel.com/en-us/forums/intel-threading-building-blocks/).

## How to Contribute
To contribute to TBB, please open a GitHub pull request (preferred) or send us a patch by e-mail.
Threading Building Blocks is licensed under [Apache License, Version 2.0](LICENSE).
By its terms, contributions submitted to the project are also done under that license.

## Engineering team contacts
* [E-mail us.](mailto:<EMAIL>)

------------------------------------------------------------------------
Intel and the Intel logo are trademarks of Intel Corporation or its subsidiaries in the U.S. and/or other countries.

\* Other names and brands may be claimed as the property of others.
