#!/usr/bin/env python3
"""
Quick AI Training - تدريب سريع للذكاء الاصطناعي
تدريب سريع ومبسط للنماذج بدون واجهة تفاعلية
"""

import sys
import os
import json
import logging
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.strategy.ai_analysis import AIAnalysisLayer

# إعداد التسجيل
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_training_data():
    """تحميل البيانات التاريخية للتدريب"""
    try:
        historical_dir = "data/historical"
        all_candles = []
        all_outcomes = []
        
        if not os.path.exists(historical_dir):
            logger.error("❌ Historical data directory not found")
            return {'candles': [], 'outcomes': []}
        
        files_processed = 0
        max_files = 5  # تحديد عدد الملفات لتسريع التدريب
        
        for filename in os.listdir(historical_dir):
            if filename.endswith('.json') and files_processed < max_files:
                filepath = os.path.join(historical_dir, filename)
                
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    candles = data.get('candles', [])
                    
                    if len(candles) >= 50:
                        # تقسيم البيانات إلى عينات تدريب (عينات أقل للتسريع)
                        lookback_period = 20
                        prediction_horizon = 1
                        step = 5  # أخذ كل 5 عينات بدلاً من كل عينة
                        
                        for i in range(lookback_period, len(candles) - prediction_horizon, step):
                            sample_candles = candles[i-lookback_period:i]
                            
                            current_close = float(candles[i]['close'])
                            future_close = float(candles[i + prediction_horizon]['close'])
                            
                            # تبسيط التصنيف لتجنب عدم التوازن
                            if future_close > current_close:  # أي ارتفاع
                                outcome = 'CALL'
                            else:  # أي انخفاض أو ثبات
                                outcome = 'PUT'
                            
                            all_candles.append(sample_candles)
                            all_outcomes.append(outcome)
                        
                        files_processed += 1
                        logger.info(f"📊 Processed {filename} - Total samples: {len(all_candles)}")
                
                except Exception as e:
                    logger.warning(f"⚠️ Error loading {filename}: {e}")
                    continue
        
        logger.info(f"📊 Loaded {len(all_candles)} training samples from {files_processed} files")
        
        return {
            'candles': all_candles,
            'outcomes': all_outcomes
        }
        
    except Exception as e:
        logger.error(f"❌ Error loading training data: {e}")
        return {'candles': [], 'outcomes': []}

def quick_train():
    """تدريب سريع للنماذج"""
    try:
        print("🤖 Quick AI Training Started")
        print("="*40)
        
        # تهيئة طبقة الذكاء الاصطناعي
        print("📚 Initializing AI layer...")
        ai_layer = AIAnalysisLayer()
        
        # تحميل البيانات
        print("📊 Loading training data...")
        training_data = load_training_data()
        
        if not training_data or len(training_data['candles']) < 50:
            print("❌ Insufficient training data")
            return False
        
        print(f"🔧 Preparing {len(training_data['candles'])} training samples...")
        
        # تحضير البيانات
        X, y = ai_layer.prepare_training_data(
            training_data['candles'],
            training_data['outcomes']
        )
        
        if len(X) < 50:
            print("❌ Not enough prepared training data")
            return False
        
        print(f"🎯 Training models with {len(X)} samples...")
        
        # تدريب النماذج
        training_results = ai_layer.train_models(X, y)
        
        if 'error' in training_results:
            print(f"❌ Training failed: {training_results['error']}")
            return False
        
        # عرض النتائج
        print("\n✅ Training completed successfully!")
        print("="*40)
        print("📈 Training Results:")
        
        for model_name, model_results in training_results.get('models', {}).items():
            if isinstance(model_results, dict) and 'accuracy' in model_results:
                accuracy = model_results['accuracy']
                print(f"  {model_name}: {accuracy:.3f} accuracy")
        
        # حفظ تقرير مبسط
        save_quick_report(training_results)
        
        print("\n🎉 Quick training completed!")
        print("Models are now ready for use in the trading system.")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Quick training failed: {e}")
        print(f"❌ Training failed: {e}")
        return False

def save_quick_report(training_results):
    """حفظ تقرير تدريب مبسط"""
    try:
        report = {
            'training_type': 'quick_training',
            'timestamp': datetime.now().isoformat(),
            'results': training_results,
            'status': 'completed'
        }
        
        # إنشاء مجلد التقارير
        reports_dir = "data/ai_training_reports"
        os.makedirs(reports_dir, exist_ok=True)
        
        # حفظ التقرير
        report_file = os.path.join(reports_dir, "quick_training_latest.json")
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"📄 Training report saved: {report_file}")
        
    except Exception as e:
        logger.error(f"❌ Error saving report: {e}")

def check_models_status():
    """فحص حالة النماذج المدربة"""
    try:
        print("🔍 Checking AI models status...")
        
        ai_layer = AIAnalysisLayer()
        performance = ai_layer.get_model_performance()
        
        print("\n📊 Models Status:")
        print("="*30)
        
        models_loaded = 0
        for model_name, model_info in performance.items():
            if model_name.startswith('scaler') or model_name.startswith('encoder') or model_name.startswith('feature'):
                continue
                
            if isinstance(model_info, dict):
                status = "✅ Loaded" if model_info.get('loaded', False) else "❌ Not loaded"
                model_type = model_info.get('type', 'Unknown')
                print(f"  {model_name}: {status} ({model_type})")
                
                if model_info.get('loaded', False):
                    models_loaded += 1
        
        print(f"\nScaler fitted: {'✅ Yes' if performance.get('scaler_fitted', False) else '❌ No'}")
        print(f"Encoder fitted: {'✅ Yes' if performance.get('encoder_fitted', False) else '❌ No'}")
        print(f"Feature count: {performance.get('feature_count', 0)}")
        
        if models_loaded > 0:
            print(f"\n🎉 {models_loaded} models are ready for use!")
        else:
            print("\n⚠️ No trained models found. Run quick training first.")
        
        return models_loaded > 0
        
    except Exception as e:
        logger.error(f"❌ Error checking models: {e}")
        print(f"❌ Error checking models: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == 'train':
            quick_train()
        elif command == 'status':
            check_models_status()
        elif command == 'help':
            print("🤖 Quick AI Training Commands:")
            print("  python quick_train.py train   - Start quick training")
            print("  python quick_train.py status  - Check models status")
            print("  python quick_train.py help    - Show this help")
        else:
            print("❌ Unknown command. Use 'help' for available commands.")
    else:
        # تشغيل تفاعلي
        print("🤖 Quick AI Training")
        print("="*30)
        print("1. Start Quick Training")
        print("2. Check Models Status")
        print("3. Exit")
        
        choice = input("\nSelect option (1-3): ").strip()
        
        if choice == '1':
            quick_train()
        elif choice == '2':
            check_models_status()
        elif choice == '3':
            print("👋 Goodbye!")
        else:
            print("❌ Invalid choice")

if __name__ == "__main__":
    main()
