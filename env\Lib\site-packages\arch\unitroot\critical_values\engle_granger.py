import numpy as np

eg_num_variables = np.arange(1, 13)
CV_PARAMETERS = {
    "c": {
        1: [
            [-3.43038, -9.37029, -15.17885, -55.36303],
            [-3.89644, -12.94215, -27.56134, 0.0],
            [-4.29403, -16.53718, -42.4585, 0.0],
            [-4.64344, -20.40961, -49.57506, 0.0],
            [-4.95788, -24.27845, -61.74077, 0.0],
            [-5.24571, -28.25033, -74.85132, 75.74062],
            [-5.51236, -32.30341, -87.15595, 120.77804],
            [-5.76201, -36.4151, -99.75951, 185.56029],
            [-5.99756, -40.56155, -113.32703, 246.83401],
            [-6.2211, -44.73506, -131.38185, 404.54031],
            [-6.43401, -49.05894, -143.7552, 487.79554],
            [-6.63798, -53.46432, -154.21481, 535.98321],
        ],
        5: [
            [-2.86164, -5.29482, -5.679, 0.0],
            [-3.3362, -7.78669, -10.49974, 0.0],
            [-3.74081, -10.41304, -16.6018, 0.0],
            [-4.09623, -13.24811, -20.16306, 0.0],
            [-4.41535, -16.18989, -24.44189, 30.22455],
            [-4.70696, -19.24077, -29.25902, 57.95138],
            [-4.97687, -22.39648, -33.05104, 80.22428],
            [-5.22928, -25.613, -38.25853, 124.99776],
            [-5.46707, -28.91109, -43.11914, 170.84202],
            [-5.69246, -32.3123, -46.38161, 200.93533],
            [-5.90723, -35.79007, -48.78788, 221.50181],
            [-6.11285, -39.32149, -52.69727, 282.3438],
        ],
        10: [
            [-2.56685, -3.76863, -2.60203, 0.0],
            [-3.04456, -5.76527, -5.56556, 0.0],
            [-3.45226, -7.91706, -9.14763, 0.0],
            [-3.81034, -10.28325, -10.46512, 0.0],
            [-4.1317, -12.77725, -12.04923, 0.0],
            [-4.42509, -15.38089, -13.87516, 21.16278],
            [-4.69646, -18.08966, -15.73625, 43.49091],
            [-4.95014, -20.87876, -17.84307, 76.76028],
            [-5.18891, -23.77904, -18.37588, 91.94413],
            [-5.41532, -26.73635, -19.4419, 118.99549],
            [-5.63098, -29.77716, -20.12138, 147.55439],
            [-5.83735, -32.87974, -21.35561, 194.13587],
        ],
    },
    "ct": {
        1: [
            [-3.95892, -14.14321, -38.37382, 0.0],
            [-4.32765, -17.56375, -47.80739, 0.0],
            [-4.66289, -21.12925, -60.38725, 77.01145],
            [-4.96963, -24.89032, -70.03763, 83.38864],
            [-5.25308, -28.74575, -82.0537, 132.65665],
            [-5.51734, -32.71514, -93.1697, 167.71372],
            [-5.76547, -36.70768, -108.62387, 266.69235],
            [-6.00008, -40.7719, -125.5897, 402.12151],
            [-6.22298, -44.97666, -138.23791, 481.74015],
            [-6.43556, -49.27447, -150.02086, 553.78841],
            [-6.63914, -53.65285, -161.91178, 642.18853],
            [-6.83492, -58.07445, -173.51189, 722.37206],
        ],
        5: [
            [-3.4106, -8.96264, -15.87449, 0.0],
            [-3.78084, -11.33209, -22.45027, 49.34984],
            [-4.11901, -13.93139, -27.36042, 71.75476],
            [-4.4289, -16.77492, -29.17918, 59.64966],
            [-4.71534, -19.71211, -33.91425, 90.3122],
            [-4.98237, -22.79087, -37.12669, 106.06059],
            [-5.23307, -25.93217, -43.28449, 172.14431],
            [-5.46978, -29.19732, -47.38432, 209.35523],
            [-5.69451, -32.54985, -51.34099, 255.61262],
            [-5.90897, -35.95662, -56.27905, 314.7515],
            [-6.1141, -39.50986, -57.48074, 336.49505],
            [-6.31139, -43.07356, -62.05329, 416.82416],
        ],
        10: [
            [-3.12715, -6.90919, -9.50997, 0.0],
            [-3.49644, -8.80957, -13.74487, 34.21356],
            [-3.83524, -10.96107, -15.98548, 45.2846],
            [-4.14644, -13.32465, -16.91505, 46.40623],
            [-4.43422, -15.8261, -18.36899, 58.11805],
            [-4.70235, -18.47198, -19.62845, 78.54991],
            [-4.95413, -21.21311, -20.66046, 95.36797],
            [-5.19184, -24.05108, -22.24331, 129.41057],
            [-5.41746, -26.97338, -24.00747, 174.31652],
            [-5.6327, -29.98025, -24.64645, 202.88926],
            [-5.83863, -33.09702, -23.78655, 221.31642],
            [-6.03648, -36.26928, -24.0077, 265.80684],
        ],
    },
    "ctt": {
        1: [
            [-4.37161, -19.16599, -67.82944, 0.0],
            [-4.69284, -22.59252, -76.04704, 0.0],
            [-4.99068, -26.08824, -91.08781, 156.74915],
            [-5.26802, -29.86676, -97.52386, 139.85738],
            [-5.52813, -33.72189, -108.37531, 189.9253],
            [-5.77365, -37.64225, -120.93471, 258.34534],
            [-6.0062, -41.67184, -133.87624, 339.22678],
            [-6.22758, -45.77769, -149.54301, 491.1006],
            [-6.43941, -49.91596, -166.53075, 653.55711],
            [-6.6423, -54.24757, -177.31377, 726.9804],
            [-6.83748, -58.61801, -190.8062, 861.879],
            [-7.02575, -63.09091, -201.6938, 961.76388],
        ],
        5: [
            [-3.83252, -12.93154, -34.0435, 45.45368],
            [-4.15396, -15.3696, -37.55669, 64.77665],
            [-4.45329, -17.9702, -42.93607, 110.00513],
            [-4.73259, -20.83066, -44.1478, 110.90471],
            [-4.99482, -23.77935, -47.91311, 143.74771],
            [-5.24218, -26.86622, -50.80537, 170.02064],
            [-5.47663, -30.04423, -55.2044, 224.20106],
            [-5.6999, -33.2972, -60.64627, 298.193],
            [-5.91315, -36.64578, -65.34129, 362.54989],
            [-6.11761, -40.09936, -69.02728, 424.31879],
            [-6.31408, -43.66931, -71.29175, 481.10138],
            [-6.50372, -47.27807, -74.1638, 541.57773],
        ],
        10: [
            [-3.55343, -10.40535, -23.21209, 44.76751],
            [-3.87361, -12.38238, -25.01889, 64.62563],
            [-4.17294, -14.55458, -26.33092, 73.14798],
            [-4.4529, -16.93022, -27.2264, 88.58307],
            [-4.71581, -19.45893, -27.89651, 106.40677],
            [-4.96397, -22.11665, -28.36549, 126.96588],
            [-5.19924, -24.86932, -29.85449, 165.60894],
            [-5.42321, -27.7259, -30.92368, 204.76268],
            [-5.63708, -30.68684, -31.27885, 239.55721],
            [-5.84223, -33.7239, -31.79413, 282.16668],
            [-6.03936, -36.88111, -30.06602, 302.33381],
            [-6.22947, -40.09655, -29.21196, 341.51162],
        ],
    },
    "n": {
        1: [
            [-2.5653, -4.0135, -34.26928, 184.36092],
            [-3.34191, -10.52922, -1.37381, -125.05815],
            [-3.86069, -14.01303, 2.84532, -339.77446],
            [-4.27608, -17.46322, -11.01744, -314.59064],
            [-4.63285, -21.03999, -28.37688, -239.37192],
            [-4.95079, -24.82053, -43.27455, -174.54679],
            [-5.24046, -28.74016, -54.79338, -168.46039],
            [-5.50851, -32.64322, -73.44286, 0.0],
            [-5.75893, -36.69779, -87.74334, 0.0],
            [-5.99498, -40.80075, -103.93717, 133.75414],
            [-6.21889, -44.94308, -124.53583, 342.54237],
            [-6.43249, -49.18547, -139.15816, 437.73654],
        ],
        5: [
            [-1.9403, -1.73812, -11.6653, 85.94351],
            [-2.75983, -6.93331, 21.37212, -195.03504],
            [-3.29588, -9.10451, 25.78282, -358.91957],
            [-3.72146, -11.38946, 15.02512, -316.62263],
            [-4.08506, -13.95957, 4.4429, -249.54889],
            [-4.40801, -16.74325, -5.59385, -168.63254],
            [-4.7016, -19.66979, -14.72458, -91.00347],
            [-4.97293, -22.72067, -21.56794, -42.88523],
            [-5.22608, -25.8954, -27.36652, 0.0],
            [-5.46443, -29.16415, -33.18049, 55.87559],
            [-5.69043, -32.48145, -39.99023, 127.29874],
            [-5.90557, -35.93025, -43.70895, 165.68976],
        ],
        10: [
            [-1.61612, -1.03595, 5.1379, -72.67136],
            [-2.45704, -5.64593, 30.26726, -240.92321],
            [-3.00169, -7.18635, 33.61599, -387.5693],
            [-3.43207, -8.91987, 22.54233, -319.53876],
            [-3.79886, -11.01597, 14.60067, -266.15764],
            [-4.12424, -13.33014, 7.23846, -201.01912],
            [-4.41975, -15.79067, -0.59539, -110.94082],
            [-4.69253, -18.39802, -5.41631, -61.16677],
            [-4.94697, -21.13716, -8.70593, -22.97557],
            [-5.18636, -24.00601, -9.92534, 0.0],
            [-5.41326, -26.90983, -13.13732, 49.06113],
            [-5.62934, -29.91024, -15.43352, 96.79571],
        ],
    },
}

LARGE_PARAMETERS = {
    ("n", 1): [
        1.5629186427019417,
        0.8850515361709982,
        -0.18676188300818614,
        -0.030738361334589192,
    ],
    ("n", 2): [
        2.2776665569327728,
        0.8383344897725661,
        -0.20647986009940758,
        -0.029862943318712875,
    ],
    ("n", 3): [
        2.9035788810943686,
        0.8997245022025984,
        -0.16999814167405708,
        -0.022232910777186025,
    ],
    ("n", 4): [
        3.492542101909836,
        0.9964395802281024,
        -0.12927180875051136,
        -0.015954718844050447,
    ],
    ("n", 5): [
        4.028379990547961,
        1.0756326484481038,
        -0.10108270386705392,
        -0.012071352399732924,
    ],
    ("n", 6): [
        4.553724471341804,
        1.1666962156251457,
        -0.07382134775645965,
        -0.008892285757974107,
    ],
    ("n", 7): [
        5.044829686177284,
        1.243033645832885,
        -0.05363719953993057,
        -0.006727110428902422,
    ],
    ("n", 8): [
        5.505826205750541,
        1.3074644741878159,
        -0.03825983401097338,
        -0.005179704495789663,
    ],
    ("n", 9): [
        5.947658846949496,
        1.3655990540540515,
        -0.025922054047927134,
        -0.004036916115802373,
    ],
    ("n", 10): [
        6.365810122704081,
        1.414707758316115,
        -0.0164991004430991,
        -0.0032095464498029004,
    ],
    ("n", 11): [
        6.76978628144805,
        1.4611808152788373,
        -0.00814169628518413,
        -0.002518783185861167,
    ],
    ("c", 1): [
        2.2415107522831885,
        0.7532195664877437,
        -0.22584558118252654,
        -0.030398140555142606,
    ],
    ("c", 2): [
        2.7448213895111184,
        0.7267751029473388,
        -0.2211017640088145,
        -0.02699091635989253,
    ],
    ("c", 3): [
        3.2906838705144654,
        0.8099910267357031,
        -0.1811729575200362,
        -0.020621627664250835,
    ],
    ("c", 4): [
        3.844919041963357,
        0.9254569358068733,
        -0.13912110861317184,
        -0.01520420260892449,
    ],
    ("c", 5): [
        4.37083223165612,
        1.0274940981636618,
        -0.1073138275078118,
        -0.011533688653245808,
    ],
    ("c", 6): [
        4.881324348899165,
        1.1275331534689796,
        -0.07963347781798141,
        -0.0086482024039167,
    ],
    ("c", 7): [
        5.367261125208782,
        1.2157848239395868,
        -0.05771020389018633,
        -0.0065400284143956496,
    ],
    ("c", 8): [
        5.819630042726716,
        1.2859070195469038,
        -0.04189656443015649,
        -0.0050944659907936965,
    ],
    ("c", 9): [
        6.260157617978893,
        1.3525922616903523,
        -0.02826187320890361,
        -0.003946288301598513,
    ],
    ("c", 10): [
        6.6663899064014025,
        1.4026725517096348,
        -0.018872779372039794,
        -0.003170819846565892,
    ],
    ("c", 11): [
        7.061557593380379,
        1.45057393491353,
        -0.010458123302259037,
        -0.002515647827852484,
    ],
    ("ct", 1): [
        3.076530485961456,
        0.8906862638604103,
        -0.18751128078023688,
        -0.024369242793674306,
    ],
    ("ct", 2): [
        3.5217106612436435,
        0.9320578662785639,
        -0.15465620597473345,
        -0.018495213048447826,
    ],
    ("ct", 3): [
        3.9917697395816147,
        1.0001780661715254,
        -0.12366363902669056,
        -0.014044042746060036,
    ],
    ("ct", 4): [
        4.472025145154065,
        1.0797195454107098,
        -0.09637933935639897,
        -0.010706538652763827,
    ],
    ("ct", 5): [
        4.9443250693799214,
        1.1567735304423317,
        -0.07415680316565132,
        -0.00827846805923212,
    ],
    ("ct", 6): [
        5.39698415040175,
        1.2255017077802615,
        -0.05643733380799992,
        -0.006480210368540261,
    ],
    ("ct", 7): [
        5.839639728902505,
        1.2913377618010338,
        -0.041414954181468566,
        -0.005088021306319187,
    ],
    ("ct", 8): [
        6.273214052355109,
        1.3555989963354433,
        -0.02804729884044599,
        -0.003945671644803167,
    ],
    ("ct", 9): [
        6.684842176330493,
        1.4097547216357982,
        -0.017796537945342472,
        -0.003113439805615629,
    ],
    ("ct", 10): [
        7.078011464511224,
        1.4570648761876588,
        -0.009478407342279338,
        -0.002464922810932975,
    ],
    ("ct", 11): [
        7.461743622869993,
        1.5028043280725334,
        -0.0018635161300673553,
        -0.0019025820182149641,
    ],
    ("ctt", 1): [
        3.8678603928655786,
        1.0995224741097416,
        -0.12182669035020083,
        -0.016109112908368153,
    ],
    ("ctt", 2): [
        4.280363928369436,
        1.145332450168531,
        -0.09459908871121978,
        -0.011931733414173584,
    ],
    ("ctt", 3): [
        4.692289431466653,
        1.1874407901095956,
        -0.0756378901691655,
        -0.009275456661052551,
    ],
    ("ctt", 4): [
        5.112414179179322,
        1.2371728787001852,
        -0.05904091444666609,
        -0.007260518200820618,
    ],
    ("ctt", 5): [
        5.527590974146523,
        1.285636175866561,
        -0.04569644020201574,
        -0.005799875689593479,
    ],
    ("ctt", 6): [
        5.9351205354218175,
        1.3329886479214559,
        -0.03432803529955425,
        -0.004660562927564296,
    ],
    ("ctt", 7): [
        6.337863356651793,
        1.380455623611525,
        -0.024328744018323634,
        -0.0037500590627598668,
    ],
    ("ctt", 8): [
        6.740294600192687,
        1.4314481716687775,
        -0.0144841787245798,
        -0.0029343793377171745,
    ],
    ("ctt", 9): [
        7.134317891811719,
        1.4806385333102572,
        -0.00570271747358847,
        -0.0022535769567990777,
    ],
    ("ctt", 10): [
        7.509928089780577,
        1.5220552870642434,
        0.0010286713395766811,
        -0.0017520841739675566,
    ],
    ("ctt", 11): [
        7.86863308484888,
        1.5571343405604674,
        0.006249720945701254,
        -0.0013753882292552033,
    ],
}

SMALL_PARAMETERS = {
    ("n", 1): [1.882033203832549, 1.36416744470282, 0.03192985244757768],
    ("n", 2): [2.7396200578343297, 1.4356430324676221, 0.03246984988630636],
    ("n", 3): [3.4176600054383064, 1.4761132485170338, 0.031593435311584206],
    ("n", 4): [4.009509648010534, 1.5098688618511487, 0.031264267910453114],
    ("n", 5): [4.5343074145791284, 1.535013603812935, 0.030740611430709386],
    ("n", 6): [5.020410267949835, 1.5582158486649238, 0.030440000362321862],
    ("n", 7): [5.463175641830661, 1.5750671005590267, 0.029871575811748152],
    ("n", 8): [5.853635919697339, 1.5801748377054619, 0.028412779488395046],
    ("n", 9): [6.239650683738574, 1.5914090378871792, 0.027801919564405586],
    ("n", 10): [6.634625364932081, 1.6117399848487928, 0.0281783096077165],
    ("n", 11): [6.999508087466321, 1.6256239717801773, 0.028061815402067314],
    ("c", 1): [2.850470871251974, 1.460498404798193, 0.03431379842034804],
    ("c", 2): [3.4736110374567346, 1.4886635568498878, 0.032623418017089456],
    ("c", 3): [4.021279196626285, 1.5068547927593716, 0.030677483060070454],
    ("c", 4): [4.547010307749973, 1.5349794082550643, 0.030554100239537973],
    ("c", 5): [5.020162751800182, 1.5550532893225837, 0.03009483730232765],
    ("c", 6): [5.437029420515827, 1.5624397311063856, 0.028603014813650907],
    ("c", 7): [5.8597100039634515, 1.5809115366271227, 0.028479622563268858],
    ("c", 8): [6.244562845373295, 1.5918731684038958, 0.027836727322945287],
    ("c", 9): [6.6202576598797975, 1.6054588637826086, 0.027612490232146403],
    ("c", 10): [7.00199005072346, 1.6255829451039308, 0.028052804822416677],
    ("c", 11): [7.329227208910897, 1.6309113362190732, 0.027321294115807304],
    ("ct", 1): [3.669195808626637, 1.5458327198088662, 0.03760572087422209],
    ("ct", 2): [4.139580399497449, 1.5398159021422568, 0.0334091069113146],
    ("ct", 3): [4.618828134660072, 1.5539129898374249, 0.03207969122557608],
    ("ct", 4): [5.058200428841472, 1.5626716392585243, 0.030499245473942227],
    ("ct", 5): [5.468723280888936, 1.5702244317444638, 0.029191029139434055],
    ("ct", 6): [5.880471274028851, 1.5854054873169332, 0.028770560569912784],
    ("ct", 7): [6.273507126376021, 1.6003866247156902, 0.02854549455132649],
    ("ct", 8): [6.638196339592987, 1.6103211456296451, 0.02799650923880026],
    ("ct", 9): [6.9883162998304265, 1.6199548460241298, 0.027559398169645588],
    ("ct", 10): [7.33450097349429, 1.6320240884481034, 0.02740786298932174],
    ("ct", 11): [7.658277690355028, 1.6400034762803566, 0.02699813733220735],
    ("ctt", 1): [4.335091354636502, 1.5953694470284292, 0.03808043025002805],
    ("ctt", 2): [4.7400318323490005, 1.5834655874249197, 0.03420311288633926],
    ("ctt", 3): [5.147514876476784, 1.583756274417623, 0.03198829559104044],
    ("ctt", 4): [5.544071981909168, 1.5884572537456778, 0.0304762381293453],
    ("ctt", 5): [5.93421908338361, 1.5980386295347646, 0.029672529293512273],
    ("ctt", 6): [6.331644015153273, 1.6157010961978449, 0.029722263829775164],
    ("ctt", 7): [6.679521852845532, 1.6208422122347832, 0.028800537563486994],
    ("ctt", 8): [7.026842296732148, 1.6299608077874526, 0.028318176852922483],
    ("ctt", 9): [7.342335220299393, 1.6323427713884842, 0.027384181002187202],
    ("ctt", 10): [7.686890568759868, 1.6472442425044669, 0.027525324496932013],
    ("ctt", 11): [7.989070461970375, 1.6517902986782715, 0.026940425755991315],
}

TAU_MAX = {
    ("n", 1): 1.675993768969255,
    ("n", 2): 1.5253218925979282,
    ("n", 3): 1.9217667988568512,
    ("n", 4): 2.6013093136909613,
    ("n", 5): 3.331911914904795,
    ("n", 6): 4.40158036986345,
    ("n", 7): 5.62818565707319,
    ("n", 8): 7.035334895253224,
    ("n", 9): 8.691971663492259,
    ("n", 10): 10.52832248111843,
    ("n", 11): 12.870015356310352,
    ("c", 1): 1.3172407570233184,
    ("c", 2): 1.3230165805633478,
    ("c", 3): 1.726489273644988,
    ("c", 4): 2.389827227498003,
    ("c", 5): 3.168667549528631,
    ("c", 6): 4.20251564217107,
    ("c", 7): 5.462072303705667,
    ("c", 8): 6.832208646451984,
    ("c", 9): 8.564909068127827,
    ("c", 10): 10.320164960192631,
    ("c", 11): 12.547207035197298,
    ("ct", 1): 1.7666178167625948,
    ("ct", 2): 2.1692248471316113,
    ("ct", 3): 2.7529296700446753,
    ("ct", 4): 3.5277162632888963,
    ("ct", 5): 4.46345718396879,
    ("ct", 6): 5.550691443022152,
    ("ct", 7): 6.876423212729502,
    ("ct", 8): 8.591216178750125,
    ("ct", 9): 10.526971692797451,
    ("ct", 10): 12.813720141090254,
    ("ct", 11): 15.903072779002036,
    ("ctt", 1): 2.8741626871614043,
    ("ctt", 2): 3.600700788036445,
    ("ctt", 3): 4.357232983171264,
    ("ctt", 4): 5.2985536460907,
    ("ctt", 5): 6.3618209604601255,
    ("ctt", 6): 7.61286678153545,
    ("ctt", 7): 9.123830565485626,
    ("ctt", 8): 11.212096145050896,
    ("ctt", 9): 13.979354427279585,
    ("ctt", 10): 17.213598644779633,
    ("ctt", 11): 20.99990045324711,
}

TAU_STAR = {
    ("n", 1): -0.9620769942520792,
    ("n", 2): -1.947028172632933,
    ("n", 3): -3.036957015327215,
    ("n", 4): -3.408246354672163,
    ("n", 5): -3.7374301278658386,
    ("n", 6): -4.023476367757518,
    ("n", 7): -4.324827123359866,
    ("n", 8): -4.608126365566668,
    ("n", 9): -4.8632698726383445,
    ("n", 10): -5.078938889257519,
    ("n", 11): -5.284022564939372,
    ("c", 1): -1.6523010711501516,
    ("c", 2): -1.8756945677722825,
    ("c", 3): -2.836457216140953,
    ("c", 4): -3.173988104546879,
    ("c", 5): -4.041558790735912,
    ("c", 6): -4.355100783800486,
    ("c", 7): -4.598038866393395,
    ("c", 8): -4.8525049740646065,
    ("c", 9): -5.081196263981044,
    ("c", 10): -5.272968755752615,
    ("c", 11): -5.494375568495901,
    ("ct", 1): -3.135552172779153,
    ("ct", 2): -3.4489835215053026,
    ("ct", 3): -3.7494153916592596,
    ("ct", 4): -4.05215201836797,
    ("ct", 5): -4.348555362128727,
    ("ct", 6): -4.589649528686736,
    ("ct", 7): -4.842576583933942,
    ("ct", 8): -5.0835919574027795,
    ("ct", 9): -5.31423267973552,
    ("ct", 10): -5.522261886403539,
    ("ct", 11): -5.722186812730368,
    ("ctt", 1): -3.5065217921868133,
    ("ctt", 2): -3.8055661047283302,
    ("ctt", 3): -4.0865860657610265,
    ("ctt", 4): -4.363961352465816,
    ("ctt", 5): -4.613962362855738,
    ("ctt", 6): -4.82547386474664,
    ("ctt", 7): -5.090093293079796,
    ("ctt", 8): -5.3193381474597405,
    ("ctt", 9): -5.5675492223780205,
    ("ctt", 10): -5.739040227632518,
    ("ctt", 11): -5.958707525343723,
}

TAU_MIN = {
    ("n", 1): -21.36194407635465,
    ("n", 2): -22.107324756574897,
    ("n", 3): -23.361075393656144,
    ("n", 4): -24.14687697430984,
    ("n", 5): -24.967193760490407,
    ("n", 6): -25.594872373813406,
    ("n", 7): -26.36397742263685,
    ("n", 8): -27.807466677993798,
    ("n", 9): -28.620488491820513,
    ("n", 10): -28.598947333721984,
    ("n", 11): -28.96505355210229,
    ("c", 1): -21.281502952644836,
    ("c", 2): -22.81587349415788,
    ("c", 3): -24.55962227749839,
    ("c", 4): -25.119041245219723,
    ("c", 5): -25.835881312478637,
    ("c", 6): -27.312500820030774,
    ("c", 7): -27.755134976157976,
    ("c", 8): -28.59303735557565,
    ("c", 9): -29.07124367061861,
    ("c", 10): -28.973625906471675,
    ("c", 11): -29.846890291984295,
    ("ct", 1): -20.553158985824695,
    ("ct", 2): -23.044852803604428,
    ("ct", 3): -24.219575227683947,
    ("ct", 4): -25.618201614096172,
    ("ct", 5): -26.895667573830984,
    ("ct", 6): -27.552565120592284,
    ("ct", 7): -28.032210509404564,
    ("ct", 8): -28.75932016906427,
    ("ct", 9): -29.390243503364616,
    ("ct", 10): -29.77291752158768,
    ("ct", 11): -30.37253007680495,
    ("ctt", 1): -20.947366357911015,
    ("ctt", 2): -23.147974757253113,
    ("ctt", 3): -24.755246335494274,
    ("ctt", 4): -26.06058607043378,
    ("ctt", 5): -26.927913925493478,
    ("ctt", 6): -27.179980391992686,
    ("ctt", 7): -28.13909651272741,
    ("ctt", 8): -28.779409356984043,
    ("ctt", 9): -29.80448404241316,
    ("ctt", 10): -29.922340110614673,
    ("ctt", 11): -30.656351047290478,
}
