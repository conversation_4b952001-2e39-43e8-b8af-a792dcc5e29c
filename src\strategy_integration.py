"""
Strategy Integration Module - وحدة تكامل الاستراتيجيات
======================================================
دمج الاستراتيجية الجديدة V2 مع النظام الأساسي
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

# استيراد الاستراتيجية الجديدة
try:
    from .strategy_v2.decision_maker import StrategyDecisionMaker
    from .risk_management.advanced_risk_manager import AdvancedRiskManager
    STRATEGY_V2_AVAILABLE = True
    print("✅ Strategy V2 loaded successfully")
except Exception as e:
    STRATEGY_V2_AVAILABLE = False
    StrategyDecisionMaker = None
    AdvancedRiskManager = None
    print(f"⚠️ Strategy V2 not available: {e}")

# استيراد الاستراتيجية القديمة كبديل
try:
    from .strategy.strategy import Strategy as StrategyV1
    STRATEGY_V1_AVAILABLE = True
except Exception as e:
    STRATEGY_V1_AVAILABLE = False
    StrategyV1 = None
    print(f"⚠️ Strategy V1 not available: {e}")

logger = logging.getLogger(__name__)

class IntegratedStrategyManager:
    """مدير الاستراتيجيات المتكامل"""
    
    def __init__(self, use_v2: bool = True, initial_balance: float = 1000.0):
        """
        تهيئة مدير الاستراتيجيات
        
        Args:
            use_v2: استخدام الاستراتيجية V2 إذا كانت متاحة
            initial_balance: الرصيد الأولي
        """
        self.use_v2 = use_v2 and STRATEGY_V2_AVAILABLE
        self.initial_balance = initial_balance
        
        # تهيئة الاستراتيجية المناسبة
        if self.use_v2:
            self.strategy = StrategyDecisionMaker()
            self.risk_manager = AdvancedRiskManager()
            self.risk_manager.set_initial_balance(initial_balance)
            self.strategy_version = "V2"
            logger.info("✅ Using Strategy V2 (Advanced)")
        elif STRATEGY_V1_AVAILABLE:
            self.strategy = StrategyV1()
            self.risk_manager = None
            self.strategy_version = "V1"
            logger.info("✅ Using Strategy V1 (Legacy)")
        else:
            raise Exception("No strategy available!")
        
        # إحصائيات
        self.stats = {
            'total_analyses': 0,
            'successful_signals': 0,
            'failed_analyses': 0,
            'strategy_version': self.strategy_version
        }
    
    async def analyze_pair(self, pair_name: str, account_balance: float = None) -> Dict[str, Any]:
        """
        تحليل زوج العملات باستخدام الاستراتيجية المناسبة
        
        Args:
            pair_name: اسم زوج العملات
            account_balance: رصيد الحساب الحالي
            
        Returns:
            نتيجة التحليل
        """
        try:
            self.stats['total_analyses'] += 1
            
            if self.use_v2:
                # استخدام الاستراتيجية V2
                result = await self._analyze_with_v2(pair_name, account_balance)
            else:
                # استخدام الاستراتيجية V1
                result = await self._analyze_with_v1(pair_name)
            
            # تحديث الإحصائيات
            if result.get('signal') != 'NEUTRAL':
                self.stats['successful_signals'] += 1
            
            # إضافة معلومات الاستراتيجية
            result['strategy_version'] = self.strategy_version
            result['analysis_timestamp'] = datetime.now().isoformat()
            
            return result
            
        except Exception as e:
            logger.error(f"Error in strategy analysis for {pair_name}: {e}")
            self.stats['failed_analyses'] += 1
            return {
                'signal': 'NEUTRAL',
                'confidence': 0,
                'error': str(e),
                'strategy_version': self.strategy_version,
                'pair': pair_name
            }
    
    async def _analyze_with_v2(self, pair_name: str, account_balance: float = None) -> Dict[str, Any]:
        """تحليل باستخدام الاستراتيجية V2"""
        try:
            # تحديث رصيد إدارة المخاطر
            if account_balance and self.risk_manager:
                self.risk_manager.risk_stats['current_balance'] = account_balance
            
            # تحليل السوق
            result = self.strategy.analyze_market(pair_name)
            
            # إضافة تقييم المخاطر إذا كانت الإشارة إيجابية
            if result.get('signal') != 'NEUTRAL' and self.risk_manager:
                risk_assessment = await self._assess_risk_v2(result, account_balance or self.initial_balance)
                result['risk_assessment'] = risk_assessment
                
                # تعديل الثقة بناءً على تقييم المخاطر
                if risk_assessment.get('status') != 'APPROVED':
                    result['signal'] = 'NEUTRAL'
                    result['confidence'] = 0
                    result['rejection_reason'] = risk_assessment.get('reason', 'Risk management rejection')
            
            return result
            
        except Exception as e:
            logger.error(f"Error in V2 analysis: {e}")
            return {'signal': 'NEUTRAL', 'confidence': 0, 'error': str(e)}
    
    async def _analyze_with_v1(self, pair_name: str) -> Dict[str, Any]:
        """تحليل باستخدام الاستراتيجية V1"""
        try:
            # تحليل باستخدام الاستراتيجية القديمة
            result = await self.strategy.analyze(pair_name)
            
            # تحويل النتيجة إلى تنسيق موحد
            if isinstance(result, dict):
                signal = result.get('signal', 'NEUTRAL')
                confidence = result.get('confidence', 0)
                expiry = result.get('expiry', 2)
            else:
                # إذا كانت النتيجة نص بسيط
                signal = str(result) if result in ['CALL', 'PUT'] else 'NEUTRAL'
                confidence = 70 if signal != 'NEUTRAL' else 0
                expiry = 2
            
            return {
                'signal': signal,
                'confidence': confidence,
                'expiry_minutes': expiry,
                'layer_agreement': {
                    'supporting_layers': ['legacy'],
                    'agreement_ratio': 1.0 if signal != 'NEUTRAL' else 0,
                    'total_active_layers': 1
                }
            }
            
        except Exception as e:
            logger.error(f"Error in V1 analysis: {e}")
            return {'signal': 'NEUTRAL', 'confidence': 0, 'error': str(e)}
    
    async def _assess_risk_v2(self, analysis_result: Dict[str, Any], account_balance: float) -> Dict[str, Any]:
        """تقييم المخاطر للاستراتيجية V2"""
        try:
            if not self.risk_manager:
                return {'status': 'APPROVED', 'reason': 'No risk manager available'}
            
            confidence = analysis_result.get('confidence', 0)
            signal_quality = 'HIGH' if confidence >= 90 else 'STANDARD' if confidence >= 80 else 'LOW'
            
            # حساب حجم المركز
            position_calc = self.risk_manager.calculate_position_size(
                confidence, account_balance, signal_quality
            )
            
            if position_calc['status'] != 'APPROVED':
                return position_calc
            
            # التحقق من صحة الصفقة
            trade_validation = self.risk_manager.validate_trade(
                position_calc['position_size'], confidence, account_balance
            )
            
            return {
                'status': trade_validation['status'],
                'position_size': position_calc.get('position_size', 0),
                'risk_percentage': position_calc.get('risk_percentage', 0),
                'validation_details': trade_validation
            }
            
        except Exception as e:
            logger.error(f"Error in risk assessment: {e}")
            return {'status': 'ERROR', 'error': str(e)}
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """الحصول على معلومات الاستراتيجية"""
        info = {
            'strategy_version': self.strategy_version,
            'v2_available': STRATEGY_V2_AVAILABLE,
            'v1_available': STRATEGY_V1_AVAILABLE,
            'using_v2': self.use_v2,
            'stats': self.stats.copy()
        }
        
        if self.use_v2 and self.risk_manager:
            info['risk_manager'] = self.risk_manager.get_risk_summary()
        
        return info
    
    def switch_strategy(self, use_v2: bool = True):
        """تبديل الاستراتيجية"""
        if use_v2 and STRATEGY_V2_AVAILABLE:
            if not self.use_v2:
                self.strategy = StrategyDecisionMaker()
                self.risk_manager = AdvancedRiskManager()
                self.risk_manager.set_initial_balance(self.initial_balance)
                self.use_v2 = True
                self.strategy_version = "V2"
                logger.info("✅ Switched to Strategy V2")
        elif not use_v2 and STRATEGY_V1_AVAILABLE:
            if self.use_v2:
                self.strategy = StrategyV1()
                self.risk_manager = None
                self.use_v2 = False
                self.strategy_version = "V1"
                logger.info("✅ Switched to Strategy V1")
        else:
            logger.warning("Cannot switch strategy - target version not available")
    
    def record_trade_result(self, trade_amount: float, outcome: str, profit_loss: float, confidence: float):
        """تسجيل نتيجة الصفقة"""
        try:
            if self.use_v2 and self.risk_manager:
                self.risk_manager.record_trade_result(trade_amount, outcome, profit_loss, confidence)
        except Exception as e:
            logger.error(f"Error recording trade result: {e}")

# إنشاء مثيل عام للاستخدام
strategy_manager = None

def get_strategy_manager(use_v2: bool = True, initial_balance: float = 1000.0) -> IntegratedStrategyManager:
    """الحصول على مدير الاستراتيجيات"""
    global strategy_manager
    if strategy_manager is None:
        strategy_manager = IntegratedStrategyManager(use_v2, initial_balance)
    return strategy_manager

def reset_strategy_manager():
    """إعادة تعيين مدير الاستراتيجيات"""
    global strategy_manager
    strategy_manager = None
