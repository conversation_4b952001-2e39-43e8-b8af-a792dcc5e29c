"""
Technical Analysis Layer - الطبقة الأولى
========================================
تحليل فني متقدم باستخدام المؤشرات التقليدية والأنماط
"""

import json
import os
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import logging

# محاولة استيراد مكتبة ta، إذا لم تكن متاحة نستخدم حسابات يدوية
try:
    import ta
    TA_AVAILABLE = True
except ImportError:
    TA_AVAILABLE = False
    print("⚠️ Warning: 'ta' library not found. Using manual calculations.")

logger = logging.getLogger(__name__)

class TechnicalAnalysisLayer:
    """الطبقة الأولى: التحليل الفني المتقدم"""
    
    def __init__(self):
        """تهيئة طبقة التحليل الفني"""
        self.name = "Technical Analysis Layer"
        self.weight = 0.25  # وزن هذه الطبقة في القرار النهائي
        
        # إعدادات المؤشرات
        self.sma_periods = [10, 20]
        self.ema_periods = [5, 10, 21]
        self.rsi_periods = [14]
        self.bb_period = 20
        self.bb_std = 2
        
        logger.info(f"✅ {self.name} initialized")
    
    def analyze_pair(self, pair_name: str) -> Dict[str, Any]:
        """
        تحليل شامل للزوج
        
        Args:
            pair_name: اسم زوج العملات
            
        Returns:
            نتيجة التحليل الفني مع الإشارة والثقة وزمن الصفقة
        """
        try:
            # قراءة البيانات التاريخية
            historical_data = self._load_historical_data(pair_name)
            if not historical_data:
                return self._create_error_result("No historical data available")
            
            # قراءة بيانات المؤشرات
            indicators_data = self._load_indicators_data(pair_name)
            if not indicators_data:
                return self._create_error_result("No indicators data available")
            
            # تحليل الشموع
            candle_analysis = self._analyze_candles(historical_data)
            
            # تحليل المؤشرات
            indicators_analysis = self._analyze_indicators(indicators_data)
            
            # تحليل الأنماط
            patterns_analysis = self._analyze_patterns(historical_data)
            
            # تحليل الاتجاه
            trend_analysis = self._analyze_trend(historical_data, indicators_data)
            
            # دمج جميع التحليلات
            final_signal = self._combine_analyses(
                candle_analysis,
                indicators_analysis, 
                patterns_analysis,
                trend_analysis
            )
            
            return final_signal
            
        except Exception as e:
            logger.error(f"Error in technical analysis for {pair_name}: {e}")
            return self._create_error_result(str(e))
    
    def _load_historical_data(self, pair_name: str) -> List[Dict[str, Any]]:
        """قراءة البيانات التاريخية"""
        try:
            file_path = f"data/historical/{pair_name}.json"
            if not os.path.exists(file_path):
                return []
            
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            candles = data.get('candles', [])
            live_candle = data.get('live_candle')
            
            # إضافة الشمعة الحية إذا كانت متاحة
            if live_candle:
                candles.append(live_candle)
            
            return candles[-100:]  # آخر 100 شمعة
            
        except Exception as e:
            logger.error(f"Error loading historical data for {pair_name}: {e}")
            return []
    
    def _load_indicators_data(self, pair_name: str) -> Dict[str, Any]:
        """قراءة بيانات المؤشرات"""
        try:
            file_path = f"data/indicators/{pair_name}_indicators.json"
            if not os.path.exists(file_path):
                return {}
            
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            return data.get('indicators', {})
            
        except Exception as e:
            logger.error(f"Error loading indicators data for {pair_name}: {e}")
            return {}
    
    def _analyze_candles(self, candles: List[Dict[str, Any]]) -> Dict[str, Any]:
        """تحليل الشموع"""
        try:
            if len(candles) < 3:
                return {"signal": "NEUTRAL", "confidence": 0, "reason": "Insufficient candles"}
            
            last_candle = candles[-1]
            prev_candle = candles[-2]
            
            # تحليل نوع الشمعة
            candle_type = self._get_candle_type(last_candle)
            
            # تحليل الاتجاه قصير المدى
            short_trend = self._get_short_trend(candles[-5:])
            
            # تحليل أنماط الانعكاس
            reversal_pattern = self._detect_reversal_patterns(candles[-3:])
            
            # حساب القوة
            strength = self._calculate_candle_strength(last_candle, prev_candle)
            
            # تحديد الإشارة
            signal = self._determine_candle_signal(candle_type, short_trend, reversal_pattern, strength)
            
            return {
                "signal": signal["direction"],
                "confidence": signal["confidence"],
                "strength": strength,
                "candle_type": candle_type,
                "trend": short_trend,
                "pattern": reversal_pattern,
                "expiry_minutes": signal["expiry_minutes"]
            }
            
        except Exception as e:
            logger.error(f"Error in candle analysis: {e}")
            return {"signal": "NEUTRAL", "confidence": 0, "error": str(e)}
    
    def _analyze_indicators(self, indicators: Dict[str, Any]) -> Dict[str, Any]:
        """تحليل المؤشرات"""
        try:
            signals = []
            
            # تحليل RSI
            if 'RSI14' in indicators:
                rsi_signal = self._analyze_rsi(indicators['RSI14'])
                signals.append(rsi_signal)
            
            # تحليل MACD
            if 'MACD' in indicators:
                macd_signal = self._analyze_macd(indicators['MACD'])
                signals.append(macd_signal)
            
            # تحليل Bollinger Bands
            if 'BB_UPPER' in indicators and 'BB_LOWER' in indicators:
                bb_signal = self._analyze_bollinger_bands(indicators)
                signals.append(bb_signal)
            
            # تحليل Moving Averages
            ma_signal = self._analyze_moving_averages(indicators)
            if ma_signal:
                signals.append(ma_signal)
            
            # دمج الإشارات
            combined_signal = self._combine_indicator_signals(signals)
            
            return combined_signal
            
        except Exception as e:
            logger.error(f"Error in indicators analysis: {e}")
            return {"signal": "NEUTRAL", "confidence": 0, "error": str(e)}
    
    def _analyze_patterns(self, candles: List[Dict[str, Any]]) -> Dict[str, Any]:
        """تحليل الأنماط"""
        try:
            if len(candles) < 10:
                return {"signal": "NEUTRAL", "confidence": 0}
            
            # تحويل إلى DataFrame
            df = pd.DataFrame(candles)
            df['close'] = df['close'].astype(float)
            df['high'] = df['high'].astype(float)
            df['low'] = df['low'].astype(float)
            df['open'] = df['open'].astype(float)
            
            patterns_found = []
            
            # البحث عن أنماط الانعكاس
            if self._is_hammer_pattern(df.iloc[-1]):
                patterns_found.append({"pattern": "HAMMER", "signal": "CALL", "confidence": 70})
            
            if self._is_shooting_star_pattern(df.iloc[-1]):
                patterns_found.append({"pattern": "SHOOTING_STAR", "signal": "PUT", "confidence": 70})
            
            if self._is_engulfing_pattern(df.iloc[-2:]):
                direction = "CALL" if df.iloc[-1]['close'] > df.iloc[-1]['open'] else "PUT"
                patterns_found.append({"pattern": "ENGULFING", "signal": direction, "confidence": 75})
            
            # تحديد أقوى نمط
            if patterns_found:
                best_pattern = max(patterns_found, key=lambda x: x['confidence'])
                return {
                    "signal": best_pattern["signal"],
                    "confidence": best_pattern["confidence"],
                    "pattern": best_pattern["pattern"],
                    "expiry_minutes": 3  # أنماط الانعكاس تحتاج وقت قصير
                }
            
            return {"signal": "NEUTRAL", "confidence": 0}
            
        except Exception as e:
            logger.error(f"Error in patterns analysis: {e}")
            return {"signal": "NEUTRAL", "confidence": 0, "error": str(e)}
    
    def _analyze_trend(self, candles: List[Dict[str, Any]], indicators: Dict[str, Any]) -> Dict[str, Any]:
        """تحليل الاتجاه"""
        try:
            # تحليل اتجاه المتوسطات المتحركة
            ma_trend = self._get_ma_trend(indicators)
            
            # تحليل اتجاه الأسعار
            price_trend = self._get_price_trend(candles[-20:])
            
            # تحديد قوة الاتجاه
            trend_strength = self._calculate_trend_strength(candles, indicators)
            
            # تحديد الإشارة النهائية
            if ma_trend == price_trend and trend_strength > 60:
                return {
                    "signal": "CALL" if ma_trend == "BULLISH" else "PUT",
                    "confidence": min(trend_strength, 85),
                    "trend": ma_trend,
                    "strength": trend_strength,
                    "expiry_minutes": 5  # الاتجاهات القوية تحتاج وقت أطول
                }
            
            return {"signal": "NEUTRAL", "confidence": 0}
            
        except Exception as e:
            logger.error(f"Error in trend analysis: {e}")
            return {"signal": "NEUTRAL", "confidence": 0, "error": str(e)}
    
    def _combine_analyses(self, candle_analysis: Dict, indicators_analysis: Dict, 
                         patterns_analysis: Dict, trend_analysis: Dict) -> Dict[str, Any]:
        """دمج جميع التحليلات"""
        try:
            analyses = [candle_analysis, indicators_analysis, patterns_analysis, trend_analysis]
            
            # فلترة التحليلات الصحيحة
            valid_analyses = [a for a in analyses if a.get('confidence', 0) > 0]
            
            if not valid_analyses:
                return self._create_neutral_result("No valid signals found")
            
            # حساب الإشارة الأكثر تكراراً
            call_signals = [a for a in valid_analyses if a.get('signal') == 'CALL']
            put_signals = [a for a in valid_analyses if a.get('signal') == 'PUT']
            
            if len(call_signals) > len(put_signals):
                direction = "CALL"
                supporting_analyses = call_signals
            elif len(put_signals) > len(call_signals):
                direction = "PUT" 
                supporting_analyses = put_signals
            else:
                return self._create_neutral_result("Conflicting signals")
            
            # حساب الثقة المتوسطة
            avg_confidence = sum(a['confidence'] for a in supporting_analyses) / len(supporting_analyses)
            
            # تحديد زمن الصفقة بناءً على نوع الإشارات
            expiry_minutes = self._determine_expiry_time(supporting_analyses)
            
            # تطبيق مرشحات الجودة
            if avg_confidence < 60:
                return self._create_neutral_result("Low confidence signals")
            
            if len(supporting_analyses) < 2:
                avg_confidence *= 0.8  # تقليل الثقة إذا كان هناك دعم قليل
            
            return {
                "layer": self.name,
                "signal": direction,
                "confidence": min(avg_confidence, 95),
                "supporting_analyses": len(supporting_analyses),
                "total_analyses": len(valid_analyses),
                "expiry_minutes": expiry_minutes,
                "details": {
                    "candle": candle_analysis,
                    "indicators": indicators_analysis,
                    "patterns": patterns_analysis,
                    "trend": trend_analysis
                },
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error combining analyses: {e}")
            return self._create_error_result(str(e))

    # ==================== دوال مساعدة ====================

    def _get_candle_type(self, candle: Dict[str, Any]) -> str:
        """تحديد نوع الشمعة"""
        try:
            open_price = float(candle['open'])
            close_price = float(candle['close'])
            high_price = float(candle['high'])
            low_price = float(candle['low'])

            body_size = abs(close_price - open_price)
            total_range = high_price - low_price

            if total_range == 0:
                return "DOJI"

            body_ratio = body_size / total_range

            if body_ratio < 0.1:
                return "DOJI"
            elif body_ratio > 0.7:
                return "STRONG_BULLISH" if close_price > open_price else "STRONG_BEARISH"
            else:
                return "BULLISH" if close_price > open_price else "BEARISH"

        except Exception:
            return "UNKNOWN"

    def _get_short_trend(self, candles: List[Dict[str, Any]]) -> str:
        """تحديد الاتجاه قصير المدى"""
        try:
            if len(candles) < 3:
                return "NEUTRAL"

            closes = [float(c['close']) for c in candles]

            # حساب المتوسط المتحرك البسيط
            if closes[-1] > sum(closes[:-1]) / len(closes[:-1]):
                return "BULLISH"
            else:
                return "BEARISH"

        except Exception:
            return "NEUTRAL"

    def _detect_reversal_patterns(self, candles: List[Dict[str, Any]]) -> str:
        """كشف أنماط الانعكاس"""
        try:
            if len(candles) < 2:
                return "NONE"

            last = candles[-1]
            prev = candles[-2]

            last_close = float(last['close'])
            last_open = float(last['open'])
            prev_close = float(prev['close'])
            prev_open = float(prev['open'])

            # نمط الابتلاع الصاعد
            if (prev_close < prev_open and  # شمعة هابطة سابقة
                last_close > last_open and  # شمعة صاعدة حالية
                last_open < prev_close and  # فتح أقل من إغلاق السابقة
                last_close > prev_open):    # إغلاق أعلى من فتح السابقة
                return "BULLISH_ENGULFING"

            # نمط الابتلاع الهابط
            if (prev_close > prev_open and  # شمعة صاعدة سابقة
                last_close < last_open and  # شمعة هابطة حالية
                last_open > prev_close and  # فتح أعلى من إغلاق السابقة
                last_close < prev_open):    # إغلاق أقل من فتح السابقة
                return "BEARISH_ENGULFING"

            return "NONE"

        except Exception:
            return "NONE"

    def _calculate_candle_strength(self, current: Dict[str, Any], previous: Dict[str, Any]) -> float:
        """حساب قوة الشمعة"""
        try:
            curr_body = abs(float(current['close']) - float(current['open']))
            curr_range = float(current['high']) - float(current['low'])

            prev_body = abs(float(previous['close']) - float(previous['open']))
            prev_range = float(previous['high']) - float(previous['low'])

            if prev_range == 0 or curr_range == 0:
                return 50

            # نسبة الجسم إلى المدى
            body_ratio = (curr_body / curr_range) * 100

            # مقارنة مع الشمعة السابقة
            size_comparison = (curr_range / prev_range) * 100

            # حساب القوة النهائية
            strength = (body_ratio + min(size_comparison, 150)) / 2

            return min(max(strength, 0), 100)

        except Exception:
            return 50

    def _determine_candle_signal(self, candle_type: str, trend: str, pattern: str, strength: float) -> Dict[str, Any]:
        """تحديد إشارة الشمعة"""
        try:
            confidence = 0
            direction = "NEUTRAL"
            expiry_minutes = 1

            # تحليل نوع الشمعة
            if candle_type in ["STRONG_BULLISH", "BULLISH"]:
                direction = "CALL"
                confidence += 30 if "STRONG" in candle_type else 20
            elif candle_type in ["STRONG_BEARISH", "BEARISH"]:
                direction = "PUT"
                confidence += 30 if "STRONG" in candle_type else 20

            # تحليل الاتجاه
            if trend == "BULLISH" and direction == "CALL":
                confidence += 25
                expiry_minutes = 3
            elif trend == "BEARISH" and direction == "PUT":
                confidence += 25
                expiry_minutes = 3
            elif trend != "NEUTRAL" and direction != "NEUTRAL":
                confidence -= 15  # اتجاه متضارب

            # تحليل الأنماط
            if pattern == "BULLISH_ENGULFING" and direction == "CALL":
                confidence += 30
                expiry_minutes = 2
            elif pattern == "BEARISH_ENGULFING" and direction == "PUT":
                confidence += 30
                expiry_minutes = 2

            # تطبيق قوة الشمعة
            confidence = confidence * (strength / 100)

            return {
                "direction": direction,
                "confidence": min(max(confidence, 0), 95),
                "expiry_minutes": expiry_minutes
            }

        except Exception:
            return {"direction": "NEUTRAL", "confidence": 0, "expiry_minutes": 1}

    def _analyze_rsi(self, rsi_value: float) -> Dict[str, Any]:
        """تحليل مؤشر RSI"""
        try:
            if rsi_value < 30:
                return {"signal": "CALL", "confidence": 70, "reason": "RSI Oversold"}
            elif rsi_value > 70:
                return {"signal": "PUT", "confidence": 70, "reason": "RSI Overbought"}
            elif rsi_value < 40:
                return {"signal": "CALL", "confidence": 50, "reason": "RSI Low"}
            elif rsi_value > 60:
                return {"signal": "PUT", "confidence": 50, "reason": "RSI High"}
            else:
                return {"signal": "NEUTRAL", "confidence": 0, "reason": "RSI Neutral"}

        except Exception:
            return {"signal": "NEUTRAL", "confidence": 0, "reason": "RSI Error"}

    def _analyze_macd(self, macd_data: Dict[str, Any]) -> Dict[str, Any]:
        """تحليل مؤشر MACD"""
        try:
            macd_line = macd_data.get('MACD', 0)
            signal_line = macd_data.get('SIGNAL', 0)
            histogram = macd_data.get('HISTOGRAM', 0)

            confidence = 0
            direction = "NEUTRAL"

            # تحليل تقاطع MACD
            if macd_line > signal_line and histogram > 0:
                direction = "CALL"
                confidence = 60
            elif macd_line < signal_line and histogram < 0:
                direction = "PUT"
                confidence = 60

            # تعزيز الإشارة بناءً على قوة الهيستوجرام
            if abs(histogram) > 0.0001:
                confidence += 15

            return {
                "signal": direction,
                "confidence": min(confidence, 85),
                "reason": f"MACD {direction.lower()}"
            }

        except Exception:
            return {"signal": "NEUTRAL", "confidence": 0, "reason": "MACD Error"}

    def _analyze_bollinger_bands(self, indicators: Dict[str, Any]) -> Dict[str, Any]:
        """تحليل Bollinger Bands"""
        try:
            bb_upper = indicators.get('BB_UPPER', 0)
            bb_lower = indicators.get('BB_LOWER', 0)
            bb_middle = indicators.get('BB_MIDDLE', 0)
            current_price = indicators.get('CLOSE', 0)

            if current_price <= bb_lower:
                return {"signal": "CALL", "confidence": 75, "reason": "Price at BB Lower"}
            elif current_price >= bb_upper:
                return {"signal": "PUT", "confidence": 75, "reason": "Price at BB Upper"}
            elif current_price < bb_middle:
                return {"signal": "CALL", "confidence": 40, "reason": "Price below BB Middle"}
            elif current_price > bb_middle:
                return {"signal": "PUT", "confidence": 40, "reason": "Price above BB Middle"}
            else:
                return {"signal": "NEUTRAL", "confidence": 0, "reason": "BB Neutral"}

        except Exception:
            return {"signal": "NEUTRAL", "confidence": 0, "reason": "BB Error"}

    def _analyze_moving_averages(self, indicators: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """تحليل المتوسطات المتحركة"""
        try:
            sma10 = indicators.get('SMA10')
            sma20 = indicators.get('SMA20')
            ema5 = indicators.get('EMA5')
            ema21 = indicators.get('EMA21')
            current_price = indicators.get('CLOSE', 0)

            signals = []

            # تحليل SMA
            if sma10 and sma20:
                if sma10 > sma20 and current_price > sma10:
                    signals.append({"signal": "CALL", "confidence": 60})
                elif sma10 < sma20 and current_price < sma10:
                    signals.append({"signal": "PUT", "confidence": 60})

            # تحليل EMA
            if ema5 and ema21:
                if ema5 > ema21 and current_price > ema5:
                    signals.append({"signal": "CALL", "confidence": 65})
                elif ema5 < ema21 and current_price < ema5:
                    signals.append({"signal": "PUT", "confidence": 65})

            if not signals:
                return None

            # دمج إشارات المتوسطات
            call_signals = [s for s in signals if s['signal'] == 'CALL']
            put_signals = [s for s in signals if s['signal'] == 'PUT']

            if len(call_signals) > len(put_signals):
                avg_conf = sum(s['confidence'] for s in call_signals) / len(call_signals)
                return {"signal": "CALL", "confidence": avg_conf, "reason": "MA Bullish"}
            elif len(put_signals) > len(call_signals):
                avg_conf = sum(s['confidence'] for s in put_signals) / len(put_signals)
                return {"signal": "PUT", "confidence": avg_conf, "reason": "MA Bearish"}

            return None

        except Exception:
            return None

    def _combine_indicator_signals(self, signals: List[Dict[str, Any]]) -> Dict[str, Any]:
        """دمج إشارات المؤشرات"""
        try:
            if not signals:
                return {"signal": "NEUTRAL", "confidence": 0}

            call_signals = [s for s in signals if s.get('signal') == 'CALL']
            put_signals = [s for s in signals if s.get('signal') == 'PUT']

            if len(call_signals) > len(put_signals):
                avg_confidence = sum(s['confidence'] for s in call_signals) / len(call_signals)
                return {
                    "signal": "CALL",
                    "confidence": min(avg_confidence, 90),
                    "supporting_indicators": len(call_signals),
                    "expiry_minutes": 2
                }
            elif len(put_signals) > len(call_signals):
                avg_confidence = sum(s['confidence'] for s in put_signals) / len(put_signals)
                return {
                    "signal": "PUT",
                    "confidence": min(avg_confidence, 90),
                    "supporting_indicators": len(put_signals),
                    "expiry_minutes": 2
                }
            else:
                return {"signal": "NEUTRAL", "confidence": 0}

        except Exception:
            return {"signal": "NEUTRAL", "confidence": 0}

    def _is_hammer_pattern(self, candle: pd.Series) -> bool:
        """كشف نمط المطرقة"""
        try:
            body = abs(candle['close'] - candle['open'])
            lower_shadow = candle['open'] - candle['low'] if candle['close'] > candle['open'] else candle['close'] - candle['low']
            upper_shadow = candle['high'] - candle['close'] if candle['close'] > candle['open'] else candle['high'] - candle['open']

            return (lower_shadow > 2 * body and upper_shadow < body * 0.5)
        except Exception:
            return False

    def _is_shooting_star_pattern(self, candle: pd.Series) -> bool:
        """كشف نمط النجمة الساقطة"""
        try:
            body = abs(candle['close'] - candle['open'])
            lower_shadow = candle['open'] - candle['low'] if candle['close'] > candle['open'] else candle['close'] - candle['low']
            upper_shadow = candle['high'] - candle['close'] if candle['close'] > candle['open'] else candle['high'] - candle['open']

            return (upper_shadow > 2 * body and lower_shadow < body * 0.5)
        except Exception:
            return False

    def _is_engulfing_pattern(self, candles: pd.DataFrame) -> bool:
        """كشف نمط الابتلاع"""
        try:
            if len(candles) < 2:
                return False

            prev = candles.iloc[-2]
            curr = candles.iloc[-1]

            prev_bullish = prev['close'] > prev['open']
            curr_bullish = curr['close'] > curr['open']

            # ابتلاع صاعد
            if not prev_bullish and curr_bullish:
                return (curr['open'] < prev['close'] and curr['close'] > prev['open'])

            # ابتلاع هابط
            if prev_bullish and not curr_bullish:
                return (curr['open'] > prev['close'] and curr['close'] < prev['open'])

            return False
        except Exception:
            return False

    def _get_ma_trend(self, indicators: Dict[str, Any]) -> str:
        """تحديد اتجاه المتوسطات المتحركة"""
        try:
            sma10 = indicators.get('SMA10')
            sma20 = indicators.get('SMA20')
            ema5 = indicators.get('EMA5')
            ema21 = indicators.get('EMA21')

            bullish_signals = 0
            bearish_signals = 0

            if sma10 and sma20:
                if sma10 > sma20:
                    bullish_signals += 1
                else:
                    bearish_signals += 1

            if ema5 and ema21:
                if ema5 > ema21:
                    bullish_signals += 1
                else:
                    bearish_signals += 1

            if bullish_signals > bearish_signals:
                return "BULLISH"
            elif bearish_signals > bullish_signals:
                return "BEARISH"
            else:
                return "NEUTRAL"

        except Exception:
            return "NEUTRAL"

    def _get_price_trend(self, candles: List[Dict[str, Any]]) -> str:
        """تحديد اتجاه الأسعار"""
        try:
            if len(candles) < 5:
                return "NEUTRAL"

            closes = [float(c['close']) for c in candles]

            # حساب المتوسط المتحرك البسيط للنصف الأول والثاني
            mid_point = len(closes) // 2
            first_half_avg = sum(closes[:mid_point]) / mid_point
            second_half_avg = sum(closes[mid_point:]) / (len(closes) - mid_point)

            if second_half_avg > first_half_avg * 1.001:  # 0.1% تحسن
                return "BULLISH"
            elif second_half_avg < first_half_avg * 0.999:  # 0.1% تراجع
                return "BEARISH"
            else:
                return "NEUTRAL"

        except Exception:
            return "NEUTRAL"

    def _calculate_trend_strength(self, candles: List[Dict[str, Any]], indicators: Dict[str, Any]) -> float:
        """حساب قوة الاتجاه"""
        try:
            strength = 0

            # قوة الاتجاه من المتوسطات المتحركة
            ma_trend = self._get_ma_trend(indicators)
            if ma_trend != "NEUTRAL":
                strength += 30

            # قوة الاتجاه من الأسعار
            price_trend = self._get_price_trend(candles[-10:])
            if price_trend != "NEUTRAL":
                strength += 25

            # قوة من RSI
            rsi = indicators.get('RSI14', 50)
            if rsi < 30 or rsi > 70:
                strength += 20
            elif rsi < 40 or rsi > 60:
                strength += 10

            # قوة من MACD
            macd_data = indicators.get('MACD', {})
            if isinstance(macd_data, dict):
                histogram = macd_data.get('HISTOGRAM', 0)
                if abs(histogram) > 0.0001:
                    strength += 15

            return min(strength, 100)

        except Exception:
            return 0

    def _determine_expiry_time(self, analyses: List[Dict[str, Any]]) -> int:
        """تحديد زمن انتهاء الصفقة"""
        try:
            expiry_times = []

            for analysis in analyses:
                if 'expiry_minutes' in analysis:
                    expiry_times.append(analysis['expiry_minutes'])

            if not expiry_times:
                return 1  # افتراضي

            # استخدام المتوسط مع تقريب
            avg_expiry = sum(expiry_times) / len(expiry_times)

            # تقريب إلى أقرب قيمة مسموحة (1, 2, 3, 5)
            if avg_expiry <= 1.5:
                return 1
            elif avg_expiry <= 2.5:
                return 2
            elif avg_expiry <= 4:
                return 3
            else:
                return 5

        except Exception:
            return 1

    def _create_error_result(self, error_msg: str) -> Dict[str, Any]:
        """إنشاء نتيجة خطأ"""
        return {
            "layer": self.name,
            "signal": "NEUTRAL",
            "confidence": 0,
            "error": error_msg,
            "timestamp": datetime.now().isoformat()
        }

    def _create_neutral_result(self, reason: str) -> Dict[str, Any]:
        """إنشاء نتيجة محايدة"""
        return {
            "layer": self.name,
            "signal": "NEUTRAL",
            "confidence": 0,
            "reason": reason,
            "timestamp": datetime.now().isoformat()
        }
