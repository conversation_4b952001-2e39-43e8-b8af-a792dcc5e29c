"""
Integrated Strategy System V2 - النظام المتكامل للاستراتيجية
=========================================================
النظام الشامل الذي يجمع التحليل والمخاطر والتنفيذ
"""

import asyncio
import json
import os
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta

from .decision_maker import StrategyDecisionMaker
from ..risk_management.advanced_risk_manager import AdvancedRiskManager

logger = logging.getLogger(__name__)

class IntegratedStrategySystem:
    """النظام المتكامل للاستراتيجية V2"""
    
    def __init__(self, initial_balance: float = 1000.0):
        """
        تهيئة النظام المتكامل
        
        Args:
            initial_balance: الرصيد الأولي للحساب
        """
        self.name = "CANFX Integrated Strategy System V2"
        self.version = "2.0.0"
        
        # تهيئة المكونات الرئيسية
        self.strategy = StrategyDecisionMaker()
        self.risk_manager = AdvancedRiskManager()
        
        # تعيين الرصيد الأولي
        self.risk_manager.set_initial_balance(initial_balance)
        
        # إعدادات النظام
        self.system_settings = {
            'auto_trading_enabled': False,
            'max_concurrent_analyses': 3,
            'analysis_timeout_seconds': 30,
            'min_time_between_trades': 60,  # ثانية
            'supported_pairs': [
                'AUDCHF_otc', 'AUDCAD_otc', 'AUDJPY_otc', 'AUDNZD_otc', 'AUDUSD_otc',
                'CADCHF_otc', 'CADJPY_otc', 'CHFJPY_otc', 'EURAUD_otc', 'EURCAD_otc',
                'EURCHF_otc', 'EURGBP_otc', 'EURJPY_otc', 'EURNZD_otc', 'EURUSD_otc',
                'GBPAUD_otc', 'GBPCAD_otc', 'GBPCHF_otc', 'GBPJPY_otc', 'GBPNZD_otc',
                'GBPUSD_otc', 'NZDCAD_otc', 'NZDCHF_otc', 'NZDJPY_otc', 'NZDUSD_otc',
                'USDCAD_otc', 'USDCHF_otc', 'USDJPY_otc'
            ]
        }
        
        # إحصائيات النظام
        self.system_stats = {
            'total_analyses': 0,
            'successful_analyses': 0,
            'failed_analyses': 0,
            'total_trades_executed': 0,
            'total_trades_rejected': 0,
            'system_uptime_start': datetime.now(),
            'last_analysis_time': None,
            'last_trade_time': None
        }
        
        # ذاكرة النظام
        self.analysis_history = []
        self.trade_history = []
        self.max_history_size = 500
        
        logger.info(f"✅ {self.name} v{self.version} initialized")
        logger.info(f"💰 Initial balance: ${initial_balance:.2f}")
        logger.info(f"📊 Supported pairs: {len(self.system_settings['supported_pairs'])}")
    
    async def analyze_and_trade(self, pair_name: str, account_balance: float = None) -> Dict[str, Any]:
        """
        التحليل الشامل واتخاذ قرار التداول
        
        Args:
            pair_name: اسم زوج العملات
            account_balance: رصيد الحساب الحالي (اختياري)
            
        Returns:
            نتيجة التحليل وقرار التداول
        """
        analysis_start = datetime.now()
        
        try:
            logger.info(f"🔍 Starting integrated analysis for {pair_name}")
            
            # التحقق من دعم الزوج
            if not self._is_pair_supported(pair_name):
                return self._create_error_result(f"Pair {pair_name} is not supported", pair_name)
            
            # التحقق من الحد الأدنى للوقت بين التحليلات
            if not self._can_analyze_now():
                return self._create_error_result("Too soon since last analysis", pair_name)
            
            # تحديث رصيد الحساب
            if account_balance is not None:
                self.risk_manager.set_initial_balance(account_balance)
            
            current_balance = self.risk_manager.risk_stats['current_balance']
            
            # المرحلة 1: التحليل الاستراتيجي
            logger.info(f"📊 Phase 1: Strategic analysis for {pair_name}")
            analysis_result = await self._perform_strategic_analysis(pair_name)
            
            if analysis_result['signal'] == 'NEUTRAL':
                self._record_analysis_result(pair_name, analysis_result, 'NO_SIGNAL')
                return self._create_no_trade_result(analysis_result, pair_name)
            
            # المرحلة 2: تقييم المخاطر
            logger.info(f"⚖️ Phase 2: Risk assessment for {pair_name}")
            risk_assessment = await self._perform_risk_assessment(analysis_result, current_balance)
            
            if risk_assessment['status'] != 'APPROVED':
                self._record_analysis_result(pair_name, analysis_result, 'RISK_REJECTED')
                return self._create_risk_rejected_result(analysis_result, risk_assessment, pair_name)
            
            # المرحلة 3: تحسين المعاملات
            logger.info(f"🎯 Phase 3: Parameter optimization for {pair_name}")
            optimized_params = await self._optimize_trade_parameters(analysis_result, risk_assessment)
            
            # المرحلة 4: التحقق النهائي
            logger.info(f"✅ Phase 4: Final validation for {pair_name}")
            final_validation = await self._perform_final_validation(optimized_params, current_balance)
            
            if not final_validation['approved']:
                self._record_analysis_result(pair_name, analysis_result, 'FINAL_REJECTED')
                return self._create_validation_failed_result(analysis_result, final_validation, pair_name)
            
            # إنشاء التوصية النهائية
            trade_recommendation = self._create_trade_recommendation(
                pair_name, analysis_result, risk_assessment, optimized_params, analysis_start
            )
            
            # تسجيل النتيجة
            self._record_analysis_result(pair_name, analysis_result, 'TRADE_RECOMMENDED')
            self._update_system_stats('analysis_success')
            
            logger.info(f"✅ Integrated analysis completed for {pair_name}")
            return trade_recommendation
            
        except Exception as e:
            logger.error(f"Error in integrated analysis for {pair_name}: {e}")
            self._update_system_stats('analysis_failed')
            return self._create_error_result(str(e), pair_name)
    
    async def execute_trade(self, trade_recommendation: Dict[str, Any]) -> Dict[str, Any]:
        """
        تنفيذ الصفقة (محاكاة)
        
        Args:
            trade_recommendation: توصية التداول
            
        Returns:
            نتيجة تنفيذ الصفقة
        """
        try:
            pair_name = trade_recommendation.get('pair')
            signal = trade_recommendation.get('signal')
            amount = trade_recommendation.get('position_size')
            confidence = trade_recommendation.get('confidence')
            expiry = trade_recommendation.get('expiry_minutes')
            
            logger.info(f"🚀 Executing trade: {signal} {pair_name} ${amount:.2f} for {expiry}min")
            
            # محاكاة تنفيذ الصفقة
            execution_result = await self._simulate_trade_execution(
                pair_name, signal, amount, expiry, confidence
            )
            
            # تسجيل نتيجة الصفقة
            self.risk_manager.record_trade_result(
                amount, 
                execution_result['outcome'], 
                execution_result['profit_loss'], 
                confidence
            )
            
            # تحديث إحصائيات النظام
            if execution_result['outcome'] == 'WIN':
                self._update_system_stats('trade_success')
            else:
                self._update_system_stats('trade_loss')
            
            # حفظ في سجل التداول
            self._record_trade_execution(trade_recommendation, execution_result)
            
            return execution_result
            
        except Exception as e:
            logger.error(f"Error executing trade: {e}")
            self._update_system_stats('trade_failed')
            return {
                'status': 'ERROR',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def scan_all_pairs(self, account_balance: float = None) -> Dict[str, Any]:
        """
        مسح جميع الأزواج المدعومة للبحث عن فرص
        
        Args:
            account_balance: رصيد الحساب الحالي
            
        Returns:
            نتائج المسح مع أفضل الفرص
        """
        scan_start = datetime.now()
        
        try:
            logger.info(f"🔍 Starting full market scan of {len(self.system_settings['supported_pairs'])} pairs")
            
            opportunities = []
            failed_analyses = []
            
            # تحديد عدد التحليلات المتزامنة
            max_concurrent = self.system_settings['max_concurrent_analyses']
            pairs = self.system_settings['supported_pairs']
            
            # تقسيم الأزواج إلى مجموعات
            for i in range(0, len(pairs), max_concurrent):
                batch = pairs[i:i + max_concurrent]
                
                # تحليل المجموعة بشكل متزامن
                batch_tasks = [
                    self.analyze_and_trade(pair, account_balance) 
                    for pair in batch
                ]
                
                batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
                
                # معالجة النتائج
                for pair, result in zip(batch, batch_results):
                    if isinstance(result, Exception):
                        failed_analyses.append({'pair': pair, 'error': str(result)})
                    elif result.get('signal') != 'NEUTRAL' and result.get('status') == 'TRADE_RECOMMENDED':
                        opportunities.append(result)
                
                # توقف قصير بين المجموعات
                if i + max_concurrent < len(pairs):
                    await asyncio.sleep(1)
            
            # ترتيب الفرص حسب الثقة
            opportunities.sort(key=lambda x: x.get('confidence', 0), reverse=True)
            
            scan_duration = (datetime.now() - scan_start).total_seconds()
            
            scan_summary = {
                'scan_type': 'FULL_MARKET_SCAN',
                'timestamp': datetime.now().isoformat(),
                'scan_duration_seconds': round(scan_duration, 2),
                'pairs_scanned': len(pairs),
                'opportunities_found': len(opportunities),
                'failed_analyses': len(failed_analyses),
                'best_opportunities': opportunities[:5],  # أفضل 5 فرص
                'all_opportunities': opportunities,
                'failed_pairs': failed_analyses,
                'system_stats': self.get_system_summary()
            }
            
            logger.info(f"✅ Market scan completed: {len(opportunities)} opportunities found in {scan_duration:.1f}s")
            return scan_summary
            
        except Exception as e:
            logger.error(f"Error in market scan: {e}")
            return {
                'scan_type': 'FULL_MARKET_SCAN',
                'timestamp': datetime.now().isoformat(),
                'error': str(e),
                'opportunities_found': 0
            }
    
    async def _perform_strategic_analysis(self, pair_name: str) -> Dict[str, Any]:
        """تنفيذ التحليل الاستراتيجي"""
        try:
            # تحليل السوق باستخدام الطبقات الأربع
            analysis_result = self.strategy.analyze_market(pair_name)
            self.system_stats['total_analyses'] += 1
            self.system_stats['last_analysis_time'] = datetime.now()
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"Strategic analysis failed for {pair_name}: {e}")
            raise
    
    async def _perform_risk_assessment(self, analysis_result: Dict[str, Any], 
                                     current_balance: float) -> Dict[str, Any]:
        """تنفيذ تقييم المخاطر"""
        try:
            confidence = analysis_result.get('confidence', 0)
            signal_quality = 'HIGH' if confidence >= 90 else 'STANDARD' if confidence >= 80 else 'LOW'
            
            # حساب حجم المركز
            position_calc = self.risk_manager.calculate_position_size(
                confidence, current_balance, signal_quality
            )
            
            if position_calc['status'] != 'APPROVED':
                return position_calc
            
            # التحقق من صحة الصفقة
            trade_validation = self.risk_manager.validate_trade(
                position_calc['position_size'], confidence, current_balance
            )
            
            # دمج النتائج
            return {
                'status': trade_validation['status'],
                'position_size': position_calc['position_size'],
                'risk_percentage': position_calc['risk_percentage'],
                'risk_analysis': position_calc.get('risk_analysis', {}),
                'validation_details': trade_validation,
                'signal_quality': signal_quality
            }
            
        except Exception as e:
            logger.error(f"Risk assessment failed: {e}")
            return {'status': 'ERROR', 'error': str(e)}
    
    async def _optimize_trade_parameters(self, analysis_result: Dict[str, Any], 
                                       risk_assessment: Dict[str, Any]) -> Dict[str, Any]:
        """تحسين معاملات التداول"""
        try:
            # تحسين زمن انتهاء الصفقة
            base_expiry = analysis_result.get('expiry_minutes', 2)
            confidence = analysis_result.get('confidence', 0)
            
            # تعديل الزمن بناءً على الثقة ونوعية الإشارة
            if confidence >= 95:
                optimized_expiry = max(1, base_expiry - 1)  # ثقة عالية جداً = وقت أقل
            elif confidence >= 85:
                optimized_expiry = base_expiry  # ثقة عالية = وقت عادي
            else:
                optimized_expiry = min(5, base_expiry + 1)  # ثقة متوسطة = وقت أكثر
            
            return {
                'optimized_expiry': optimized_expiry,
                'original_expiry': base_expiry,
                'confidence_factor': confidence / 100,
                'position_size': risk_assessment['position_size'],
                'risk_level': risk_assessment.get('risk_analysis', {}).get('risk_level', 'MODERATE')
            }
            
        except Exception as e:
            logger.error(f"Parameter optimization failed: {e}")
            return {
                'optimized_expiry': 2,
                'error': str(e)
            }
    
    async def _perform_final_validation(self, optimized_params: Dict[str, Any], 
                                      current_balance: float) -> Dict[str, Any]:
        """التحقق النهائي قبل التداول"""
        try:
            # التحقق من الرصيد الكافي
            position_size = optimized_params.get('position_size', 0)
            if position_size > current_balance:
                return {
                    'approved': False,
                    'reason': f'Insufficient balance: ${position_size:.2f} > ${current_balance:.2f}'
                }
            
            # التحقق من حالة إدارة المخاطر
            if self.risk_manager._is_emergency_stop_active():
                return {
                    'approved': False,
                    'reason': 'Emergency stop is active'
                }
            
            if self.risk_manager._is_cooling_period_active():
                return {
                    'approved': False,
                    'reason': 'Cooling period is active'
                }
            
            # التحقق من الحد الأدنى للوقت بين الصفقات
            if not self._can_trade_now():
                return {
                    'approved': False,
                    'reason': 'Too soon since last trade'
                }
            
            return {
                'approved': True,
                'reason': 'All validations passed',
                'final_position_size': position_size,
                'final_expiry': optimized_params.get('optimized_expiry', 2)
            }
            
        except Exception as e:
            logger.error(f"Final validation failed: {e}")
            return {
                'approved': False,
                'reason': f'Validation error: {str(e)}'
            }

    async def _simulate_trade_execution(self, pair_name: str, signal: str, amount: float,
                                      expiry: int, confidence: float) -> Dict[str, Any]:
        """محاكاة تنفيذ الصفقة"""
        try:
            # محاكاة وقت التنفيذ
            await asyncio.sleep(0.1)

            # محاكاة نتيجة الصفقة بناءً على الثقة
            # كلما زادت الثقة، زادت احتمالية النجاح
            import random

            # تحويل الثقة إلى احتمالية نجاح
            base_success_rate = 0.5  # 50% أساسي
            confidence_bonus = (confidence - 50) / 100 * 0.4  # حتى 40% إضافية
            success_probability = min(base_success_rate + confidence_bonus, 0.9)

            # تحديد النتيجة
            is_win = random.random() < success_probability

            if is_win:
                # ربح عادة 80-90% من المبلغ المستثمر
                payout_rate = random.uniform(0.8, 0.9)
                profit = amount * payout_rate

                return {
                    'status': 'EXECUTED',
                    'outcome': 'WIN',
                    'profit_loss': profit,
                    'payout_rate': payout_rate,
                    'execution_time': datetime.now().isoformat(),
                    'pair': pair_name,
                    'signal': signal,
                    'amount': amount,
                    'expiry_minutes': expiry,
                    'confidence': confidence
                }
            else:
                # خسارة = المبلغ المستثمر بالكامل
                return {
                    'status': 'EXECUTED',
                    'outcome': 'LOSS',
                    'profit_loss': -amount,
                    'execution_time': datetime.now().isoformat(),
                    'pair': pair_name,
                    'signal': signal,
                    'amount': amount,
                    'expiry_minutes': expiry,
                    'confidence': confidence
                }

        except Exception as e:
            logger.error(f"Trade simulation failed: {e}")
            return {
                'status': 'FAILED',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    def _create_trade_recommendation(self, pair_name: str, analysis_result: Dict[str, Any],
                                   risk_assessment: Dict[str, Any], optimized_params: Dict[str, Any],
                                   analysis_start: datetime) -> Dict[str, Any]:
        """إنشاء توصية التداول النهائية"""
        try:
            analysis_duration = (datetime.now() - analysis_start).total_seconds()

            return {
                'status': 'TRADE_RECOMMENDED',
                'system': self.name,
                'version': self.version,
                'timestamp': datetime.now().isoformat(),
                'analysis_duration_seconds': round(analysis_duration, 3),

                # معلومات الصفقة
                'pair': pair_name,
                'signal': analysis_result['signal'],
                'confidence': analysis_result['confidence'],
                'expiry_minutes': optimized_params['optimized_expiry'],
                'position_size': risk_assessment['position_size'],

                # تفاصيل التحليل
                'analysis_summary': {
                    'layer_agreement': analysis_result.get('layer_agreement', {}),
                    'supporting_layers': analysis_result.get('layer_agreement', {}).get('supporting_layers', []),
                    'agreement_ratio': analysis_result.get('layer_agreement', {}).get('agreement_ratio', 0)
                },

                # تفاصيل المخاطر
                'risk_summary': {
                    'risk_percentage': risk_assessment['risk_percentage'],
                    'risk_level': risk_assessment.get('risk_analysis', {}).get('risk_level', 'MODERATE'),
                    'signal_quality': risk_assessment.get('signal_quality', 'STANDARD'),
                    'max_loss': risk_assessment['position_size']
                },

                # معاملات محسنة
                'optimization': {
                    'original_expiry': optimized_params.get('original_expiry', 2),
                    'optimized_expiry': optimized_params['optimized_expiry'],
                    'confidence_factor': optimized_params.get('confidence_factor', 1.0)
                },

                # إحصائيات النظام
                'system_state': {
                    'total_analyses_today': self.system_stats['total_analyses'],
                    'risk_manager_state': self.risk_manager.get_risk_summary(),
                    'emergency_stop_active': self.risk_manager.risk_stats['emergency_stop_active']
                }
            }

        except Exception as e:
            logger.error(f"Error creating trade recommendation: {e}")
            return {
                'status': 'ERROR',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    # ==================== دوال مساعدة ====================

    def _is_pair_supported(self, pair_name: str) -> bool:
        """التحقق من دعم الزوج"""
        return pair_name in self.system_settings['supported_pairs']

    def _can_analyze_now(self) -> bool:
        """التحقق من إمكانية التحليل الآن"""
        if not self.system_stats['last_analysis_time']:
            return True

        time_since_last = (datetime.now() - self.system_stats['last_analysis_time']).total_seconds()
        min_interval = 10  # 10 ثواني بين التحليلات

        return time_since_last >= min_interval

    def _can_trade_now(self) -> bool:
        """التحقق من إمكانية التداول الآن"""
        if not self.system_stats['last_trade_time']:
            return True

        time_since_last = (datetime.now() - self.system_stats['last_trade_time']).total_seconds()
        min_interval = self.system_settings['min_time_between_trades']

        return time_since_last >= min_interval

    def _record_analysis_result(self, pair_name: str, analysis_result: Dict[str, Any], status: str):
        """تسجيل نتيجة التحليل"""
        try:
            record = {
                'timestamp': datetime.now().isoformat(),
                'pair': pair_name,
                'signal': analysis_result.get('signal', 'NEUTRAL'),
                'confidence': analysis_result.get('confidence', 0),
                'status': status,
                'layer_agreement': analysis_result.get('layer_agreement', {})
            }

            self.analysis_history.append(record)

            # الحفاظ على حجم الذاكرة
            if len(self.analysis_history) > self.max_history_size:
                self.analysis_history = self.analysis_history[-self.max_history_size:]

        except Exception as e:
            logger.error(f"Error recording analysis result: {e}")

    def _record_trade_execution(self, recommendation: Dict[str, Any], execution_result: Dict[str, Any]):
        """تسجيل تنفيذ الصفقة"""
        try:
            record = {
                'timestamp': datetime.now().isoformat(),
                'recommendation': recommendation,
                'execution': execution_result
            }

            self.trade_history.append(record)
            self.system_stats['last_trade_time'] = datetime.now()

            # الحفاظ على حجم الذاكرة
            if len(self.trade_history) > self.max_history_size:
                self.trade_history = self.trade_history[-self.max_history_size:]

        except Exception as e:
            logger.error(f"Error recording trade execution: {e}")

    def _update_system_stats(self, event_type: str):
        """تحديث إحصائيات النظام"""
        try:
            if event_type == 'analysis_success':
                self.system_stats['successful_analyses'] += 1
            elif event_type == 'analysis_failed':
                self.system_stats['failed_analyses'] += 1
            elif event_type == 'trade_success':
                self.system_stats['total_trades_executed'] += 1
            elif event_type == 'trade_loss':
                self.system_stats['total_trades_executed'] += 1
            elif event_type == 'trade_failed':
                self.system_stats['total_trades_rejected'] += 1

        except Exception as e:
            logger.error(f"Error updating system stats: {e}")

    def _create_no_trade_result(self, analysis_result: Dict[str, Any], pair_name: str) -> Dict[str, Any]:
        """إنشاء نتيجة عدم التداول"""
        return {
            'status': 'NO_TRADE',
            'pair': pair_name,
            'signal': 'NEUTRAL',
            'confidence': 0,
            'reason': analysis_result.get('reason', 'No trading signal detected'),
            'analysis_details': analysis_result,
            'timestamp': datetime.now().isoformat()
        }

    def _create_risk_rejected_result(self, analysis_result: Dict[str, Any],
                                   risk_assessment: Dict[str, Any], pair_name: str) -> Dict[str, Any]:
        """إنشاء نتيجة رفض المخاطر"""
        return {
            'status': 'RISK_REJECTED',
            'pair': pair_name,
            'signal': analysis_result['signal'],
            'confidence': analysis_result['confidence'],
            'reason': f"Risk management rejection: {risk_assessment.get('reason', 'Unknown')}",
            'analysis_details': analysis_result,
            'risk_details': risk_assessment,
            'timestamp': datetime.now().isoformat()
        }

    def _create_validation_failed_result(self, analysis_result: Dict[str, Any],
                                       validation_result: Dict[str, Any], pair_name: str) -> Dict[str, Any]:
        """إنشاء نتيجة فشل التحقق"""
        return {
            'status': 'VALIDATION_FAILED',
            'pair': pair_name,
            'signal': analysis_result['signal'],
            'confidence': analysis_result['confidence'],
            'reason': f"Final validation failed: {validation_result.get('reason', 'Unknown')}",
            'analysis_details': analysis_result,
            'validation_details': validation_result,
            'timestamp': datetime.now().isoformat()
        }

    def _create_error_result(self, error_msg: str, pair_name: str) -> Dict[str, Any]:
        """إنشاء نتيجة خطأ"""
        return {
            'status': 'ERROR',
            'pair': pair_name,
            'signal': 'NEUTRAL',
            'confidence': 0,
            'error': error_msg,
            'timestamp': datetime.now().isoformat()
        }

    def get_system_summary(self) -> Dict[str, Any]:
        """الحصول على ملخص النظام"""
        try:
            uptime = datetime.now() - self.system_stats['system_uptime_start']

            return {
                'system': self.name,
                'version': self.version,
                'uptime_hours': round(uptime.total_seconds() / 3600, 2),
                'system_stats': self.system_stats.copy(),
                'risk_summary': self.risk_manager.get_risk_summary(),
                'strategy_performance': self.strategy.get_performance_summary(),
                'supported_pairs_count': len(self.system_settings['supported_pairs']),
                'analysis_history_size': len(self.analysis_history),
                'trade_history_size': len(self.trade_history),
                'last_update': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error getting system summary: {e}")
            return {'error': str(e)}

    def reset_daily_stats(self):
        """إعادة تعيين الإحصائيات اليومية"""
        try:
            self.risk_manager.reset_daily_stats()
            logger.info("Daily stats reset for integrated system")
        except Exception as e:
            logger.error(f"Error resetting daily stats: {e}")

    def enable_auto_trading(self):
        """تفعيل التداول الآلي"""
        self.system_settings['auto_trading_enabled'] = True
        logger.info("Auto trading enabled")

    def disable_auto_trading(self):
        """إلغاء تفعيل التداول الآلي"""
        self.system_settings['auto_trading_enabled'] = False
        logger.info("Auto trading disabled")

    def is_auto_trading_enabled(self) -> bool:
        """التحقق من حالة التداول الآلي"""
        return self.system_settings.get('auto_trading_enabled', False)
